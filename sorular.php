﻿<?php 
include("config.php"); 
$sayfa_no = 3;
include("header.php");

?>

<body>
    <div id="root">
	<?php  include("nav.php"); ?>

<main>
        <div class="container">
          <!-- Title and Top Buttons Start -->
		  <div class="page-title-container">
            <div class="row">
              <!-- Title Start -->
              <div class="col-12 col-md-7">
                <h1 class="mb-0 pb-0 display-4" id="title">Soru </h1>
				<a href="soru-listesi.php?yap=1"><button type="button" class="btn btn-outline-primary"><span><i class="bi-chevron-left"></i> Geri</span></button></a> 

              </div>
              <!-- Title End -->

              <div class="col-12 col-md-5 d-flex align-items-start justify-content-end">
                <!-- <a href="category.php?yap=1"><button type="button" class="btn btn-icon btn-icon-start btn-outline-primary mb-1"><span><i class="bi-plus"></i> <?php echo $texts["CategoryAdd"]; ?></span></button></a>
				<a href="category.php"><button type="button"  class="btn btn-icon btn-icon-start btn-outline-primary mb-1"><span><i class="bi-list"></i> <?php echo $texts["Categories"]; ?></span></button></a> -->
              </div>
            </div>
          </div>
		   
 
<section class="scroll-section" id="labelSize">


<table class="table table-striped" style="width:100%">
			<tr>
			<td><strong>Kategorisi </strong></td>
			<td>
				<select class="form-select" aria-label="Default select example">
					<option selected>Kategori Seçiniz</option>
					<option value="1">Çalışan Bağlılığı</option>
					<option value="2">Eğitim & Gelişim</option>
					<option value="3">Ik Stratejisi & Analitik</option>
				</select>

			</td>
 			 
			</tr>
 
			<tr>
			<td>Soru</td>
			<td colspan="2"><textarea type="text" class="form-control" id="exampleFormControlInput1">Çalışanlarınızın genel memnuniyet düzeyini nasıl değerlendirirsiniz?
</textarea></td>
 			</tr>

			<tr>
			<td>A şıkkı / Option 1</td>
			<td><input type="text" class="form-control" id="exampleFormControlInput1" placeholder="Yüksek memnuniyet düzeyi"></td>
			<td width="60%"><textarea type="text" class="textarea form-control" id="exampleFormControlInput1" style="height: 150px;">Yüksek çalışan memnuniyeti, çalışma ortamınızın, yönetim şeklinizin ve şirket kültürünüzün çalışan beklentileri ile uyumlu olduğunu gösterir. Bu başarıyı sürdürebilmek için, düzenli olarak çalışan memnuniyeti anketleri yapmaya devam edin. Ayrıca, çalışanlarınızla birebir görüşmeler yaparak mevcut memnuniyeti sağlayan unsurları analiz edin. Belirli aralıklarla geri bildirim toplayarak hangi alanların geliştirilmesi gerektiğini öğrenin. Çalışanların gelişimi için mentorluk ve eğitim programları oluşturmayı düşünebilirsiniz. Bu şekilde, memnuniyet seviyelerini koruyarak çalışan bağlılığını uzun vadede sürdürebilirsiniz.</textarea></td>

 			</tr>
			<tr>
			<td>B şıkkı / Option 2</td>
			<td><input type="text" class="form-control" id="exampleFormControlInput1" placeholder="Orta seviyede memnuniyet"></td>
			<td><textarea type="text" class="textarea form-control" id="exampleFormControlInput1" style="height: 150px;">Orta düzeyde memnuniyet, bazı alanların iyi olduğunu, ancak iyileştirilmesi gereken noktaların da olduğunu gösterir. İlk olarak, hangi alanlarda memnuniyetsizlik olduğunu anlamak için derinlemesine geri bildirim toplayın (örneğin, iş-yaşam dengesi, ücret politikaları, kariyer gelişimi). Bu geri bildirimlere dayanarak iyileştirme planları hazırlayın ve her çalışan için gelişim fırsatlarını belirleyin. Bir eylem planı oluşturup bu planın düzenli olarak takip edilmesini sağlayın. Belirli sorun alanlarına odaklanarak çalışan bağlılığını artıracak somut aksiyonlar alın (örneğin, eğitim programları, çalışma saatlerinde esneklik sağlama gibi).</textarea></td>

 			</tr>
			<tr>
			<td>C şıkkı / Option 3</td>
			<td><input type="text" class="form-control" id="exampleFormControlInput1" placeholder="Düşük memnuniyet	"></td>
			<td><textarea type="text" class="textarea form-control" id="exampleFormControlInput1" style="height: 150px;">Düşük çalışan memnuniyeti, ciddi bir sorun teşkil eder ve verimliliği, çalışan bağlılığını ve şirket imajını olumsuz etkileyebilir. İlk adım olarak, çalışan memnuniyetine dair anonim anketler yaparak şikayetlerin kaynaklarını tespit edin. Ardından, ortaya çıkan sorunlara yönelik çözüm stratejileri geliştirin; örneğin, çalışanların taleplerine uygun revizyonlar yaparak ve çalışanların kendilerini geliştirmelerine olanak tanıyan eğitim ve gelişim fırsatları sunarak. Yönetim kadrosuna da bu süreci daha iyi yönetmeleri için liderlik ve iletişim becerileri konusunda eğitim sağlayın. Tüm bu adımların sonuçlarını düzenli olarak takip edin ve gerektiğinde aksiyon planlarını güncelleyin.</textarea></td>
 			</tr>
			<tr>
			<td>D şıkkı / Option 4</td>
			<td><input type="text" class="form-control" id="exampleFormControlInput1" placeholder="Çalışan memnuniyetini ölçmüyoruz"></td>
			<td><textarea type="text" class="textarea form-control" id="exampleFormControlInput1" style="height: 150px;">Çalışan memnuniyetini ölçmemek, şirkette potansiyel sorunların fark edilmemesine neden olabilir. İlk olarak, çalışan memnuniyetini ölçen bir anket sistemi oluşturun. Bu anketi yıllık veya altı ayda bir düzenli olarak yapın. Toplanan geri bildirimleri analiz edin ve bu geri bildirimlere dayanarak iyileştirme planları oluşturun. Ayrıca, çalışanlarla birebir görüşmeler yaparak memnuniyetsizliklerin kök nedenlerini daha detaylı inceleyin. Geri bildirim süreçleri oluşturduğunuzda, çalışanların gelişimini destekleyici aksiyonlar alabilirsiniz (örneğin, kariyer planlama, ödüllendirme sistemi). Bu şekilde çalışan memnuniyetini sürekli takip edebilir ve stratejik kararlar alırken daha bilinçli hareket edebilirsiniz.</textarea></td>
 			</tr>
			<tr>

			<tr> 
			<td colspan="3" align="center"><button type="button" class="btn btn-primary">Kaydet</button></td>
			</tr>


			 
 
 
</table>



 
</section>


</div>
</main>

<? 
 
include "footer.php";
?> 

