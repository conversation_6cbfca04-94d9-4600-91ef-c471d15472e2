<?
if ($_COOKIE["site_login"] == "login_OK") {
header ("Location: main.php");
die();
} else {
$index_page=1;
include("config.php"); 
include "header.php";
?>  
 
 
  </head>

  <body class="h-100">
    <div id="root" class="h-100">
      <!-- Background Start -->
      <div class="fixed-background"></div>
      <!-- Background End -->

      <div class="container-fluid p-0 h-100 position-relative">
        <div class="row g-0 h-100">
          <!-- Left Side Start -->
          <div class="offset-0 col-12 d-none d-lg-flex offset-md-1 col-lg h-lg-100">
            <div class="min-h-100 d-flex align-items-center">
              <div class="w-200 w-lg-100 w-xxl-50">
                <div>
                  <div class="mb-5">
                     <h1 class="display-3 text-white"> </h1>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Left Side End -->

          <!-- Right Side Start -->
          <div class="col-12 col-lg-auto h-100 pb-4 px-4 pt-0 p-lg-0">
            <div class="sw-lg-70 min-h-100 bg-foreground d-flex justify-content-center align-items-center shadow-deep py-5 full-page-content-right-border">
              <div class="sw-lg-50 px-5">
                <div class="sh-11">
                  <a href="index.php">
                    <img src="img/logo1.png" width="250"> 
                  </a>
                </div>
                <div class="mb-4">
                  <h2 class="cta-1 mb-0 text-primary"><?php echo $texts["welcome"]; ?></h2> 
                </div>
                <div class="mb-5">
                  <p class="h6"><?php echo $texts["welcome1"]; ?></p>
                </div>
                <div>
				<?
          
          $hata = filter_var($_GET["hata"], FILTER_SANITIZE_SPECIAL_CHARS); 

          if ($hata==1) {
            echo "<br><div class=\"alert alert-danger\">$texts[err_1]</div><br>";
          } elseif ($hata==2) {
              echo "<br><div class=\"alert alert-danger\">$texts[err_2]</div><br>";		
          } elseif ($hata==3) {
              echo "<br><div class=\"alert alert-danger\">$texts[err_3]</div><br>";		
          }
        ?>

				<form action="login.php" method="POST">
                    <div class="mb-3 filled form-group tooltip-end-top">
                      <i data-cs-icon="user"></i>
                      <input class="form-control" placeholder="<?php echo $texts["Username"]; ?>" name="username" />
                    </div>
                    <div class="mb-3 filled form-group tooltip-end-top">
                      <i data-cs-icon="lock-off"></i>
                      <input class="form-control pe-7" name="password" type="password" placeholder="<?php echo $texts["password"]; ?>" />
                    </div>
                    <button type="submit" name="submit" class="btn btn-primary"><?php echo $texts["enter"]; ?></button>
                  </form>
                </div>
              </div>
            </div>
          </div>
          <!-- Right Side End -->
        </div>
      </div>
    </div>

	<?php
include "footer.php";
}
?>
 
  </body>
</html>
