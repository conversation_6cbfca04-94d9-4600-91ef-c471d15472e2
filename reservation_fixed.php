<?php
// Düzeltilmiş rezervasyon saati seçimi kodu

// Örnek translate array'i
$translate = [
    "rez_saati" => [
        "09:00" => "09:00",
        "10:00" => "10:00", 
        "11:00" => "11:00",
        "12:00" => "12:00",
        "13:00" => "13:00",
        "14:00" => "14:00",
        "15:00" => "15:00",
        "16:00" => "16:00",
        "17:00" => "17:00",
        "18:00" => "18:00"
    ]
];
?>

<!-- Düzeltilmiş kod -->
<select name="rez_saati" class="form-select">
    <option value="">Saat Seçiniz</option>
    @foreach ($translate["rez_saati"] as $key => $value)
        <option value="{{ $key }}">{{ $value }}</option>
    @endforeach    
</select>

<!-- Alternatif olarak PHP ile de yazılabilir -->
<select name="rez_saati_php" class="form-select">
    <option value="">Saat Seçiniz</option>
    <?php foreach ($translate["rez_saati"] as $key => $value): ?>
        <option value="<?= $key ?>"><?= $value ?></option>
    <?php endforeach; ?>
</select>
