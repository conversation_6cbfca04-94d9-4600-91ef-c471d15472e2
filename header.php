<?
//ini_set('default_charset', 'UTF-8');//
//header('Content-type : text/html; charset=utf-8');

//  ini_set('display_errors', 1);
//  ini_set('display_startup_errors', 1);
//   error_reporting(E_ALL);
   // error_reporting(0);
?>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <title>ARI Management Panel </title>
    <meta name="description" content="Login Page" />
    <!-- Favicon Tags Start -->
    <link rel="apple-touch-icon-precomposed" sizes="57x57" href="img/icon/favicon/apple-touch-icon-57x57.png" />
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="img/icon/favicon/apple-touch-icon-114x114.png" />
    <link rel="apple-touch-icon-precomposed" sizes="72x72" href="img/icon/favicon/apple-touch-icon-72x72.png" />
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="img/icon/favicon/apple-touch-icon-144x144.png" />
    <link rel="apple-touch-icon-precomposed" sizes="60x60" href="img/icon/favicon/apple-touch-icon-60x60.png" />
    <link rel="apple-touch-icon-precomposed" sizes="120x120" href="img/icon/favicon/apple-touch-icon-120x120.png" />
    <link rel="apple-touch-icon-precomposed" sizes="76x76" href="img/icon/favicon/apple-touch-icon-76x76.png" />
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="img/icon/favicon/apple-touch-icon-152x152.png" />
    <link rel="icon" type="image/png" href="img/icon/favicon-196x196.png" sizes="196x196" />
    <link rel="icon" type="image/png" href="img/icon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/png" href="img/icon/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="img/icon/favicon-16x16.png" sizes="16x16" />
    <link rel="icon" type="image/png" href="img/icon/favicon-128.png" sizes="128x128" />
	<link rel="shortcut icon" href="img/icon/favicon.ico" type="image/x-icon">
	<link rel="icon" href="img/icon/favicon.ico" type="image/x-icon">
    
	<meta name="application-name" content="&nbsp;" />
    <meta name="msapplication-TileColor" content="#FFFFFF" />
    <meta name="msapplication-TileImage" content="img/icon/favicon/mstile-144x144.png" />
    <meta name="msapplication-square70x70logo" content="img/icon/favicon/mstile-70x70.png" />
    <meta name="msapplication-square150x150logo" content="img/icon/favicon/mstile-150x150.png" />
    <meta name="msapplication-wide310x150logo" content="img/icon/favicon/mstile-310x150.png" />
    <meta name="msapplication-square310x310logo" content="img/icon/favicon/mstile-310x310.png" />

	<link rel="stylesheet" href="font/bootstrap-icons.css">
    <link rel="stylesheet" href="font/CS-Interface/style.css" />
    <link rel="stylesheet" href="css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="css/vendor/OverlayScrollbars.min.css" />
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/main.css" />
    <script src="js/base/loader.js"></script>

    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />


  </head>





  