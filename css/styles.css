@font-face {
  font-family: 'Plex-Light';
  src: url('../css/Barlow-Light.ttf');
}
@font-face {
  font-family: 'Plex-Regular';
  src: url('../css/Barlow-Medium.ttf');
}
@font-face {
  font-family: 'Plex-SemiBold';
  src: url('../css/Barlow-SemiBold.ttf');
}


:root {
  --sm: 576px;
  --md: 768px;
  --lg: 992px;
  --xl: 1200px;
  --xxl: 1400px; }

 

.soruback { background:#bbe0e2}

.cevapback { background:#d9f1f2 }


.h-100-card {
  height: calc(100% - var(--small-title-height)) !important; }

.w-100 {
  width: 100% !important; }

.w-90 {
  width: 90% !important; }

.w-80 {
  width: 80% !important; }

.w-75 {
  width: 75% !important; }

.w-70 {
  width: 70% !important; }

.w-60 {
  width: 60% !important; }

.w-50 {
  width: 50% !important; }

.w-40 {
  width: 40% !important; }

.w-33 {
  width: 33.3% !important; }

.w-30 {
  width: 30% !important; }

.w-25 {
  width: 25% !important; }

.w-20 {
  width: 20% !important; }

.w-10 {
  width: 10% !important; }

.h-auto {
  height: auto !important; }

.w-auto {
  width: auto !important; }

.sh-0 {
  height: 0 !important; }

.sw-0 {
  width: 0 !important; }

.sh-1 {
  height: 0.5rem !important; }

.sw-1 {
  width: 0.5rem !important; }

.sh-2 {
  height: 1rem !important; }

.sw-2 {
  width: 1rem !important; }

.sh-3 {
  height: 1.5rem !important; }

.sw-3 {
  width: 1.5rem !important; }

.sh-4 {
  height: 2rem !important; }

.sw-4 {
  width: 2rem !important; }
.sww-4 {
    width: 6rem !important; }
.sh-5 {
  height: 2.5rem !important; }

.sw-5 {
  width: 2.5rem !important; }

.sh-6 {
  height: 3rem !important; }

.sw-6 {
  width: 3rem !important; }

.sh-7 {
  height: 3.5rem !important; }

.sw-7 {
  width: 3.5rem !important; }

.sh-8 {
  height: 4rem !important; }

.sw-8 {
  width: 4rem !important; }

.sh-9 {
  height: 4.5rem !important; }

.sw-9 {
  width: 4.5rem !important; }

.sh-10 {
  height: 5rem !important; }

.sw-10 {
  width: 5rem !important; }

.sh-11 {
  height: 5.5rem !important; }

.sw-11 {
  width: 5.5rem !important; }

.sh-12 {
  height: 6rem !important; }

.sw-12 {
  width: 6rem !important; }

.sh-13 {
  height: 6.5rem !important; }

.sw-13 {
  width: 6.5rem !important; }

.sh-14 {
  height: 7rem !important; }

.sw-14 {
  width: 7rem !important; }

.sh-15 {
  height: 7.5rem !important; }

.sw-15 {
  width: 7.5rem !important; }

.sh-16 {
  height: 8rem !important; }

.sw-16 {
  width: 8rem !important; }

.sh-17 {
  height: 8.5rem !important; }
  .shh-17 {
    height: 15rem !important; }
  
.sw-17 {
  width: 8.5rem !important; }

.sh-18 {
  height: 9rem !important; }

.sw-18 {
  width: 9rem !important; }

.sh-19 {
  height: 9.5rem !important; }

.sw-19 {
  width: 9.5rem !important; }

.sh-20 {
  height: 10rem !important; }

.sw-20 {
  width: 10rem !important; }

.sh-21 {
  height: 10.5rem !important; }

.sw-21 {
  width: 10.5rem !important; }

.sh-22 {
  height: 11rem !important; }

.sw-22 {
  width: 11rem !important; }

.sh-23 {
  height: 11.5rem !important; }

.sw-23 {
  width: 11.5rem !important; }

.sh-24 {
  height: 12rem !important; }

.sw-24 {
  width: 12rem !important; }

.sh-25 {
  height: 12.5rem !important; }

.sw-25 {
  width: 12.5rem !important; }

.sh-30 {
  height: 15rem !important; }

.sw-30 {
  width: 15rem !important; }

.sh-35 {
  height: 17.5rem !important; }

.sw-35 {
  width: 17.5rem !important; }

.sh-40 {
  height: 20rem !important; }

.sw-40 {
  width: 20rem !important; }

.sh-45 {
  height: 22.5rem !important; }

.sw-45 {
  width: 22.5rem !important; }

.sh-50 {
  height: 25rem !important; }

.sw-50 {
  width: 25rem !important; }

.sh-60 {
  height: 30rem !important; }

.sw-60 {
  width: 30rem !important; }

.sh-70 {
  height: 35rem !important; }

.sw-70 {
  width: 35rem !important; }

.sh-80 {
  height: 40rem !important; }

.sw-80 {
  width: 40rem !important; }

@media (min-width: 576px) {
  .sh-sm-0 {
    height: 0 !important; } }

@media (min-width: 576px) {
  .sw-sm-0 {
    width: 0 !important; } }

@media (min-width: 576px) {
  .sh-sm-1 {
    height: 0.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-1 {
    width: 0.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-2 {
    height: 1rem !important; } }

@media (min-width: 576px) {
  .sw-sm-2 {
    width: 1rem !important; } }

@media (min-width: 576px) {
  .sh-sm-3 {
    height: 1.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-3 {
    width: 1.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-4 {
    height: 2rem !important; } }

@media (min-width: 576px) {
  .sw-sm-4 {
    width: 2rem !important; } }

@media (min-width: 576px) {
  .sh-sm-5 {
    height: 2.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-5 {
    width: 2.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-6 {
    height: 3rem !important; } }

@media (min-width: 576px) {
  .sw-sm-6 {
    width: 3rem !important; } }

@media (min-width: 576px) {
  .sh-sm-7 {
    height: 3.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-7 {
    width: 3.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-8 {
    height: 4rem !important; } }

@media (min-width: 576px) {
  .sw-sm-8 {
    width: 4rem !important; } }

@media (min-width: 576px) {
  .sh-sm-9 {
    height: 4.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-9 {
    width: 4.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-10 {
    height: 5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-10 {
    width: 5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-11 {
    height: 5.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-11 {
    width: 5.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-12 {
    height: 6rem !important; } }

@media (min-width: 576px) {
  .sw-sm-12 {
    width: 6rem !important; } }

@media (min-width: 576px) {
  .sh-sm-13 {
    height: 6.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-13 {
    width: 6.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-14 {
    height: 7rem !important; } }

@media (min-width: 576px) {
  .sw-sm-14 {
    width: 7rem !important; } }

@media (min-width: 576px) {
  .sh-sm-15 {
    height: 7.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-15 {
    width: 7.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-16 {
    height: 8rem !important; } }

@media (min-width: 576px) {
  .sw-sm-16 {
    width: 8rem !important; } }

@media (min-width: 576px) {
  .sh-sm-17 {
    height: 8.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-17 {
    width: 8.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-18 {
    height: 9rem !important; } }

@media (min-width: 576px) {
  .sw-sm-18 {
    width: 9rem !important; } }

@media (min-width: 576px) {
  .sh-sm-19 {
    height: 9.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-19 {
    width: 9.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-20 {
    height: 10rem !important; } }

@media (min-width: 576px) {
  .sw-sm-20 {
    width: 10rem !important; } }

@media (min-width: 576px) {
  .sh-sm-21 {
    height: 10.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-21 {
    width: 10.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-22 {
    height: 11rem !important; } }

@media (min-width: 576px) {
  .sw-sm-22 {
    width: 11rem !important; } }

@media (min-width: 576px) {
  .sh-sm-23 {
    height: 11.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-23 {
    width: 11.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-24 {
    height: 12rem !important; } }

@media (min-width: 576px) {
  .sw-sm-24 {
    width: 12rem !important; } }

@media (min-width: 576px) {
  .sh-sm-25 {
    height: 12.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-25 {
    width: 12.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-30 {
    height: 15rem !important; } }

@media (min-width: 576px) {
  .sw-sm-30 {
    width: 15rem !important; } }

@media (min-width: 576px) {
  .sh-sm-35 {
    height: 17.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-35 {
    width: 17.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-40 {
    height: 20rem !important; } }

@media (min-width: 576px) {
  .sw-sm-40 {
    width: 20rem !important; } }

@media (min-width: 576px) {
  .sh-sm-45 {
    height: 22.5rem !important; } }

@media (min-width: 576px) {
  .sw-sm-45 {
    width: 22.5rem !important; } }

@media (min-width: 576px) {
  .sh-sm-50 {
    height: 25rem !important; } }

@media (min-width: 576px) {
  .sw-sm-50 {
    width: 25rem !important; } }

@media (min-width: 576px) {
  .sh-sm-60 {
    height: 30rem !important; } }

@media (min-width: 576px) {
  .sw-sm-60 {
    width: 30rem !important; } }

@media (min-width: 576px) {
  .sh-sm-70 {
    height: 35rem !important; } }

@media (min-width: 576px) {
  .sw-sm-70 {
    width: 35rem !important; } }

@media (min-width: 576px) {
  .sh-sm-80 {
    height: 40rem !important; } }

@media (min-width: 576px) {
  .sw-sm-80 {
    width: 40rem !important; } }

@media (min-width: 768px) {
  .sh-md-0 {
    height: 0 !important; } }

@media (min-width: 768px) {
  .sw-md-0 {
    width: 0 !important; } }

@media (min-width: 768px) {
  .sh-md-1 {
    height: 0.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-1 {
    width: 0.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-2 {
    height: 1rem !important; } }

@media (min-width: 768px) {
  .sw-md-2 {
    width: 1rem !important; } }

@media (min-width: 768px) {
  .sh-md-3 {
    height: 1.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-3 {
    width: 1.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-4 {
    height: 2rem !important; } }

@media (min-width: 768px) {
  .sw-md-4 {
    width: 2rem !important; } }

@media (min-width: 768px) {
  .sh-md-5 {
    height: 2.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-5 {
    width: 2.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-6 {
    height: 3rem !important; } }

@media (min-width: 768px) {
  .sw-md-6 {
    width: 3rem !important; } }

@media (min-width: 768px) {
  .sh-md-7 {
    height: 3.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-7 {
    width: 3.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-8 {
    height: 4rem !important; } }

@media (min-width: 768px) {
  .sw-md-8 {
    width: 4rem !important; } }

@media (min-width: 768px) {
  .sh-md-9 {
    height: 4.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-9 {
    width: 4.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-10 {
    height: 5rem !important; } }

@media (min-width: 768px) {
  .sw-md-10 {
    width: 5rem !important; } }

@media (min-width: 768px) {
  .sh-md-11 {
    height: 5.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-11 {
    width: 5.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-12 {
    height: 6rem !important; } }

@media (min-width: 768px) {
  .sw-md-12 {
    width: 6rem !important; } }

@media (min-width: 768px) {
  .sh-md-13 {
    height: 6.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-13 {
    width: 6.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-14 {
    height: 7rem !important; } }

@media (min-width: 768px) {
  .sw-md-14 {
    width: 7rem !important; } }

@media (min-width: 768px) {
  .sh-md-15 {
    height: 7.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-15 {
    width: 7.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-16 {
    height: 8rem !important; } }

@media (min-width: 768px) {
  .sw-md-16 {
    width: 8rem !important; } }

@media (min-width: 768px) {
  .sh-md-17 {
    height: 8.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-17 {
    width: 8.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-18 {
    height: 9rem !important; } }

@media (min-width: 768px) {
  .sw-md-18 {
    width: 9rem !important; } }

@media (min-width: 768px) {
  .sh-md-19 {
    height: 9.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-19 {
    width: 9.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-20 {
    height: 10rem !important; } }

@media (min-width: 768px) {
  .sw-md-20 {
    width: 10rem !important; } }

@media (min-width: 768px) {
  .sh-md-21 {
    height: 10.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-21 {
    width: 10.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-22 {
    height: 11rem !important; } }

@media (min-width: 768px) {
  .sw-md-22 {
    width: 11rem !important; } }

@media (min-width: 768px) {
  .sh-md-23 {
    height: 11.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-23 {
    width: 11.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-24 {
    height: 12rem !important; } }

@media (min-width: 768px) {
  .sw-md-24 {
    width: 12rem !important; } }

@media (min-width: 768px) {
  .sh-md-25 {
    height: 12.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-25 {
    width: 12.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-30 {
    height: 15rem !important; } }

@media (min-width: 768px) {
  .sw-md-30 {
    width: 15rem !important; } }

@media (min-width: 768px) {
  .sh-md-35 {
    height: 17.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-35 {
    width: 17.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-40 {
    height: 20rem !important; } }

@media (min-width: 768px) {
  .sw-md-40 {
    width: 20rem !important; } }

@media (min-width: 768px) {
  .sh-md-45 {
    height: 22.5rem !important; } }

@media (min-width: 768px) {
  .sw-md-45 {
    width: 22.5rem !important; } }

@media (min-width: 768px) {
  .sh-md-50 {
    height: 25rem !important; } }

@media (min-width: 768px) {
  .sw-md-50 {
    width: 25rem !important; } }

@media (min-width: 768px) {
  .sh-md-60 {
    height: 30rem !important; } }

@media (min-width: 768px) {
  .sw-md-60 {
    width: 30rem !important; } }

@media (min-width: 768px) {
  .sh-md-70 {
    height: 35rem !important; } }

@media (min-width: 768px) {
  .sw-md-70 {
    width: 35rem !important; } }

@media (min-width: 768px) {
  .sh-md-80 {
    height: 40rem !important; } }

@media (min-width: 768px) {
  .sw-md-80 {
    width: 40rem !important; } }

@media (min-width: 992px) {
  .sh-lg-0 {
    height: 0 !important; } }

@media (min-width: 992px) {
  .sw-lg-0 {
    width: 0 !important; } }

@media (min-width: 992px) {
  .sh-lg-1 {
    height: 0.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-1 {
    width: 0.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-2 {
    height: 1rem !important; } }

@media (min-width: 992px) {
  .sw-lg-2 {
    width: 1rem !important; } }

@media (min-width: 992px) {
  .sh-lg-3 {
    height: 1.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-3 {
    width: 1.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-4 {
    height: 2rem !important; } }

@media (min-width: 992px) {
  .sw-lg-4 {
    width: 2rem !important; } }

@media (min-width: 992px) {
  .sh-lg-5 {
    height: 2.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-5 {
    width: 2.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-6 {
    height: 3rem !important; } }

@media (min-width: 992px) {
  .sw-lg-6 {
    width: 3rem !important; } }

@media (min-width: 992px) {
  .sh-lg-7 {
    height: 3.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-7 {
    width: 3.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-8 {
    height: 4rem !important; } }

@media (min-width: 992px) {
  .sw-lg-8 {
    width: 4rem !important; } }

@media (min-width: 992px) {
  .sh-lg-9 {
    height: 4.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-9 {
    width: 4.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-10 {
    height: 5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-10 {
    width: 5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-11 {
    height: 5.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-11 {
    width: 5.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-12 {
    height: 6rem !important; } }

@media (min-width: 992px) {
  .sw-lg-12 {
    width: 6rem !important; } }

@media (min-width: 992px) {
  .sh-lg-13 {
    height: 6.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-13 {
    width: 6.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-14 {
    height: 7rem !important; } }

@media (min-width: 992px) {
  .sw-lg-14 {
    width: 7rem !important; } }

@media (min-width: 992px) {
  .sh-lg-15 {
    height: 7.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-15 {
    width: 7.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-16 {
    height: 8rem !important; } }

@media (min-width: 992px) {
  .sw-lg-16 {
    width: 8rem !important; } }

@media (min-width: 992px) {
  .sh-lg-17 {
    height: 8.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-17 {
    width: 8.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-18 {
    height: 9rem !important; } }

@media (min-width: 992px) {
  .sw-lg-18 {
    width: 9rem !important; } }

@media (min-width: 992px) {
  .sh-lg-19 {
    height: 9.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-19 {
    width: 9.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-20 {
    height: 10rem !important; } }

@media (min-width: 992px) {
  .sw-lg-20 {
    width: 10rem !important; } }

@media (min-width: 992px) {
  .sh-lg-21 {
    height: 10.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-21 {
    width: 10.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-22 {
    height: 11rem !important; } }

@media (min-width: 992px) {
  .sw-lg-22 {
    width: 11rem !important; } }

@media (min-width: 992px) {
  .sh-lg-23 {
    height: 11.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-23 {
    width: 11.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-24 {
    height: 12rem !important; } }

@media (min-width: 992px) {
  .sw-lg-24 {
    width: 12rem !important; } }

@media (min-width: 992px) {
  .sh-lg-25 {
    height: 12.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-25 {
    width: 12.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-30 {
    height: 15rem !important; } }

@media (min-width: 992px) {
  .sw-lg-30 {
    width: 15rem !important; } }

@media (min-width: 992px) {
  .sh-lg-35 {
    height: 17.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-35 {
    width: 17.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-40 {
    height: 20rem !important; } }

@media (min-width: 992px) {
  .sw-lg-40 {
    width: 20rem !important; } }

@media (min-width: 992px) {
  .sh-lg-45 {
    height: 22.5rem !important; } }

@media (min-width: 992px) {
  .sw-lg-45 {
    width: 22.5rem !important; } }

@media (min-width: 992px) {
  .sh-lg-50 {
    height: 25rem !important; } }

@media (min-width: 992px) {
  .sw-lg-50 {
    width: 25rem !important; } }

@media (min-width: 992px) {
  .sh-lg-60 {
    height: 30rem !important; } }

@media (min-width: 992px) {
  .sw-lg-60 {
    width: 30rem !important; } }

@media (min-width: 992px) {
  .sh-lg-70 {
    height: 35rem !important; } }

@media (min-width: 992px) {
  .sw-lg-70 {
    width: 35rem !important; } }

@media (min-width: 992px) {
  .sh-lg-80 {
    height: 40rem !important; } }

@media (min-width: 992px) {
  .sw-lg-80 {
    width: 40rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-0 {
    height: 0 !important; } }

@media (min-width: 1200px) {
  .sw-xl-0 {
    width: 0 !important; } }

@media (min-width: 1200px) {
  .sh-xl-1 {
    height: 0.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-1 {
    width: 0.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-2 {
    height: 1rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-2 {
    width: 1rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-3 {
    height: 1.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-3 {
    width: 1.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-4 {
    height: 2rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-4 {
    width: 2rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-5 {
    height: 2.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-5 {
    width: 2.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-6 {
    height: 3rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-6 {
    width: 3rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-7 {
    height: 3.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-7 {
    width: 3.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-8 {
    height: 4rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-8 {
    width: 4rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-9 {
    height: 4.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-9 {
    width: 4.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-10 {
    height: 5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-10 {
    width: 5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-11 {
    height: 5.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-11 {
    width: 5.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-12 {
    height: 6rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-12 {
    width: 6rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-13 {
    height: 6.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-13 {
    width: 6.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-14 {
    height: 7rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-14 {
    width: 7rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-15 {
    height: 7.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-15 {
    width: 7.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-16 {
    height: 8rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-16 {
    width: 8rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-17 {
    height: 8.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-17 {
    width: 8.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-18 {
    height: 9rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-18 {
    width: 9rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-19 {
    height: 9.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-19 {
    width: 9.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-20 {
    height: 10rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-20 {
    width: 10rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-21 {
    height: 10.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-21 {
    width: 10.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-22 {
    height: 11rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-22 {
    width: 11rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-23 {
    height: 11.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-23 {
    width: 11.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-24 {
    height: 12rem !important; }
    .shh-xl-24 {
      height: 16rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-24 {
    width: 12rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-25 {
    height: 12.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-25 {
    width: 12.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-30 {
    height: 15rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-30 {
    width: 15rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-35 {
    height: 17.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-35 {
    width: 17.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-40 {
    height: 20rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-40 {
    width: 20rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-45 {
    height: 22.5rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-45 {
    width: 22.5rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-50 {
    height: 25rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-50 {
    width: 25rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-60 {
    height: 30rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-60 {
    width: 30rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-70 {
    height: 35rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-70 {
    width: 35rem !important; } }

@media (min-width: 1200px) {
  .sh-xl-80 {
    height: 40rem !important; } }

@media (min-width: 1200px) {
  .sw-xl-80 {
    width: 40rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-0 {
    height: 0 !important; } }

@media (min-width: 1400px) {
  .sw-xxl-0 {
    width: 0 !important; } }

@media (min-width: 1400px) {
  .sh-xxl-1 {
    height: 0.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-1 {
    width: 0.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-2 {
    height: 1rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-2 {
    width: 1rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-3 {
    height: 1.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-3 {
    width: 1.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-4 {
    height: 2rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-4 {
    width: 2rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-5 {
    height: 2.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-5 {
    width: 2.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-6 {
    height: 3rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-6 {
    width: 3rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-7 {
    height: 3.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-7 {
    width: 3.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-8 {
    height: 4rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-8 {
    width: 4rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-9 {
    height: 4.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-9 {
    width: 4.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-10 {
    height: 5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-10 {
    width: 5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-11 {
    height: 5.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-11 {
    width: 5.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-12 {
    height: 6rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-12 {
    width: 6rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-13 {
    height: 6.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-13 {
    width: 6.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-14 {
    height: 7rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-14 {
    width: 7rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-15 {
    height: 7.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-15 {
    width: 7.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-16 {
    height: 8rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-16 {
    width: 8rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-17 {
    height: 8.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-17 {
    width: 8.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-18 {
    height: 9rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-18 {
    width: 9rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-19 {
    height: 9.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-19 {
    width: 9.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-20 {
    height: 10rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-20 {
    width: 10rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-21 {
    height: 10.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-21 {
    width: 10.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-22 {
    height: 11rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-22 {
    width: 11rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-23 {
    height: 11.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-23 {
    width: 11.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-24 {
    height: 12rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-24 {
    width: 12rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-25 {
    height: 12.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-25 {
    width: 12.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-30 {
    height: 15rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-30 {
    width: 15rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-35 {
    height: 17.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-35 {
    width: 17.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-40 {
    height: 20rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-40 {
    width: 20rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-45 {
    height: 22.5rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-45 {
    width: 22.5rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-50 {
    height: 25rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-50 {
    width: 25rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-60 {
    height: 30rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-60 {
    width: 30rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-70 {
    height: 35rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-70 {
    width: 35rem !important; } }

@media (min-width: 1400px) {
  .sh-xxl-80 {
    height: 40rem !important; } }

@media (min-width: 1400px) {
  .sw-xxl-80 {
    width: 40rem !important; } }

@media (min-width: 576px) {
  .h-sm-100-card {
    height: calc(100% - var(--small-title-height)) !important; } }

@media (min-width: 768px) {
  .h-md-100-card {
    height: calc(100% - var(--small-title-height)) !important; } }

@media (min-width: 992px) {
  .h-lg-100-card {
    height: calc(100% - var(--small-title-height)) !important; } }

@media (min-width: 1200px) {
  .h-xl-100-card {
    height: calc(100% - var(--small-title-height)) !important; } }

@media (min-width: 1400px) {
  .h-xxl-100-card {
    height: calc(100% - var(--small-title-height)) !important; } }

@media (min-width: 576px) {
  .h-sm-100 {
    height: 100% !important; } }

@media (min-width: 768px) {
  .h-md-100 {
    height: 100% !important; } }

@media (min-width: 992px) {
  .h-lg-100 {
    height: 100% !important; } }

@media (min-width: 1200px) {
  .h-xl-100 {
    height: 100% !important; } }

@media (min-width: 1400px) {
  .h-xxl-100 {
    height: 100% !important; } }

@media (min-width: 576px) {
  .h-sm-auto {
    height: auto !important; } }

@media (min-width: 768px) {
  .h-md-auto {
    height: auto !important; } }

@media (min-width: 992px) {
  .h-lg-auto {
    height: auto !important; } }

@media (min-width: 1200px) {
  .h-xl-auto {
    height: auto !important; } }

@media (min-width: 1400px) {
  .h-xxl-auto {
    height: auto !important; } }

@media (min-width: 576px) {
  .w-sm-auto {
    width: auto !important; } }

@media (min-width: 768px) {
  .w-md-auto {
    width: auto !important; } }

@media (min-width: 992px) {
  .w-lg-auto {
    width: auto !important; } }

@media (min-width: 1200px) {
  .w-xl-auto {
    width: auto !important; } }

@media (min-width: 1400px) {
  .w-xxl-auto {
    width: auto !important; } }

@media (min-width: 576px) {
  .h-sm-25 {
    height: 25% !important; } }

@media (min-width: 576px) {
  .w-sm-25 {
    width: 25% !important; } }

@media (min-width: 576px) {
  .h-sm-50 {
    height: 50% !important; } }

@media (min-width: 576px) {
  .w-sm-50 {
    width: 50% !important; } }

@media (min-width: 576px) {
  .h-sm-75 {
    height: 75% !important; } }

@media (min-width: 576px) {
  .w-sm-75 {
    width: 75% !important; } }

@media (min-width: 576px) {
  .h-sm-100 {
    height: 100% !important; } }

@media (min-width: 576px) {
  .w-sm-100 {
    width: 100% !important; } }

@media (min-width: 768px) {
  .h-md-25 {
    height: 25% !important; } }

@media (min-width: 768px) {
  .w-md-25 {
    width: 25% !important; } }

@media (min-width: 768px) {
  .h-md-50 {
    height: 50% !important; } }

@media (min-width: 768px) {
  .w-md-50 {
    width: 50% !important; } }

@media (min-width: 768px) {
  .h-md-75 {
    height: 75% !important; } }

@media (min-width: 768px) {
  .w-md-75 {
    width: 75% !important; } }

@media (min-width: 768px) {
  .h-md-100 {
    height: 100% !important; } }

@media (min-width: 768px) {
  .w-md-100 {
    width: 100% !important; } }

@media (min-width: 992px) {
  .h-lg-25 {
    height: 25% !important; } }

@media (min-width: 992px) {
  .w-lg-25 {
    width: 25% !important; } }

@media (min-width: 992px) {
  .h-lg-50 {
    height: 50% !important; } }

@media (min-width: 992px) {
  .w-lg-50 {
    width: 50% !important; } }

@media (min-width: 992px) {
  .h-lg-75 {
    height: 75% !important; } }

@media (min-width: 992px) {
  .w-lg-75 {
    width: 75% !important; } }

@media (min-width: 992px) {
  .h-lg-100 {
    height: 100% !important; } }

@media (min-width: 992px) {
  .w-lg-100 {
    width: 100% !important; } }

@media (min-width: 1200px) {
  .h-xl-25 {
    height: 25% !important; } }

@media (min-width: 1200px) {
  .w-xl-25 {
    width: 25% !important; } }

@media (min-width: 1200px) {
  .h-xl-50 {
    height: 50% !important; } }

@media (min-width: 1200px) {
  .w-xl-50 {
    width: 50% !important; } }

@media (min-width: 1200px) {
  .h-xl-75 {
    height: 75% !important; } }

@media (min-width: 1200px) {
  .w-xl-75 {
    width: 75% !important; } }

@media (min-width: 1200px) {
  .h-xl-100 {
    height: 100% !important; } }

@media (min-width: 1200px) {
  .w-xl-100 {
    width: 100% !important; } }

@media (min-width: 1400px) {
  .h-xxl-25 {
    height: 25% !important; } }

@media (min-width: 1400px) {
  .w-xxl-25 {
    width: 25% !important; } }

@media (min-width: 1400px) {
  .h-xxl-50 {
    height: 50% !important; } }

@media (min-width: 1400px) {
  .w-xxl-50 {
    width: 50% !important; } }

@media (min-width: 1400px) {
  .h-xxl-75 {
    height: 75% !important; } }

@media (min-width: 1400px) {
  .w-xxl-75 {
    width: 75% !important; } }

@media (min-width: 1400px) {
  .h-xxl-100 {
    height: 100% !important; } }

@media (min-width: 1400px) {
  .w-xxl-100 {
    width: 100% !important; } }

.min-h-100 {
  min-height: 100%; }

.max-h-100 {
  max-height: 100%; }

.min-w-100 {
  min-width: 100%; }

.max-w-100 {
  max-width: 100%; }

.min-w-0 {
  min-width: 0; }

.min-h-0 {
  min-height: 0; }

/*
*
* Positions
*
* Top, bottom, left and right position utils.
*
*/
.t-0 {
  top: 0 !important;
  z-index: 1; }

.b-0 {
  bottom: 0 !important;
  z-index: 1; }

.s-0 {
  left: 0 !important;
  z-index: 1; }

.e-0 {
  right: 0 !important;
  z-index: 1; }

.t-n0 {
  top: 0 !important;
  z-index: 1; }

.b-n0 {
  bottom: 0 !important;
  z-index: 1; }

.s-n0 {
  left: 0 !important;
  z-index: 1; }

.e-n0 {
  right: 0 !important;
  z-index: 1; }

.t-1 {
  top: 0.25rem !important;
  z-index: 1; }

.b-1 {
  bottom: 0.25rem !important;
  z-index: 1; }

.s-1 {
  left: 0.25rem !important;
  z-index: 1; }

.e-1 {
  right: 0.25rem !important;
  z-index: 1; }

.t-n1 {
  top: -0.25rem !important;
  z-index: 1; }

.b-n1 {
  bottom: -0.25rem !important;
  z-index: 1; }

.s-n1 {
  left: -0.25rem !important;
  z-index: 1; }

.e-n1 {
  right: -0.25rem !important;
  z-index: 1; }

.t-2 {
  top: 0.5rem !important;
  z-index: 1; }

.b-2 {
  bottom: 0.5rem !important;
  z-index: 1; }

.s-2 {
  left: 0.5rem !important;
  z-index: 1; }

.e-2 {
  right: 0.5rem !important;
  z-index: 1; }

.t-n2 {
  top: -0.5rem !important;
  z-index: 1; }

.b-n2 {
  bottom: -0.5rem !important;
  z-index: 1; }

.s-n2 {
  left: -0.5rem !important;
  z-index: 1; }

.e-n2 {
  right: -0.5rem !important;
  z-index: 1; }

.t-3 {
  top: 1rem !important;
  z-index: 1; }

.b-3 {
  bottom: 1rem !important;
  z-index: 1; }

.s-3 {
  left: 1rem !important;
  z-index: 1; }

.e-3 {
  right: 1rem !important;
  z-index: 1; }

.t-n3 {
  top: -1rem !important;
  z-index: 1; }

.b-n3 {
  bottom: -1rem !important;
  z-index: 1; }

.s-n3 {
  left: -1rem !important;
  z-index: 1; }

.e-n3 {
  right: -1rem !important;
  z-index: 1; }

.t-4 {
  top: 1.5rem !important;
  z-index: 1; }

.b-4 {
  bottom: 1.5rem !important;
  z-index: 1; }

.s-4 {
  left: 1.5rem !important;
  z-index: 1; }

.e-4 {
  right: 1.5rem !important;
  z-index: 1; }

.t-n4 {
  top: -1.5rem !important;
  z-index: 1; }

.b-n4 {
  bottom: -1.5rem !important;
  z-index: 1; }

.s-n4 {
  left: -1.5rem !important;
  z-index: 1; }

.e-n4 {
  right: -1.5rem !important;
  z-index: 1; }

.t-5 {
  top: 2rem !important;
  z-index: 1; }

.b-5 {
  bottom: 2rem !important;
  z-index: 1; }

.s-5 {
  left: 2rem !important;
  z-index: 1; }

.e-5 {
  right: 2rem !important;
  z-index: 1; }

.t-n5 {
  top: -2rem !important;
  z-index: 1; }

.b-n5 {
  bottom: -2rem !important;
  z-index: 1; }

.s-n5 {
  left: -2rem !important;
  z-index: 1; }

.e-n5 {
  right: -2rem !important;
  z-index: 1; }

/*
*
* Shared
*
* Styles for anchors, opacity, backgrounds and so on.
*
*/
a {
  transition: color var(--transition-time-short);
  text-decoration: initial;
  color: var(--primary);
  outline: initial !important; }
  a:hover, a:active {
    text-decoration: initial;
    color: var(--secondary); }

a.body {
  color: var(--body); }
  a.body:hover, a.body:active {
    color: var(--primary); }

.body-link {
  color: var(--body); }
  .body-link:hover {
    color: var(--primary); }

.alternate-link {
  color: var(--alternate); }
  .alternate-link:hover {
    color: var(--primary); }

.muted-link {
  color: var(--muted); }
  .muted-link:hover {
    color: var(--primary); }

.underline-link {
  text-decoration: underline; }

.underline-hover-link:hover {
  text-decoration: underline; }

img {
  object-fit: cover; }

.absolute-center {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%); }

.absolute-center-vertical {
  top: 50%;
  transform: translateY(-50%); }

.absolute-center-horizontal {
  left: 50%;
  transform: translateX(-50%); }

.opacity-0 {
  opacity: 0; }

.opacity-10 {
  opacity: 0.1; }

.opacity-25 {
  opacity: 0.25; }

.opacity-50 {
  opacity: 0.5; }

.opacity-75 {
  opacity: 0.75; }

.opacity-100 {
  opacity: 1; }

.grayscale {
  filter: grayscale(1); }

.img-fluid-width {
  max-width: 100%;
  height: auto; }

.img-fluid-height {
  max-height: 100%;
  width: auto; }

.shadow {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important; }

.shadow-deep {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1) !important; }

.bg-cover-center {
  background-size: cover;
  background-position: center; }

.z-index-1 {
  z-index: 1; }

.z-index-1000 {
  z-index: 1000; }

.basic-transform-transition {
  -webkit-transition: all 0.1s ease;
  transition: all 0.1s ease;
  pointer-events: none;
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0); }

.fixed-background {
  background: url("../img/background/background-blue.jpg") no-repeat center center fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0; }

.hover-trigger:hover .visible-hover {
  visibility: visible !important; }

.cursor-default {
  cursor: default; }

.cursor-pointer {
  cursor: pointer; }

.theme-filter {
  filter: var(--theme-image-filter); }

.flex-grow-1 {
  flex: 1; }

/*
*
* Shared
*
* Rest of the theme variables.
*
*/
:root {
  --transition-time: 400ms;
  --transition-time-long: 1000ms;
  --transition-time-short: 200ms;
  --nav-size-slim: 5rem;
  --nav-size: 18rem;
  --footer-size: 4.5rem;
  --input-height: 2.25rem;
  --small-title-height: 2rem;
  --font: 'Plex-Regular', sans-serif;
  --font-heading: 'Plex-Regular', sans-serif; }

html[data-radius='rounded'] {
  --border-radius-lg: 16px;
  --border-radius-md: 10px;
  --border-radius-sm: 6px;
  --border-radius-xl: 50px; }

html[data-radius='standard'] {
  --border-radius-lg: 6px;
  --border-radius-md: 4px;
  --border-radius-sm: 3px;
  --border-radius-xl: 4px; }

html[data-radius='flat'] {
  --border-radius-lg: 0;
  --border-radius-sm: 0;
  --border-radius-md: 0;
  --border-radius-xl: 0; }

:root {
  --card-spacing: 1.2rem;
  --card-spacing-sm: 1.25rem;
  --card-spacing-xs: 0.75rem;
  --card-spacing-sm-horizontal: 1.75rem 1.25rem;
  --card-spacing-sm-vertical: 1.25rem 1.75rem;
  --main-spacing-horizontal: 2.5rem;
  --main-spacing-vertical: 2rem;
  --title-spacing: 0.25rem; }
  @media (max-width: 1199.98px) {
    :root {
      --main-spacing-horizontal: 2rem; } }
  @media (max-width: 991.98px) {
    :root {
      --nav-size-slim: 4rem;
      --title-spacing: 1rem;
      --footer-size: 4rem; } }
  @media (max-width: 767.98px) {
    :root {
      --card-spacing: 1.75rem;
      --footer-size: 3rem;
      --main-spacing-horizontal: 1.25rem;
      --main-spacing-vertical: 1.25rem; } }

/*
*
* Dark Blue
*
* Theme variables.
*
*/  
html[data-color='light-blue'] {
  --primary: #2499e3;
  --secondary: #50c6db;
  --tertiary: #1859bb;
  --quaternary: #2a2c7c;
  --primary-rgb: 36, 153, 227;
  --secondary-rgb: 48, 198, 220;
  --tertiary-rgb: 24, 89, 187;
  --quaternary-rgb: 42, 44, 124;
  --primary-darker: #1d7ab4;
  --secondary-darker: #409faf;
  --tertiary-darker: #124188;
  --quaternary-darker: #1a1a4d;
  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;
  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;
  --background-theme: #eaf0f1;
  --background-light: #f8f8f8;
  --gradient-1: #000000;
  --gradient-2: #3d5b90;
  --gradient-3: #000000;
  --gradient-1-darker: #1e7cb6;
  --gradient-2-darker: #2d9fcf;
  --gradient-3-darker: #59b4c4;
  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;
  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;
  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;
  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;
  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.07);
  --background-navcolor-light: #fff;
  --background-navcolor-dark: #253a52;
  --theme-image-filter: hue-rotate(0deg); }

/*
*
* Light Green
*
* Theme variables.
*
*/
html[data-color='light-green'] {
  --primary: #16a34f;
  --secondary: #41c119;
  --tertiary: #007e63;
  --quaternary: #085327;
  --primary-rgb: 22, 163, 79;
  --secondary-rgb: 68, 189, 12;
  --tertiary-rgb: 0, 126, 99;
  --quaternary-rgb: 8, 83, 39;
  --primary-darker: #127a3c;
  --secondary-darker: #349b14;
  --tertiary-darker: #014b3b;
  --quaternary-darker: #053118;
  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;
  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;
  --background-theme: #eaf1ec;
  --background-light: #f8f8f8;
  --gradient-1: #16923b;
  --gradient-2: #32af5b;
  --gradient-3: #42ca6f;
  --gradient-1-darker: #137c3d;
  --gradient-2-darker: #2c924e;
  --gradient-3-darker: #489b3d;
  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;
  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;
  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;
  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;
  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-light: 0px 3px 10px rgba(0, 0, 0, 0.07);
  --background-navcolor-light: #fff;
  --background-navcolor-dark: #24352b;
  --theme-image-filter: hue-rotate(283deg) contrast(0.9); }

/*
*
* Light Red
*
* Theme variables.
*
*/
html[data-color='light-red'] {
  --primary: #df3951;
  --secondary: #fa684e;
  --tertiary: #e03e22;
  --quaternary: #942020;
  --primary-rgb: 223, 57, 81;
  --secondary-rgb: 250, 104, 78;
  --tertiary-rgb: 224, 62, 34;
  --quaternary-rgb: 148, 32, 32;
  --primary-darker: #b42e42;
  --secondary-darker: #c4533f;
  --tertiary-darker: #a72e19;
  --quaternary-darker: #571313;
  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;
  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;
  --background-theme: #f1ecea;
  --background-light: #f8f8f8;
  --gradient-1: #d0384e;
  --gradient-2: #dd484a;
  --gradient-3: #f15254;
  --gradient-1-darker: #b63245;
  --gradient-2-darker: #c23f42;
  --gradient-3-darker: #ce6041;
  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;
  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;
  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;
  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;
  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.07);
  --background-navcolor-light: #fff;
  --background-navcolor-dark: #3b2b2f;
  --theme-image-filter: hue-rotate(150deg) contrast(1.2); }

/*
*
* Light Purple
*
* Theme variables.
*
*/
html[data-color='light-purple'] {
  --primary: #9653cd;
  --secondary: #ba7feb;
  --tertiary: #922692;
  --quaternary: #5d2872;
  --primary-rgb: 150, 83, 205;
  --secondary-rgb: 186, 127, 235;
  --tertiary-rgb: 146, 38, 146;
  --quaternary-rgb: 93, 40, 114;
  --primary-darker: #7541a0;
  --secondary-darker: #9868c0;
  --tertiary-darker: #691c69;
  --quaternary-darker: #391846;
  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;
  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;
  --background-theme: #efeaf1;
  --background-light: #f8f8f8;
  --gradient-1: #8755b0;
  --gradient-2: #a86dd8;
  --gradient-3: #b170e5;
  --gradient-1-darker: #714794;
  --gradient-2-darker: #915dbb;
  --gradient-3-darker: #b272d4;
  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;
  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;
  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;
  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;
  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.07);
  --background-navcolor-light: #fff;
  --background-navcolor-dark: #3d3246;
  --theme-image-filter: hue-rotate(64deg) contrast(0.9); }

/*
*
* Light Pink
*
* Theme variables.
*
*/
html[data-color='light-pink'] {
  --primary: #e54291;
  --secondary: #ff84bf;
  --tertiary: #b42448;
  --quaternary: #751a46;
  --primary-rgb: 229, 66, 145;
  --secondary-rgb: 255, 132, 191;
  --tertiary-rgb: 180, 36, 72;
  --quaternary-rgb: 131, 34, 81;
  --primary-darker: #be3779;
  --secondary-darker: #ce6899;
  --tertiary-darker: #7e1932;
  --quaternary-darker: #531332;
  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;
  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;
  --background-theme: #f1eaee;
  --background-light: #f8f8f8;
  --gradient-1: #d64c8f;
  --gradient-2: #e55b9e;
  --gradient-3: #ef75b0;
  --gradient-1-darker: #b9437c;
  --gradient-2-darker: #d35291;
  --gradient-3-darker: #d8849b;
  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;
  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;
  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;
  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;
  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.07);
  --background-navcolor-light: #fff;
  --background-navcolor-dark: #462e3a;
  --theme-image-filter: hue-rotate(130deg) contrast(0.9); }

/*
*
* Accordion
*
* Bootstrap accordion styles.
*
*/
.accordion-button {
  border-color: var(--separator);
  color: var(--body);
  font-size: 1em; }

.accordion-button:not(.collapsed) {
  color: var(--primary);
  background: initial; }

.accordion-button:focus {
  border-color: var(--separator); }

.accordion-collapse {
  border-color: var(--separator); }

.accordion-button::after {
  background: initial !important;
  font-family: 'Plex-Regular' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: '\e915';
  text-align: center;
  transform-origin: center; }

.accordion-item {
  border: 1px solid var(--separator); }

.accordion-item,
.accordion-button {
  background: initial; }

.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md); }

/*
*
* Alert
*
* Bootstrap alert styles.
*
*/
.alert {
  border: initial;
  border-radius: var(--border-radius-md); }

.alert-primary {
  color: var(--primary);
  background-color: rgba(var(--primary-rgb), 0.15); }
  .alert-primary .alert-link {
    color: var(--primary); }

.alert-secondary {
  color: var(--secondary);
  background-color: rgba(var(--secondary-rgb), 0.15); }
  .alert-secondary .alert-link {
    color: var(--secondary); }

.alert-tertiary {
  color: var(--tertiary);
  background-color: rgba(var(--tertiary-rgb), 0.15); }
  .alert-tertiary .alert-link {
    color: var(--tertiary); }

.alert-quaternary {
  color: var(--quaternary);
  background-color: rgba(var(--quaternary-rgb), 0.15); }
  .alert-quaternary .alert-link {
    color: var(--quaternary); }

.alert-success {
  color: var(--success);
  background-color: rgba(var(--success-rgb), 0.15); }
  .alert-success .alert-link {
    color: var(--success); }

.alert-danger {
  color: var(--danger);
  background-color: rgba(var(--danger-rgb), 0.15); }
  .alert-danger .alert-link {
    color: var(--danger); }

.alert-info {
  color: var(--info);
  background-color: rgba(var(--info-rgb), 0.15); }
  .alert-info .alert-link {
    color: var(--info); }

.alert-warning {
  color: var(--warning);
  background-color: rgba(var(--warning-rgb), 0.15); }
  .alert-warning .alert-link {
    color: var(--warning); }

.alert-light {
  color: var(--alternate);
  background-color: rgba(var(--dark-rgb), 0.05); }
  .alert-light .alert-link {
    color: var(--alternate); }

.alert-dark {
  color: var(--alternate);
  background-color: rgba(var(--dark-rgb), 0.15); }
  .alert-dark .alert-link {
    color: var(--alternate); }

.alert-heading {
  color: inherit !important; }

.alert-dismissible .btn-close {
  top: 3px;
  right: 3px;
  padding: 0.5rem; }

/*
*
* Background
*
* Bootstrap background utilities and additional ones.
*
*/
.bg-primary {
  background-color: var(--primary) !important; }

.bg-secondary {
  background-color: var(--secondary) !important; }

.bg-tertiary {
  color: var(--light-text) !important;
  background-color: var(--tertiary) !important; }

.bg-quaternary {
  color: var(--light-text) !important;
  background-color: var(--quaternary) !important; }

.bg-success {
  background-color: var(--success) !important;
  color: var(--light-text) !important; }

.bg-danger {
  background-color: var(--danger) !important;
  color: var(--light-text) !important; }

.bg-warning {
  background-color: var(--warning) !important;
  color: var(--light-text) !important; }

.bg-info {
  background-color: var(--info) !important;
  color: var(--light-text) !important; }

.bg-light {
  background-color: var(--background-light) !important;
  color: var(--dark-text) !important; }

.bg-dark {
  background-color: var(--dark) !important;
  color: var(--light-text) !important; }

.bg-separator {
  background-color: var(--separator) !important;
  color: var(--light-text) !important; }

.bg-separator-light {
  background-color: var(--separator-light) !important;
  color: var(--light-text) !important; }

.bg-theme {
  background-color: var(--background-theme) !important; }

.bg-muted {
  background-color: var(--muted) !important;
  color: var(--light-text) !important; }

.bg-alternate {
  background-color: var(--alternate) !important; }

.bg-body {
  background-color: var(--body) !important; }

.bg-foreground {
  background-color: var(--foreground) !important;
  color: var(--primary) !important; }

.bg-background {
  background-color: var(--background) !important;
  color: var(--primary) !important; }

.bg-outline-primary {
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  color: var(--primary) !important; }

.bg-outline-secondary {
  box-shadow: inset 0 0 0 1px var(--secondary) !important;
  color: var(--secondary) !important; }

.bg-outline-tertiary {
  box-shadow: inset 0 0 0 1px var(--tertiary) !important;
  color: var(--tertiary) !important; }

.bg-outline-quaternary {
  box-shadow: inset 0 0 0 1px var(--quaternary) !important;
  color: var(--quaternary) !important; }

.bg-outline-success {
  box-shadow: inset 0 0 0 1px var(--success) !important;
  color: var(--success) !important; }

.bg-outline-danger {
  box-shadow: inset 0 0 0 1px var(--danger) !important;
  color: var(--danger) !important; }

.bg-outline-warning {
  box-shadow: inset 0 0 0 1px var(--warning) !important;
  color: var(--warning) !important; }

.bg-outline-info {
  box-shadow: inset 0 0 0 1px var(--info) !important;
  color: var(--info) !important; }

.bg-outline-light {
  box-shadow: inset 0 0 0 1px var(--light) !important;
  color: var(--light) !important; }

.bg-outline-dark {
  box-shadow: inset 0 0 0 1px var(--dark) !important;
  color: var(--dark) !important; }

.bg-outline-muted {
  box-shadow: inset 0 0 0 1px var(--muted) !important;
  color: var(--muted) !important; }

.bg-outline-body {
  box-shadow: inset 0 0 0 1px var(--body) !important;
  color: var(--body) !important; }

.bg-outline-alternate {
  box-shadow: inset 0 0 0 1px var(--alternate) !important;
  color: var(--alternate) !important; }

.bg-outline-separator {
  box-shadow: inset 0 0 0 1px var(--separator) !important;
  color: var(--separator) !important; }

.bg-outline-foreground {
  box-shadow: inset 0 0 0 1px var(--foreground) !important;
  color: var(--primary) !important; }

.bg-outline-background {
  box-shadow: inset 0 0 0 1px var(--background) !important;
  color: var(--primary) !important; }

.bg-gradient-single-1 {
  background-color: var(--gradient-1) !important; }

.bg-gradient-single-2 {
  background-color: var(--gradient-2) !important; }

.bg-gradient-single-3 {
  background-color: var(--gradient-3) !important; }

.bg-gradient-1 {
  background-image: linear-gradient(160deg, var(--gradient-1), var(--gradient-1), var(--gradient-1-darker)) !important; }

.bg-gradient-2 {
  background-image: linear-gradient(160deg, var(--gradient-2), var(--gradient-2), var(--gradient-2-darker)) !important; }

.bg-gradient-3 {
  background-image: linear-gradient(160deg, var(--gradient-3), var(--gradient-3), var(--gradient-3-darker)) !important; }

.bg-gradient-primary {
  background-image: linear-gradient(160deg, var(--primary), var(--primary), var(--secondary)) !important; }

.bg-vertical-ornament-1 {
  background: url(../img/product/vertical/vertical-ornament-1.png);
  background-size: cover; }

.bg-vertical-ornament-2 {
  background: url(../img/product/vertical/vertical-ornament-2.png);
  background-size: cover; }

.bg-vertical-ornament-3 {
  background: url(../img/product/vertical/vertical-ornament-3.png);
  background-size: cover; }

.bg-vertical-ornament-4 {
  background: url(../img/product/vertical/vertical-ornament-4.png);
  background-size: cover; }

a:focus.bg-primary,
a:hover.bg-primary {
  background-color: var(--primary-darker) !important;
  border-color: var(--primary-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-secondary,
a:hover.bg-secondary {
  background-color: var(--secondary-darker) !important;
  border-color: var(--secondary-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-tertiary,
a:hover.bg-tertiary {
  background-color: var(--tertiary-darker) !important;
  border-color: var(--tertiary-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-quaternary,
a:hover.bg-quaternary {
  background-color: var(--quaternary-darker) !important;
  border-color: var(--quaternary-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-warning,
a:hover.bg-warning {
  background-color: var(--warning-darker) !important;
  border-color: var(--warning-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-danger,
a:hover.bg-danger {
  background-color: var(--danger-darker) !important;
  border-color: var(--danger-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-success,
a:hover.bg-success {
  background-color: var(--success-darker) !important;
  border-color: var(--success-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-info,
a:hover.bg-info {
  background-color: var(--info-darker) !important;
  border-color: var(--info-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-light,
a:hover.bg-light {
  background-color: var(--light-darker) !important;
  border-color: var(--light-darker) !important;
  color: var(--dark-text) !important; }

a:focus.bg-dark,
a:hover.bg-dark {
  background-color: var(--dark-darker) !important;
  border-color: var(--dark-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-muted,
a:hover.bg-muted {
  background-color: var(--muted-darker) !important;
  border-color: var(--muted-darker) !important;
  color: var(--light-text) !important; }

a:focus.bg-separator,
a:hover.bg-separator {
  background-color: var(--separator-darker) !important;
  border-color: var(--separator-darker) !important;
  color: var(--light-text) !important; }

/*
*
* Badge
*
* Bootstrap badge styles.
*
*/
.badge {
  padding: 0.65em 1.1em;
  font-size: 0.9em;
  font-weight: 400;
  border-radius: var(--border-radius-md);
  color: var(--light-text);
  text-indent: -1px;
  line-height: 1.25; }

.btn .badge {
  padding-top: 1px;
  padding-bottom: 1px; }

a.badge,
.btn.badge {
  box-shadow: initial !important; }

.badge {
  background: unset;
  border: initial !important; }

/*
*
* Border
*
* Bootstrap border utilities and additional ones.
*
*/
.rounded-top {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-top-right-radius: var(--border-radius-lg) !important; }

.rounded-top-start {
  border-top-left-radius: var(--border-radius-lg) !important; }

.rounded-top-end {
  border-top-right-radius: var(--border-radius-lg) !important; }

.rounded-end {
  border-top-right-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important; }

.rounded-bottom {
  border-bottom-right-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-bottom-start {
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-bottom-end {
  border-bottom-right-radius: var(--border-radius-lg) !important; }

.rounded-start {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-pill {
  border-radius: 50px !important; }

.rounded,
.rounded-lg {
  border-radius: var(--border-radius-lg) !important; }

.rounded-xl {
  border-radius: var(--border-radius-xl) !important; }

.rounded-xl-top {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-top-right-radius: var(--border-radius-lg) !important; }

.rounded-xl-top-start {
  border-top-left-radius: var(--border-radius-lg) !important; }

.rounded-xl-top-end {
  border-top-right-radius: var(--border-radius-lg) !important; }

.rounded-xl-end {
  border-top-right-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important; }

.rounded-xl-bottom {
  border-bottom-right-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-xl-bottom-start {
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-xl-bottom-end {
  border-bottom-right-radius: var(--border-radius-lg) !important; }

.rounded-xl-start {
  border-top-left-radius: var(--border-radius-lg) !important;
  border-bottom-left-radius: var(--border-radius-lg) !important; }

.rounded-sm {
  border-radius: var(--border-radius-sm) !important; }

.rounded-sm-top {
  border-top-left-radius: var(--border-radius-sm) !important;
  border-top-right-radius: var(--border-radius-sm) !important; }

.rounded-sm-top-start {
  border-top-left-radius: var(--border-radius-sm) !important; }

.rounded-sm-top-end {
  border-top-right-radius: var(--border-radius-sm) !important; }

.rounded-sm-end {
  border-top-right-radius: var(--border-radius-sm) !important;
  border-bottom-right-radius: var(--border-radius-sm) !important; }

.rounded-sm-bottom {
  border-bottom-right-radius: var(--border-radius-sm) !important;
  border-bottom-left-radius: var(--border-radius-sm) !important; }

.rounded-sm-bottom-start {
  border-bottom-left-radius: var(--border-radius-sm) !important; }

.rounded-sm-bottom-end {
  border-bottom-right-radius: var(--border-radius-sm) !important; }

.rounded-sm-start {
  border-top-left-radius: var(--border-radius-sm) !important;
  border-bottom-left-radius: var(--border-radius-sm) !important; }

.rounded-md {
  border-radius: var(--border-radius-md) !important; }

.rounded-md-top {
  border-top-left-radius: var(--border-radius-md) !important;
  border-top-right-radius: var(--border-radius-md) !important; }

.rounded-md-top-start {
  border-top-left-radius: var(--border-radius-md) !important; }

.rounded-md-top-end {
  border-top-right-radius: var(--border-radius-md) !important; }

.rounded-md-end {
  border-top-right-radius: var(--border-radius-md) !important;
  border-bottom-right-radius: var(--border-radius-md) !important; }

.rounded-md-bottom {
  border-bottom-right-radius: var(--border-radius-md) !important;
  border-bottom-left-radius: var(--border-radius-md) !important; }

.rounded-md-bottom-start {
  border-bottom-left-radius: var(--border-radius-md) !important; }

.rounded-md-bottom-end {
  border-bottom-right-radius: var(--border-radius-md) !important; }

.rounded-md-start {
  border-top-left-radius: var(--border-radius-md) !important;
  border-bottom-left-radius: var(--border-radius-md) !important; }

/* Colors */
.border {
  border-color: var(--separator) !important; }

.border-bottom {
  border-bottom: 1px solid var(--separator) !important; }

.border-top {
  border-top: 1px solid var(--separator) !important; }

.border-start {
  border-left: 1px solid var(--separator) !important; }

.border-end {
  border-right: 1px solid var(--separator) !important; }

.border-primary {
  border-color: var(--primary) !important; }

.border-secondary {
  border-color: var(--secondary) !important; }

.border-tertiary {
  border-color: var(--tertiary) !important; }

.border-quaternary {
  border-color: var(--quaternary) !important; }

.border-separator {
  border-color: var(--separator) !important; }

.border-separator-light {
  border-color: var(--separator-light) !important; }

.border-muted {
  border-color: var(--muted) !important; }

.border-alternate {
  border-color: var(--alternate) !important; }

.border-body {
  border-color: var(--body) !important; }

.border-success {
  border-color: var(--success) !important; }

.border-danger {
  border-color: var(--danger) !important; }

.border-warning {
  border-color: var(--warning) !important; }

.border-info {
  border-color: var(--info) !important; }

.border-light {
  border-color: var(--light) !important; }

.border-dark {
  border-color: var(--dark) !important; }

.border-white {
  border-color: var(--light-text) !important; }

.border-foreground {
  border-color: var(--foreground) !important; }

.border-background {
  border-color: var(--background) !important; }

.border-transparent {
  border-color: transparent !important; }

.border-1 {
  border-width: 1px !important; }

.border-2 {
  border-width: 2px !important; }

.border-3 {
  border-width: 3px !important; }

.border-4 {
  border-width: 4px !important; }

.border-5 {
  border-width: 5px !important; }

.border-dashed {
  border-style: dashed !important; }

.border-last-none > *:last-child {
  border: initial !important; }

.separator {
  border-bottom: 1px solid var(--separator); }

.separator-light {
  border-bottom: 1px solid var(--separator-light); }

.full-page-content-right-border {
  border-radius: initial !important;
  border-bottom-left-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important; }
  @media (min-width: 992px) {
    .full-page-content-right-border {
      border-radius: initial !important;
      border-bottom-left-radius: var(--border-radius-lg) !important;
      border-top-left-radius: var(--border-radius-lg) !important; } }

.full-page-content-single-border {
  border-radius: initial !important;
  border-bottom-left-radius: var(--border-radius-lg) !important;
  border-bottom-right-radius: var(--border-radius-lg) !important; }

/*
*
* Button
*
* Bootstrap button styles.
*
*/
button {
  outline: initial !important;
  box-shadow: initial !important;
  text-decoration: initial !important; }

.btn {
  font-family: var(--font);
  padding: 10px 20px;
  font-size: 1em;
  line-height: 1rem;
  margin-bottom: 4px;
  margin-top: 4px;
  border: initial;
  box-shadow: initial !important;
  transition: all var(--transition-time-short);
  transition-property: color, background-color, background-image, background;
  border-radius: var(--border-radius-md);
  color: var(--light-text) !important;
  white-space: nowrap; }
  .btn.shadow {
    box-shadow: 0 4px 13px rgba(0, 0, 0, 0.07) !important; }

.btn.disabled,
.btn:disabled {
  cursor: initial; }

.btn-icon {
  padding: 9px 20px;
  height: var(--input-height); }
  .btn-icon span {
    vertical-align: middle; }
  .btn-icon.btn-icon-only {
    padding: 9px 9px;
    flex-shrink: 0;
    width: var(--input-height); }
  .btn-icon.btn-sm.btn-icon-only {
    padding: 7px 7px;
    width: 30px; }
  .btn-icon svg {
    width: 15px;
    height: 15px; }
  .btn-icon.btn-xs {
    height: 28px; }
    .btn-icon.btn-xs svg {
      width: 12px;
      height: 12px;
      margin-top: -1px; }

.btn-xl,
.btn-group-xl > .btn {
  padding: 15px 40px;
  line-height: 1;
  font-size: 1em;
  border-radius: var(--border-radius-md); }
  .btn-xl.btn-icon,
  .btn-group-xl > .btn.btn-icon {
    padding: 13px 40px;
    height: 44px; }

.btn-lg,
.btn-group-lg > .btn {
  padding: 11px 35px;
  line-height: 1;
  font-size: 1em;
  border-radius: var(--border-radius-md); }
  .btn-lg.btn-icon,
  .btn-group-lg > .btn.btn-icon {
    padding: 9px 35px;
    height: var(--input-height); }

.btn-sm,
.btn-group-sm > .btn {
  padding: 8px 14px;
  font-size: 0.9em;
  border-radius: var(--border-radius-md);
  line-height: 1;
  height: 30px; }
  .btn-sm.btn-icon,
  .btn-group-sm > .btn.btn-icon {
    height: 30px; }
    .btn-sm.btn-icon i,
    .btn-group-sm > .btn.btn-icon i {
      font-size: 14px;
      width: 16px; }

.btn-primary,
.btn-primary:not(:disabled):not(.disabled):focus,
.btn-primary:not(:disabled):not(.disabled).focus {
  background-color: var(--primary); }
  .btn-primary:hover,
  .btn-primary:not(:disabled):not(.disabled):focus:hover,
  .btn-primary:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--primary-darker); }

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-primary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--primary-darker); }

.btn-primary.disabled,
.btn-primary:disabled {
  background-color: var(--primary); }

.btn-secondary,
.btn-secondary:not(:disabled):not(.disabled):focus,
.btn-secondary:not(:disabled):not(.disabled).focus {
  background-color: var(--secondary); }
  .btn-secondary:hover,
  .btn-secondary:not(:disabled):not(.disabled):focus:hover,
  .btn-secondary:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--secondary-darker); }

.btn-secondary.disabled,
.btn-secondary:disabled {
  background-color: var(--secondary); }

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active,
.show > .btn-secondary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-secondary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--secondary-darker); }

.btn-tertiary,
.btn-tertiary:not(:disabled):not(.disabled):focus,
.btn-tertiary:not(:disabled):not(.disabled).focus {
  background-color: var(--tertiary); }
  .btn-tertiary:hover,
  .btn-tertiary:not(:disabled):not(.disabled):focus:hover,
  .btn-tertiary:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--tertiary-darker); }

.btn-tertiary.disabled,
.btn-tertiary:disabled {
  background-color: var(--tertiary); }

.btn-tertiary:not(:disabled):not(.disabled):active,
.btn-tertiary:not(:disabled):not(.disabled).active,
.show > .btn-tertiary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-tertiary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--tertiary-darker); }

.btn-quaternary,
.btn-quaternary:not(:disabled):not(.disabled):focus,
.btn-quaternary:not(:disabled):not(.disabled).focus {
  background-color: var(--quaternary); }
  .btn-quaternary:hover,
  .btn-quaternary:not(:disabled):not(.disabled):focus:hover,
  .btn-quaternary:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--quaternary-darker); }

.btn-quaternary.disabled,
.btn-quaternary:disabled {
  background-color: var(--quaternary); }

.btn-quaternary:not(:disabled):not(.disabled):active,
.btn-quaternary:not(:disabled):not(.disabled).active,
.show > .btn-quaternary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-quaternary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--quaternary-darker); }

.btn-success,
.btn-success:not(:disabled):not(.disabled):focus,
.btn-success:not(:disabled):not(.disabled).focus {
  background-color: var(--success); }
  .btn-success:hover,
  .btn-success:not(:disabled):not(.disabled):focus:hover,
  .btn-success:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--success-darker); }

.btn-success.disabled,
.btn-success:disabled {
  background-color: var(--success); }

.btn-success:not(:disabled):not(.disabled):active,
.btn-success:not(:disabled):not(.disabled).active,
.show > .btn-success:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-success:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--success-darker); }

.btn-white,
.btn-white:not(:disabled):not(.disabled):focus,
.btn-white:not(:disabled):not(.disabled).focus {
  background-color: var(--light-text);
  color: var(--primary) !important; }
  .btn-white:hover,
  .btn-white:not(:disabled):not(.disabled):focus:hover,
  .btn-white:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--light-text-darker); }

.btn-white.disabled,
.btn-white:disabled {
  background-color: var(--light-text); }

.btn-white:not(:disabled):not(.disabled):active,
.btn-white:not(:disabled):not(.disabled).active,
.show > .btn-white:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-white:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--light-text-darker); }

.btn-black,
.btn-black:not(:disabled):not(.disabled):focus,
.btn-black:not(:disabled):not(.disabled).focus {
  background-color: var(--dark-text);
  color: var(--primary) !important; }
  .btn-black:hover,
  .btn-black:not(:disabled):not(.disabled):focus:hover,
  .btn-black:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--dark-text-darker); }

.btn-black.disabled,
.btn-black:disabled {
  background-color: var(--dark-text); }

.btn-black:not(:disabled):not(.disabled):active,
.btn-black:not(:disabled):not(.disabled).active,
.show > .btn-black:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-black:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--dark-text-darker); }

.btn-warning,
.btn-warning:not(:disabled):not(.disabled):focus,
.btn-warning:not(:disabled):not(.disabled).focus {
  background-color: var(--warning); }
  .btn-warning:hover,
  .btn-warning:not(:disabled):not(.disabled):focus:hover,
  .btn-warning:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--warning-darker); }

.btn-warning.disabled,
.btn-warning:disabled {
  background-color: var(--warning); }

.btn-warning:not(:disabled):not(.disabled):active,
.btn-warning:not(:disabled):not(.disabled).active,
.show > .btn-warning:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-warning:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--warning-darker); }

.btn-info,
.btn-info:not(:disabled):not(.disabled):focus,
.btn-info:not(:disabled):not(.disabled).focus {
  background-color: var(--info); }
  .btn-info:hover,
  .btn-info:not(:disabled):not(.disabled):focus:hover,
  .btn-info:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--info-darker); }

.btn-info.disabled,
.btn-info:disabled {
  background-color: var(--info); }

.btn-info:not(:disabled):not(.disabled):active,
.btn-info:not(:disabled):not(.disabled).active,
.show > .btn-info:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-info:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--info-darker); }

.btn-danger,
.btn-danger:not(:disabled):not(.disabled):focus,
.btn-danger:not(:disabled):not(.disabled).focus {
  background-color: var(--danger); }
  .btn-danger:hover,
  .btn-danger:not(:disabled):not(.disabled):focus:hover,
  .btn-danger:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--danger-darker); }

.btn-danger.disabled,
.btn-danger:disabled {
  background-color: var(--danger); }

.btn-danger:not(:disabled):not(.disabled):active,
.btn-danger:not(:disabled):not(.disabled).active,
.show > .btn-danger:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-danger:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--danger-darker); }

.btn-light,
.btn-light:not(:disabled):not(.disabled):focus,
.btn-light:not(:disabled):not(.disabled).focus {
  background-color: var(--light);
  color: var(--dark-text) !important; }
  .btn-light:hover,
  .btn-light:not(:disabled):not(.disabled):focus:hover,
  .btn-light:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--light-darker); }

.btn-light.disabled,
.btn-light:disabled {
  background-color: var(--light); }

.btn-light:not(:disabled):not(.disabled):active,
.btn-light:not(:disabled):not(.disabled).active,
.show > .btn-light:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-light:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--light-darker); }

.btn-dark,
.btn-dark:not(:disabled):not(.disabled):focus,
.btn-dark:not(:disabled):not(.disabled).focus {
  background-color: var(--dark); }
  .btn-dark:hover,
  .btn-dark:not(:disabled):not(.disabled):focus:hover,
  .btn-dark:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--dark-darker); }

.btn-dark.disabled,
.btn-dark:disabled {
  background-color: var(--light); }

.btn-dark:not(:disabled):not(.disabled):active,
.btn-dark:not(:disabled):not(.disabled).active,
.show > .btn-dark:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-dark:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--dark-darker); }

.btn-body,
.btn-body:not(:disabled):not(.disabled):focus,
.btn-body:not(:disabled):not(.disabled).focus {
  background-color: var(--body); }
  .btn-body:hover,
  .btn-body:not(:disabled):not(.disabled):focus:hover,
  .btn-body:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--body-darker); }

.btn-body.disabled,
.btn-body:disabled {
  background-color: var(--light); }

.btn-body:not(:disabled):not(.disabled):active,
.btn-body:not(:disabled):not(.disabled).active,
.show > .btn-body:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-body:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--body-darker); }

.btn-muted,
.btn-muted:not(:disabled):not(.disabled):focus,
.btn-muted:not(:disabled):not(.disabled).focus {
  background-color: var(--muted); }
  .btn-muted:hover,
  .btn-muted:not(:disabled):not(.disabled):focus:hover,
  .btn-muted:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--muted-darker); }

.btn-muted.disabled,
.btn-muted:disabled {
  background-color: var(--light); }

.btn-muted:not(:disabled):not(.disabled):active,
.btn-muted:not(:disabled):not(.disabled).active,
.show > .btn-muted:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-muted:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--muted-darker); }

.btn-separator,
.btn-separator:not(:disabled):not(.disabled):focus,
.btn-separator:not(:disabled):not(.disabled).focus {
  background-color: var(--separator); }
  .btn-separator:hover,
  .btn-separator:not(:disabled):not(.disabled):focus:hover,
  .btn-separator:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--separator-darker); }

.btn-separator.disabled,
.btn-separator:disabled {
  background-color: var(--light); }

.btn-separator:not(:disabled):not(.disabled):active,
.btn-separator:not(:disabled):not(.disabled).active,
.show > .btn-separator:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-separator:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--separator-darker); }

.btn-separator-light,
.btn-separator-light:not(:disabled):not(.disabled):focus,
.btn-separator-light:not(:disabled):not(.disabled).focus {
  background-color: var(--separator-light);
  color: var(--body) !important; }
  .btn-separator-light:hover,
  .btn-separator-light:not(:disabled):not(.disabled):focus:hover,
  .btn-separator-light:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--separator-darker);
    color: var(--light-text) !important; }

.btn-separator-light.disabled,
.btn-separator-light:disabled {
  background-color: var(--light); }

.btn-separator-light:not(:disabled):not(.disabled):active,
.btn-separator-light:not(:disabled):not(.disabled).active,
.show > .btn-separator-light:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-separator-light:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--separator-darker); }

.btn-alternate,
.btn-alternate:not(:disabled):not(.disabled):focus,
.btn-alternate:not(:disabled):not(.disabled).focus {
  background-color: var(--alternate); }
  .btn-alternate:hover,
  .btn-alternate:not(:disabled):not(.disabled):focus:hover,
  .btn-alternate:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--alternate-darker); }

.btn-alternate.disabled,
.btn-alternate:disabled {
  background-color: var(--light); }

.btn-alternate:not(:disabled):not(.disabled):active,
.btn-alternate:not(:disabled):not(.disabled).active,
.show > .btn-alternate:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-alternate:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--alternate-darker); }

.btn-link,
.btn-link:not(:disabled):not(.disabled):focus,
.btn-link:not(:disabled):not(.disabled).focus {
  transition: color var(--transition-time-short);
  color: var(--primary) !important;
  text-decoration: initial !important; }
  .btn-link:hover, .btn-link:active,
  .btn-link:not(:disabled):not(.disabled):focus:hover,
  .btn-link:not(:disabled):not(.disabled):focus:active,
  .btn-link:not(:disabled):not(.disabled).focus:hover,
  .btn-link:not(:disabled):not(.disabled).focus:active {
    text-decoration: initial;
    color: var(--secondary) !important; }

.btn-link.disabled,
.btn-link:disabled {
  background-color: var(--link); }

.btn-link:not(:disabled):not(.disabled):active,
.btn-link:not(:disabled):not(.disabled).active,
.show > .btn-link:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-link:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  color: var(--primary-darker) !important; }

.btn-outline-primary {
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  color: var(--primary) !important; }
  .btn-outline-primary:hover {
    color: var(--light-text) !important;
    background-color: var(--primary);
    box-shadow: initial !important; }

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  background: initial;
  color: var(--primary) !important; }

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-primary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--primary);
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  color: var(--light-text) !important; }

.btn-outline-secondary {
  box-shadow: inset 0 0 0 1px var(--secondary) !important;
  color: var(--secondary) !important; }
  .btn-outline-secondary:hover {
    color: var(--light-text) !important;
    background-color: var(--secondary);
    box-shadow: initial !important; }

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  box-shadow: inset 0 0 0 1px var(--secondary) !important;
  background: initial;
  color: var(--secondary) !important; }

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-secondary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--secondary);
  box-shadow: inset 0 0 0 1px var(--secondary) !important;
  color: var(--light-text) !important; }

.btn-outline-tertiary {
  box-shadow: inset 0 0 0 1px var(--tertiary) !important;
  color: var(--tertiary) !important; }
  .btn-outline-tertiary:hover {
    color: var(--light-text) !important;
    background-color: var(--tertiary);
    box-shadow: initial !important; }

.btn-outline-tertiary.disabled,
.btn-outline-tertiary:disabled {
  box-shadow: inset 0 0 0 1px var(--tertiary) !important;
  background: initial;
  color: var(--tertiary) !important; }

.btn-outline-tertiary:not(:disabled):not(.disabled):active,
.btn-outline-tertiary:not(:disabled):not(.disabled).active,
.show > .btn-outline-tertiary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-tertiary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--tertiary);
  box-shadow: inset 0 0 0 1px var(--tertiary) !important;
  color: var(--light-text) !important; }

.btn-outline-quaternary {
  box-shadow: inset 0 0 0 1px var(--quaternary) !important;
  color: var(--quaternary) !important; }
  .btn-outline-quaternary:hover {
    color: var(--light-text) !important;
    background-color: var(--quaternary);
    box-shadow: initial !important; }

.btn-outline-quaternary.disabled,
.btn-outline-quaternary:disabled {
  box-shadow: inset 0 0 0 1px var(--quaternary) !important;
  background: initial;
  color: var(--quaternary) !important; }

.btn-outline-quaternary:not(:disabled):not(.disabled):active,
.btn-outline-quaternary:not(:disabled):not(.disabled).active,
.show > .btn-outline-quaternary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-quaternary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--quaternary);
  box-shadow: inset 0 0 0 1px var(--quaternary) !important;
  color: var(--light-text) !important; }

.btn-outline-success {
  box-shadow: inset 0 0 0 1px var(--success) !important;
  color: var(--success) !important; }
  .btn-outline-success:hover {
    color: var(--light-text) !important;
    background-color: var(--success);
    box-shadow: initial !important; }

.btn-outline-success.disabled,
.btn-outline-success:disabled {
  box-shadow: inset 0 0 0 1px var(--success) !important;
  background: initial;
  color: var(--success) !important; }

.btn-outline-success:not(:disabled):not(.disabled):active,
.btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-success:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--success);
  box-shadow: inset 0 0 0 1px var(--success) !important;
  color: var(--light-text) !important; }

.btn-outline-warning {
  box-shadow: inset 0 0 0 1px var(--warning) !important;
  color: var(--warning) !important; }
  .btn-outline-warning:hover {
    color: var(--light-text) !important;
    background-color: var(--warning);
    box-shadow: initial !important; }

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  box-shadow: inset 0 0 0 1px var(--warning) !important;
  background: initial;
  color: var(--warning) !important; }

.btn-outline-warning:not(:disabled):not(.disabled):active,
.btn-outline-warning:not(:disabled):not(.disabled).active,
.show > .btn-outline-warning:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-warning:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--warning);
  box-shadow: inset 0 0 0 1px var(--warning) !important;
  color: var(--light-text) !important; }

.btn-outline-light {
  box-shadow: inset 0 0 0 1px var(--light) !important;
  color: var(--light) !important; }
  .btn-outline-light:hover {
    color: var(--dark-text) !important;
    background-color: var(--light);
    box-shadow: initial !important; }

.btn-outline-light.disabled,
.btn-outline-light:disabled {
  box-shadow: inset 0 0 0 1px var(--light) !important;
  background: initial;
  color: var(--light) !important; }

.btn-outline-light:not(:disabled):not(.disabled):active,
.btn-outline-light:not(:disabled):not(.disabled).active,
.show > .btn-outline-light:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-light:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--light);
  box-shadow: inset 0 0 0 1px var(--light) !important;
  color: var(--dark-text) !important; }

.btn-outline-white {
  box-shadow: inset 0 0 0 1px var(--light-text) !important;
  color: var(--light-text) !important; }
  .btn-outline-white:hover {
    color: var(--dark-text) !important;
    background-color: var(--light-text);
    box-shadow: initial !important; }

.btn-outline-white.disabled,
.btn-outline-white:disabled {
  box-shadow: inset 0 0 0 1px var(--light-text) !important;
  background: initial;
  color: var(--light-text) !important; }

.btn-outline-white:not(:disabled):not(.disabled):active,
.btn-outline-white:not(:disabled):not(.disabled).active,
.show > .btn-outline-white:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-white:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--light-text);
  box-shadow: inset 0 0 0 1px var(--light-text) !important;
  color: var(--dark-text) !important; }

.btn-outline-black {
  box-shadow: inset 0 0 0 1px var(--dark-text) !important;
  color: var(--dark-text) !important; }
  .btn-outline-black:hover {
    color: var(--light-text) !important;
    background-color: var(--dark-text);
    box-shadow: initial !important; }

.btn-outline-black.disabled,
.btn-outline-black:disabled {
  box-shadow: inset 0 0 0 1px var(--dark-text) !important;
  background: initial;
  color: var(--dark-text) !important; }

.btn-outline-black:not(:disabled):not(.disabled):active,
.btn-outline-black:not(:disabled):not(.disabled).active,
.show > .btn-outline-black:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-black:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--dark-text);
  box-shadow: inset 0 0 0 1px var(--dark-text) !important;
  color: var(--light-text) !important; }

.btn-outline-info {
  box-shadow: inset 0 0 0 1px var(--info) !important;
  color: var(--info) !important; }
  .btn-outline-info:hover {
    color: var(--light-text) !important;
    background-color: var(--info);
    box-shadow: initial !important; }

.btn-outline-info.disabled,
.btn-outline-info:disabled {
  box-shadow: inset 0 0 0 1px var(--info) !important;
  background: initial;
  color: var(--info) !important; }

.btn-outline-info:not(:disabled):not(.disabled):active,
.btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-info:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--info);
  box-shadow: inset 0 0 0 1px var(--info) !important;
  color: var(--light-text) !important; }

.btn-outline-danger {
  box-shadow: inset 0 0 0 1px var(--danger) !important;
  color: var(--danger) !important; }
  .btn-outline-danger:hover {
    color: var(--light-text) !important;
    background-color: var(--danger);
    box-shadow: initial !important; }

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  box-shadow: inset 0 0 0 1px var(--info) !important;
  background: initial;
  color: var(--danger) !important; }

.btn-outline-danger:not(:disabled):not(.disabled):active,
.btn-outline-danger:not(:disabled):not(.disabled).active,
.show > .btn-outline-danger:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-danger:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--danger);
  box-shadow: inset 0 0 0 1px var(--danger) !important;
  color: var(--light-text) !important; }

.btn-outline-dark {
  box-shadow: inset 0 0 0 1px var(--dark) !important;
  color: var(--dark) !important; }
  .btn-outline-dark:hover {
    color: var(--light-text) !important;
    background-color: var(--dark);
    box-shadow: initial !important; }

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  box-shadow: inset 0 0 0 1px var(--dark) !important;
  background: initial;
  color: var(--dark) !important; }

.btn-outline-dark:not(:disabled):not(.disabled):active,
.btn-outline-dark:not(:disabled):not(.disabled).active,
.show > .btn-outline-dark:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-dark:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--dark);
  box-shadow: inset 0 0 0 1px var(--dark) !important;
  color: var(--light-text) !important; }

.btn-outline-body {
  box-shadow: inset 0 0 0 1px var(--body) !important;
  color: var(--body) !important; }
  .btn-outline-body:hover {
    color: var(--light-text) !important;
    background-color: var(--body);
    box-shadow: initial !important; }

.btn-outline-body.disabled,
.btn-outline-body:disabled {
  box-shadow: inset 0 0 0 1px var(--body) !important;
  background: initial;
  color: var(--body) !important; }

.btn-outline-body:not(:disabled):not(.disabled):active,
.btn-outline-body:not(:disabled):not(.disabled).active,
.show > .btn-outline-body:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-body:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--body);
  box-shadow: inset 0 0 0 1px var(--body) !important;
  color: var(--light-text) !important; }

.btn-outline-muted {
  box-shadow: inset 0 0 0 1px var(--muted) !important;
  color: var(--muted) !important; }
  .btn-outline-muted:hover {
    color: var(--light-text) !important;
    background-color: var(--muted);
    box-shadow: initial !important; }

.btn-outline-muted.disabled,
.btn-outline-muted:disabled {
  box-shadow: inset 0 0 0 1px var(--muted) !important;
  background: initial;
  color: var(--muted) !important; }

.btn-outline-muted:not(:disabled):not(.disabled):active,
.btn-outline-muted:not(:disabled):not(.disabled).active,
.show > .btn-outline-muted:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-muted:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--muted);
  box-shadow: inset 0 0 0 1px var(--muted) !important;
  color: var(--light-text) !important; }

.btn-outline-separator {
  box-shadow: inset 0 0 0 1px var(--separator) !important;
  color: var(--separator) !important; }
  .btn-outline-separator:hover {
    color: var(--light-text) !important;
    background-color: var(--separator);
    box-shadow: initial !important; }

.btn-outline-separator.disabled,
.btn-outline-separator:disabled {
  box-shadow: inset 0 0 0 1px var(--separator) !important;
  background: initial;
  color: var(--separator) !important; }

.btn-outline-separator:not(:disabled):not(.disabled):active,
.btn-outline-separator:not(:disabled):not(.disabled).active,
.show > .btn-outline-separator:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-separator:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--separator);
  box-shadow: inset 0 0 0 1px var(--separator) !important;
  color: var(--light-text) !important; }

.btn-outline-alternate {
  box-shadow: inset 0 0 0 1px var(--alternate) !important;
  color: var(--alternate) !important; }
  .btn-outline-alternate:hover {
    color: var(--light-text) !important;
    background-color: var(--alternate);
    box-shadow: initial !important; }

.btn-outline-alternate.disabled,
.btn-outline-alternate:disabled {
  box-shadow: inset 0 0 0 1px var(--alternate) !important;
  background: initial;
  color: var(--alternate) !important; }

.btn-outline-alternate:not(:disabled):not(.disabled):active,
.btn-outline-alternate:not(:disabled):not(.disabled).active,
.show > .btn-outline-alternate:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-outline-alternate:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--alternate);
  box-shadow: inset 0 0 0 1px var(--alternate) !important;
  color: var(--light-text) !important; }

.btn-gradient-primary,
.btn-gradient-primary:not(:disabled):not(.disabled):focus,
.btn-gradient-primary:not(:disabled):not(.disabled).focus {
  z-index: 1;
  position: relative;
  background-color: var(--primary);
  background-image: linear-gradient(to right, var(--primary), var(--primary), rgba(var(--secondary-rgb), 0.5)); }
  .btn-gradient-primary::before,
  .btn-gradient-primary:not(:disabled):not(.disabled):focus::before,
  .btn-gradient-primary:not(:disabled):not(.disabled).focus::before {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    transition: opacity var(--transition-time-short) linear;
    opacity: 0;
    background-image: linear-gradient(to right, var(--primary-darker), var(--primary-darker));
    border-radius: var(--border-radius-md); }
  .btn-gradient-primary:hover::before,
  .btn-gradient-primary:not(:disabled):not(.disabled):focus:hover::before,
  .btn-gradient-primary:not(:disabled):not(.disabled).focus:hover::before {
    opacity: 1; }

.btn-gradient-primary:not(:disabled):not(.disabled):active,
.btn-gradient-primary:not(:disabled):not(.disabled).active,
.show > .btn-gradient-primary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-gradient-primary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-image: linear-gradient(to right, var(--primary-darker), var(--primary-darker)); }
  .btn-gradient-primary:not(:disabled):not(.disabled):active::before,
  .btn-gradient-primary:not(:disabled):not(.disabled).active::before,
  .show > .btn-gradient-primary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']::before,
  .show > .btn-gradient-primary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']::before {
    transition: opacity var(--transition-time-short) linear;
    opacity: 0; }

.btn-gradient-primary.disabled,
.btn-gradient-primary:disabled {
  background-color: var(--primary);
  background-image: linear-gradient(to right, var(--primary), var(--primary), rgba(var(--secondary-rgb), 0.5)); }
  .btn-gradient-primary.disabled::before,
  .btn-gradient-primary:disabled::before {
    opacity: 0; }

.btn-gradient-secondary,
.btn-gradient-secondary:not(:disabled):not(.disabled):focus,
.btn-gradient-secondary:not(:disabled):not(.disabled).focus {
  z-index: 1;
  position: relative;
  background-color: var(--secondary);
  background-image: linear-gradient(to right, var(--secondary), var(--secondary), rgba(var(--primary-rgb), 0.4)); }
  .btn-gradient-secondary::before,
  .btn-gradient-secondary:not(:disabled):not(.disabled):focus::before,
  .btn-gradient-secondary:not(:disabled):not(.disabled).focus::before {
    position: absolute;
    content: '';
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    transition: opacity var(--transition-time-short) linear;
    opacity: 0;
    background-image: linear-gradient(to right, var(--secondary-darker), var(--secondary-darker));
    border-radius: var(--border-radius-md); }
  .btn-gradient-secondary:hover::before,
  .btn-gradient-secondary:not(:disabled):not(.disabled):focus:hover::before,
  .btn-gradient-secondary:not(:disabled):not(.disabled).focus:hover::before {
    opacity: 1; }

.btn-gradient-secondary:not(:disabled):not(.disabled):active,
.btn-gradient-secondary:not(:disabled):not(.disabled).active,
.show > .btn-gradient-secondary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-gradient-secondary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-image: linear-gradient(to right, var(--secondary-darker), var(--secondary-darker)); }
  .btn-gradient-secondary:not(:disabled):not(.disabled):active::before,
  .btn-gradient-secondary:not(:disabled):not(.disabled).active::before,
  .show > .btn-gradient-secondary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']::before,
  .show > .btn-gradient-secondary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']::before {
    transition: opacity var(--transition-time-short) linear;
    opacity: 0; }

.btn-gradient-secondary.disabled,
.btn-gradient-secondary:disabled {
  background-color: var(--secondary);
  background-image: linear-gradient(to right, var(--secondary), var(--secondary), rgba(var(--primary-rgb), 0.4)); }
  .btn-gradient-secondary.disabled::before,
  .btn-gradient-secondary:disabled::before {
    opacity: 0; }

.btn-group-vertical > .btn:first-child,
.btn-group-vertical > .btn-group:first-child > .btn {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.btn-group-vertical > .btn:last-child,
.btn-group-vertical > .btn-group:last-child > .btn {
  border-bottom-left-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md); }

.btn-foreground,
.btn-foreground:not(:disabled):not(.disabled):focus,
.btn-foreground:not(:disabled):not(.disabled).focus {
  background-color: var(--foreground);
  color: var(--primary) !important; }
  .btn-foreground:hover,
  .btn-foreground:not(:disabled):not(.disabled):focus:hover,
  .btn-foreground:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--foreground);
    color: var(--secondary) !important; }

.btn-foreground:not(:disabled):not(.disabled):active,
.btn-foreground:not(:disabled):not(.disabled).active,
.show > .btn-foreground:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-foreground:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--foreground);
  color: var(--primary) !important; }

.btn-foreground.disabled,
.btn-foreground:disabled {
  background-color: var(--foreground);
  color: var(--primary) !important; }

.btn-foreground-alternate,
.btn-foreground-alternate:not(:disabled):not(.disabled):focus,
.btn-foreground-alternate:not(:disabled):not(.disabled).focus {
  background-color: var(--foreground);
  color: var(--alternate) !important; }
  .btn-foreground-alternate:hover,
  .btn-foreground-alternate:not(:disabled):not(.disabled):focus:hover,
  .btn-foreground-alternate:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--foreground);
    color: var(--primary) !important; }

.btn-foreground-alternate:not(:disabled):not(.disabled):active,
.btn-foreground-alternate:not(:disabled):not(.disabled).active,
.show > .btn-foreground-alternate:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-foreground-alternate:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--foreground);
  color: var(--alternate) !important; }

.btn-foreground-alternate.disabled,
.btn-foreground-alternate:disabled {
  background-color: var(--foreground);
  color: var(--alternate) !important; }

.btn-background-alternate,
.btn-background-alternate:not(:disabled):not(.disabled):focus,
.btn-background-alternate:not(:disabled):not(.disabled).focus {
  background-color: var(--background);
  color: var(--alternate) !important; }
  .btn-background-alternate:hover,
  .btn-background-alternate:not(:disabled):not(.disabled):focus:hover,
  .btn-background-alternate:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--background);
    color: var(--primary) !important; }

.btn-background-alternate:not(:disabled):not(.disabled):active,
.btn-background-alternate:not(:disabled):not(.disabled).active,
.show > .btn-background-alternate:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-background-alternate:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--background);
  color: var(--alternate) !important; }

.btn-background-alternate.disabled,
.btn-background-alternate:disabled {
  background-color: var(--background);
  color: var(--alternate) !important; }

.btn-background,
.btn-background:not(:disabled):not(.disabled):focus,
.btn-background:not(:disabled):not(.disabled).focus {
  background-color: var(--background);
  color: var(--primary) !important; }
  .btn-background:hover,
  .btn-background:not(:disabled):not(.disabled):focus:hover,
  .btn-background:not(:disabled):not(.disabled).focus:hover {
    background-color: var(--background);
    color: var(--secondary) !important; }

.btn-background:not(:disabled):not(.disabled):active,
.btn-background:not(:disabled):not(.disabled).active,
.show > .btn-background:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-background:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--background);
  color: var(--primary) !important; }

.btn-background.disabled,
.btn-background:disabled {
  background-color: var(--background);
  color: var(--primary) !important; }

.btn-foreground.hover-outline,
.btn-foreground.hover-outline:not(:disabled):not(.disabled):focus,
.btn-foreground.hover-outline:not(:disabled):not(.disabled).focus {
  background-color: var(--foreground);
  color: var(--primary) !important; }
  .btn-foreground.hover-outline:hover,
  .btn-foreground.hover-outline:not(:disabled):not(.disabled):focus:hover,
  .btn-foreground.hover-outline:not(:disabled):not(.disabled).focus:hover {
    color: var(--primary) !important;
    box-shadow: inset 0 0 0 1px var(--primary) !important; }

.btn-foreground.hover-outline:not(:disabled):not(.disabled):active,
.btn-foreground.hover-outline:not(:disabled):not(.disabled).active,
.show > .btn-foreground.hover-outline:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-foreground.hover-outline:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  background-color: var(--foreground);
  color: var(--primary) !important;
  box-shadow: inset 0 0 0 1px var(--primary) !important; }

.btn-foreground.hover-outline.disabled,
.btn-foreground.hover-outline:disabled {
  background-color: var(--foreground);
  color: var(--primary) !important; }

.btn-background.hover-outline,
.btn-background.hover-outline:not(:disabled):not(.disabled):focus,
.btn-background.hover-outline:not(:disabled):not(.disabled).focus {
  background-color: var(--background);
  color: var(--primary) !important; }
  .btn-background.hover-outline:hover,
  .btn-background.hover-outline:not(:disabled):not(.disabled):focus:hover,
  .btn-background.hover-outline:not(:disabled):not(.disabled).focus:hover {
    color: var(--primary) !important;
    background-color: var(--foreground);
    box-shadow: inset 0 0 0 1px var(--primary) !important; }

.btn-background.hover-outline:not(:disabled):not(.disabled):active,
.btn-background.hover-outline:not(:disabled):not(.disabled).active,
.show > .btn-background.hover-outline:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true'],
.show > .btn-background.hover-outline:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true'] {
  color: var(--primary) !important;
  background-color: var(--foreground);
  box-shadow: inset 0 0 0 1px var(--primary) !important; }

.btn-background.hover-outline.disabled,
.btn-background.hover-outline:disabled {
  background-color: var(--background);
  color: var(--primary) !important; }

.btn-overlay.btn-foreground {
  background-color: rgba(var(--foreground-rgb), 0.7); }
  .btn-overlay.btn-foreground:hover {
    background-color: rgba(var(--foreground-rgb), 1); }

.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-primary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-secondary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-secondary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-tertiary:not(:disabled):not(.disabled):active:focus,
.btn-outline-tertiary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-tertiary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-tertiary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-quaternary:not(:disabled):not(.disabled):active:focus,
.btn-outline-quaternary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-quaternary:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-quaternary:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-success:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-success:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-warning:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-warning:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-info:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-info:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-danger:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-danger:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-light:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--dark-text) !important; }

.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-dark:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-dark:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-body:not(:disabled):not(.disabled):active:focus,
.btn-outline-body:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-body:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-body:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-alternate:not(:disabled):not(.disabled):active:focus,
.btn-outline-alternate:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-alternate:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-alternate:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-muted:not(:disabled):not(.disabled):active:focus,
.btn-outline-muted:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-muted:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-muted:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.btn-outline-separator:not(:disabled):not(.disabled):active:focus,
.btn-outline-separator:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-separator:not(:disabled):not(.disabled)[data-bs-toggle='dropdown'][aria-expanded='true']:active:focus,
.show > .btn-outline-separator:not(:disabled):not(.disabled).dropdown-toggle[aria-expanded='true']:active:focus {
  color: var(--light-text) !important; }

.dropdown-toggle-split {
  padding-left: 12px;
  padding-right: 12px; }

.btn-xl + .dropdown-toggle-split,
.btn-group-xl > .btn + .dropdown-toggle-split {
  padding-left: 20px;
  padding-right: 20px; }

.btn-lg + .dropdown-toggle-split,
.btn-group-lg > .btn + .dropdown-toggle-split {
  padding-left: 15px;
  padding-right: 15px; }

.btn-sm + .dropdown-toggle-split,
.btn-group-sm > .btn + .dropdown-toggle-split {
  padding-left: 10px;
  padding-right: 10px; }

.btn-close {
  width: calc(var(--input-height) / 2);
  height: calc(var(--input-height) / 2);
  margin: 0;
  padding: 0.25rem;
  background: initial;
  opacity: 1;
  font-size: 18px;
  font-family: 'Plex-Regular' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--alternate); }
  .btn-close:after {
    display: inline-block;
    content: '\e91b'; }
  .btn-close:hover:after {
    color: var(--primary); }

.btn.disabled,
.btn:disabled {
  opacity: 0.5; }

.btn-custom-control .form-check-input {
  pointer-events: none;
  margin-top: 0.25rem !important;
  background-color: transparent; }

.btn-custom-control .form-check {
  margin-bottom: 0; }

.btn-custom-control.btn-sm {
  padding-top: 3px; }

[class*='btn-outline-'].btn-custom-control:hover .form-check-input {
  border-color: var(--light-text) !important; }

.btn-outline-primary.btn-custom-control .form-check-input {
  border-color: var(--primary) !important; }

.btn-outline-primary.btn-custom-control .form-check-input:checked {
  background-color: var(--primary) !important; }

.btn-outline-secondary.btn-custom-control .form-check-input {
  border-color: var(--secondary) !important; }

.btn-outline-secondary.btn-custom-control .form-check-input:checked {
  background-color: var(--secondary) !important; }

.btn-outline-secondary.btn-custom-control .form-check-input[type='checkbox']:indeterminate {
  background-color: var(--secondary);
  border-color: var(--secondary); }

.btn-primary.btn-custom-control .form-check-input,
.btn-secondary.btn-custom-control .form-check-input {
  border-color: var(--light-text) !important; }

.btn-primary.btn-custom-control .form-check-input:checked,
.btn-secondary.btn-custom-control .form-check-input:checked {
  border-color: var(--light-text) !important; }

.check-all-container .btn-sm.btn-custom-control {
  padding-left: 10px !important;
  font-size: 0.8em;
  padding-right: 2px !important; }
  .check-all-container .btn-sm.btn-custom-control .form-check {
    padding-top: 0; }

.check-all-container-checkbox-click .btn-custom-control .form-check,
.check-all-container .btn-custom-control .form-check {
  padding-top: 0.3rem; }

.hover-scale-up {
  backface-visibility: hidden;
  transition: transform var(--transition-time-short); }
  .hover-scale-up:hover {
    transform: scale(1.02);
    z-index: 1; }

.hover-scale-down {
  backface-visibility: hidden;
  transition: transform var(--transition-time-short); }
  .hover-scale-down:hover {
    transform: scale(0.98); }

.active-scale-up {
  backface-visibility: hidden;
  transition: transform var(--transition-time-short); }
  .active-scale-up:active {
    z-index: 1;
    transform: scale(1.05); }

.active-scale-down {
  backface-visibility: hidden;
  transition: transform var(--transition-time-short); }
  .active-scale-down:active {
    transform: scale(0.95); }

.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: initial; }

.btn-check:checked + .btn-outline-primary,
.btn-check:active + .btn-outline-primary,
.btn-outline-primary:active,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show {
  color: var(--light-text) !important;
  background-color: var(--primary);
  border-color: initial; }

.btn-check:checked + .btn-outline-secondary,
.btn-check:active + .btn-outline-secondary,
.btn-outline-secondary:active,
.btn-outline-secondary.active,
.btn-outline-secondary.dropdown-toggle.show {
  color: var(--light-text) !important;
  background-color: var(--secondary);
  border-color: initial; }

.btn-check:checked + .btn-primary,
.btn-check:active + .btn-primary,
.btn-primary:active,
.btn-primary.active,
.show > .btn-primary.dropdown-toggle {
  background-color: var(--primary-darker);
  border-color: var(--primary-darker); }

.btn-check:focus + .btn-primary,
.btn-primary:focus {
  background-color: var(--primary-darker);
  border-color: var(--primary-darker); }

.btn-check:not(:checked):focus + .btn-primary {
  background-color: var(--primary);
  border-color: var(--primary); }

.btn-check:checked + .btn-secondary,
.btn-check:active + .btn-secondary,
.btn-secondary:active,
.btn-secondary.active,
.show > .btn-secondary.dropdown-toggle {
  background-color: var(--secondary-darker);
  border-color: var(--secondary-darker); }

.btn-check:focus + .btn-secondary,
.btn-secondary:focus {
  background-color: var(--secondary-darker);
  border-color: var(--secondary-darker); }

.btn-check:not(:checked):focus + .btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary); }

/*
*
* Card
*
* Bootstrap card styles.
*
*/
.card {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
  background: var(--foreground);
  border-radius: var(--border-radius-lg);
  border: initial; }
  .card.no-shadow {
    box-shadow: initial !important; }
  .card .card-body,
  .card .card-footer,
  .card .card-header {
    padding: var(--card-spacing); }
  .card .half-padding.card-body, .card .half-padding.card-footer, .card .half-padding.card-header {
    padding: calc(var(--card-spacing) / 2); }
  .card.sm .card-body,
  .card.sm .card-footer,
  .card.sm .card-header {
    padding: var(--card-spacing-sm); }
  .card .card-header .handle {
    cursor: default; }
  .card .card-header {
    background: var(--foreground);
    border-color: rgba(var(--separator-rgb), 0.5); }
  .card .card-footer {
    background: initial;
    border-color: rgba(var(--separator-rgb), 0.3); }
  .card .card-img {
    border-radius: var(--border-radius-lg); }
  .card .card-img-top {
    width: 100%;
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-top-right-radius: var(--border-radius-lg); }
  .card .card-img-overlay {
    background: rgba(0, 0, 0, 0.35);
    border-radius: var(--border-radius-lg); }
  .card .card-img-bottom {
    width: 100%;
    border-radius: initial;
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg); }
  .card .card-img-left {
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: initial;
    border-top-right-radius: initial; }
    @media (max-width: 767.98px) {
      .card .card-img-left {
        border-radius: initial;
        border-bottom-left-radius: initial;
        border-top-left-radius: var(--border-radius-lg);
        border-top-right-radius: var(--border-radius-lg);
        border-bottom-right-radius: initial; } }
  .card [class*='card-img-horizontal'] {
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: initial;
    border-top-right-radius: initial;
    height: 100%; }
  @media (max-width: 575.98px) {
    .card .card-img-horizontal-sm {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial; } }
  @media (max-width: 767.98px) {
    .card .card-img-horizontal-md {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial; } }
  @media (max-width: 991.98px) {
    .card .card-img-horizontal-lg {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial; } }
  @media (max-width: 1199.98px) {
    .card .card-img-horizontal-xl {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial; } }
  .card .card-footer {
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg); }
  .card .card[class*='border'] {
    border: 1px solid var(--separator); }
  .card .card-header:first-child {
    border-top-left-radius: var(--border-radius-lg);
    border-top-right-radius: var(--border-radius-lg); }
  .card .card-img-overlay {
    padding: var(--card-spacing); }
  .card .card-top-buttons {
    right: 0;
    top: 0; }
    @media (max-width: 575.98px) {
      .card .card-top-buttons {
        padding: 0.35rem; } }

.card.active:after,
.card.selected:after,
.card.activatable.context-menu-active:after {
  box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5), 0 4px 13px rgba(0, 0, 0, 0.04) !important;
  content: '';
  display: block;
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: var(--border-radius-lg);
  z-index: 0;
  pointer-events: none; }

.card .card {
  border-radius: var(--border-radius-md); }

.card-top-buttons {
  position: absolute;
  right: 0;
  top: 0; }
  .card-top-buttons .btn {
    color: var(--primary) !important; }

.card-deck {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  margin-right: -20px;
  margin-left: -20px; }
  @media (max-width: 767.98px) {
    .card-deck {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin-right: initial;
      margin-left: initial; } }
  .card-deck .card {
    margin-right: 20px;
    margin-left: 20px; }
    @media (max-width: 767.98px) {
      .card-deck .card {
        margin-right: initial;
        margin-left: initial; } }

.bg-primary .card-header {
  background-color: var(--primary); }

.bg-secondary .card-header {
  background-color: var(--secondary); }

.bg-tertiary .card-header {
  background-color: var(--tertiary); }

.bg-quaternary .card-header {
  background-color: var(--quaternary); }

.bg-warning .card-header {
  background-color: var(--warning); }

.bg-danger .card-header {
  background-color: var(--danger); }

.bg-success .card-header {
  background-color: var(--success); }

.bg-info .card-header {
  background-color: var(--info); }

.bg-light {
  background-color: var(--background-light) !important; }
  .bg-light .card-header {
    background-color: var(--background-light) !important;
    border-color: rgba(var(--muted-rgb), 0.6); }
  .bg-light .card-footer {
    border-color: rgba(var(--muted-rgb), 0.6); }

.bg-dark .card-header {
  background-color: var(--dark); }

.border-primary.card {
  border: 1px solid var(--primary); }
  .border-primary.card .card-header {
    border-color: var(--primary); }

.border-secondary {
  border: 1px solid var(--secondary); }
  .border-secondary .card-header {
    border-color: var(--secondary); }

.border-tertiary {
  border: 1px solid var(--tertiary); }
  .border-tertiary .card-header {
    border-color: var(--tertiary); }

.border-quaternary {
  border: 1px solid var(--quaternary); }
  .border-quaternary .card-header {
    border-color: var(--quaternary); }

.border-info {
  border: 1px solid var(--info); }
  .border-info .card-header {
    border-color: var(--info); }

.border-success {
  border: 1px solid var(--success); }
  .border-success .card-header {
    border-color: var(--success); }

.border-danger {
  border: 1px solid var(--danger); }
  .border-danger .card-header {
    border-color: var(--danger); }

.border-light {
  border: 1px solid var(--light); }
  .border-light .card-header {
    border-color: var(--light); }

.border-dark {
  border: 1px solid var(--dark); }
  .border-dark .card-header {
    border-color: var(--dark); }

.border-warning {
  border: 1px solid var(--warning); }
  .border-warning .card-header {
    border-color: var(--warning); }

.p-card {
  padding: var(--card-spacing); }

.pe-card {
  padding-right: var(--card-spacing); }

.ps-card {
  padding-left: var(--card-spacing); }

.pt-card {
  padding-top: var(--card-spacing); }

.pb-card {
  padding-bottom: var(--card-spacing); }

.m-card {
  margin: var(--card-spacing); }

.mb-card {
  margin-bottom: var(--card-spacing); }

.mt-card {
  margin-top: var(--card-spacing); }

.ms-card {
  margin-left: var(--card-spacing); }

.me-card {
  margin-right: var(--card-spacing); }

.list-group-item.active {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text); }
  .list-group-item.active * {
    color: var(--light-text); }

.list-group-item-action:hover,
.list-group-item-action:focus {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text); }
  .list-group-item-action:hover *,
  .list-group-item-action:focus * {
    color: var(--light-text); }

.list-group-item {
  background: var(--foreground);
  color: var(--body);
  border-color: rgba(var(--separator-rgb), 0.5);
  padding: 16px 12px; }
  .list-group-item.list-group-item-primary {
    background: var(--primary); }
  .list-group-item.list-group-item-secondary {
    background: var(--secondary); }
  .list-group-item.list-group-item-success {
    background: var(--success); }
  .list-group-item.list-group-item-danger {
    background: var(--danger); }
  .list-group-item.list-group-item-warning {
    background: var(--warning); }
  .list-group-item.list-group-item-info {
    background: var(--info); }
  .list-group-item.list-group-item-light {
    background: var(--light); }
  .list-group-item.list-group-item-dark {
    background: var(--dark);
    color: var(--light); }

.list-group-item.disabled,
.list-group-item:disabled {
  color: var(--muted);
  background: var(--foreground); }

.list-group-item:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.list-group-item:last-child {
  border-bottom-right-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md); }

.list-group-horizontal > .list-group-item:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
  border-top-right-radius: initial;
  border-bottom-right-radius: initial; }

.list-group-horizontal > .list-group-item:last-child {
  border-top-left-radius: initial;
  border-bottom-left-radius: initial;
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md); }

@media (min-width: 576px) {
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial; }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); } }

@media (min-width: 768px) {
  .list-group-horizontal-md > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial; }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); } }

@media (min-width: 992px) {
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial; }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); } }

@media (min-width: 1200px) {
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial; }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); } }

@media (min-width: 1400px) {
  .list-group-horizontal-xxl > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial; }
  .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); } }

.card.hover-border-primary {
  cursor: pointer; }
  .card.hover-border-primary:hover:after {
    box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5), 0 4px 13px rgba(0, 0, 0, 0.04) !important;
    content: '';
    display: block;
    height: 100%;
    position: absolute;
    top: 0;
    width: 100%;
    border-radius: var(--border-radius-lg);
    z-index: 0;
    pointer-events: none; }

.hover-img-scale-up {
  overflow: hidden;
  backface-visibility: hidden;
  transform: scale(1); }
  .hover-img-scale-up img.scale {
    transition: transform var(--transition-time); }
  .hover-img-scale-up:hover img.scale {
    transform: scale(1.1); }

.hover-img-scale-down {
  overflow: hidden;
  backface-visibility: hidden;
  transform: scale(1); }
  .hover-img-scale-down img.scale {
    transition: transform var(--transition-time);
    transform: scale(1.15); }
  .hover-img-scale-down:hover img.scale {
    transform: scale(1.05); }

.hover-reveal .reveal-content {
  opacity: 0;
  transition: opacity var(--transition-time); }

.hover-reveal:hover .reveal-content {
  opacity: 1; }

@media (max-width: 991.98px) {
  .hover-reveal .reveal-content {
    opacity: 1; } }

/*
*
* Dropdown
*
* Bootstrap dropdown styles.
*
*/
.dropdown-menu {
  line-height: 1.3;
  background-color: var(--foreground);
  border-radius: var(--border-radius-md);
  border-color: var(--separator);
  color: var(--body);
  font-family: var(--font);
  font-size: 1em; }
  .dropdown-menu.shadow {
    border: initial;
    box-shadow: 0 4px 13px rgba(0, 0, 0, 0.07) !important; }
  .dropdown-menu ul {
    padding-left: 0;
    list-style: none;
    margin-bottom: 0; }
    .dropdown-menu ul li {
      padding: 3px 0 3px 0; }
  .dropdown-menu a,
  .dropdown-menu .dropdown-item,
  .dropdown-menu .nav-link {
    text-decoration: initial;
    color: var(--body);
    background: initial;
    border: initial !important;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem; }
    .dropdown-menu a:hover, .dropdown-menu a.active,
    .dropdown-menu .dropdown-item:hover,
    .dropdown-menu .dropdown-item.active,
    .dropdown-menu .nav-link:hover,
    .dropdown-menu .nav-link.active {
      background-color: initial;
      color: var(--primary) !important; }

.nav-tabs .dropdown-menu {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.dropdown-item.disabled,
.dropdown-item:disabled {
  color: var(--body);
  opacity: 0.5; }

.dropdown-toggle-split::after {
  margin-bottom: 0; }

.dropup .dropdown-toggle-split::after,
.dropend .dropdown-toggle-split::after,
.dropend .dropdown-toggle::after {
  margin-bottom: 1px; }

.dropstart .caret-absolute {
  position: relative; }
  .dropstart .caret-absolute:before {
    position: absolute;
    left: 13px;
    top: calc(50% - 3px); }

.dropend .caret-absolute {
  position: relative; }
  .dropend .caret-absolute:after {
    position: absolute;
    right: 13px;
    top: calc(50% - 3px); }

.dropdown-toggle::after,
.dropup .dropdown-toggle::after,
.dropstart .dropdown-toggle::before,
.dropend .dropdown-toggle::after {
  width: 5px;
  height: 5px;
  border: initial;
  transform: rotate(45deg);
  border-top: 1px solid;
  border-right: 1px solid;
  margin-top: initial;
  vertical-align: middle;
  margin-bottom: 2px; }

.dropstart .dropdown-toggle::before {
  transform: rotate(225deg); }

.dropstart .dropdown-toggle.show::before {
  transform: rotate(135deg); }

.dropup .dropdown-toggle.show::after {
  transform: rotate(-45deg); }

.dropdown-toggle.show::after {
  transform: rotate(135deg); }

.dropdown-toggle[data-bs-toggle='collapse']:not(.collapsed)::after {
  transform: rotate(135deg); }

.dropdown-menu.dropdown-menu-sm {
  min-width: 7rem;
  padding: 0.5rem 0; }
  .dropdown-menu.dropdown-menu-sm .dropdown-item,
  .dropdown-menu.dropdown-menu-sm .nav-link,
  .dropdown-menu.dropdown-menu-sm a {
    padding: 0.4rem 1.25rem;
    font-size: 0.9em; }

.dropdown-header {
  font-size: 0.8em; }

.dropdown-divider {
  border-color: var(--separator); }

.dropdown-submenu.dropend .dropdown-menu {
  margin-left: 4px !important; }

.dropdown-submenu.dropstart .dropdown-menu {
  margin-right: 4px !important; }

[x-placement^='bottom-'] .dropdown-submenu .dropdown-menu,
.navbar .dropdown-submenu .dropdown-menu {
  bottom: auto;
  margin-top: calc(-0.5rem - 1px) !important; }

[x-placement^='top-'] .dropdown-submenu .dropdown-menu {
  top: auto;
  bottom: 0;
  margin-bottom: calc(-0.5rem - 1px) !important; }

.dropdown-submenu.dropend > .dropdown-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%; }

.dropdown-header,
.dropdown-item,
.dropdown-item-text {
  padding: 0.5rem 1.5rem; }

.dropend.dropdown-submenu .dropdown-menu {
  bottom: initial; }

/*
*
* Form
*
* Styles for Bootstrap forms, additional layouts and controls.
*
*/
.form-control,
.form-select,
.custom-select {
  font-size: 1em;
  height: auto;
  min-height: var(--input-height);
  color: var(--body);
  box-shadow: initial !important;
  background-color: var(--foreground);
  border-color: var(--separator);
  border-radius: var(--border-radius-md);
  filter: none;
  margin-bottom: 6px;
  transition: border-color 0.15s ease-in-out; }
  .form-control.borderless,
  .form-select.borderless,
  .custom-select.borderless {
    border: initial;
    box-shadow: initial; }
  .form-control:focus,
  .form-select:focus,
  .custom-select:focus {
    background-color: var(--foreground);
    color: var(--body);
    border-color: rgba(var(--primary-rgb), 1); }
  .form-control.shadow,
  .form-select.shadow,
  .custom-select.shadow {
    border: initial;
    box-shadow: 0 4px 13px rgba(0, 0, 0, 0.07) !important; }

.form-text {
  color: var(--muted); }

.form-select {
  padding-right: 2rem; }

textarea.form-control {
  height: initial; }

.form-label,
.col-form-label {
  color: var(--alternate); }

.form-control-muted {
  border-color: var(--muted); }
  .form-control-muted:focus {
    border-color: var(--alternate); }

.form-control-separator {
  border-color: var(--separator); }
  .form-control-separator:focus {
    border-color: var(--alternate); }

.form-control-sm,
.custom-select-sm {
  min-height: 30px;
  font-size: 0.9em;
  padding: 0.25rem 0.75rem;
  line-height: 1.25; }

.form-control-lg,
.custom-select-lg {
  height: 44px;
  font-size: 1.25em;
  line-height: 1.5;
  padding: 0.375rem 0.75rem 0.375rem 0.75rem;
  margin-bottom: 6px;
}

.form-control-xl {
  font-size: 1.6em;
  height: 56px; }

.search-input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 15px;
  pointer-events: none;
  width: 17px;
  height: 17px; }

.custom-select {
  background: var(--foreground) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23777777' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px; }

.search-input-container {
  position: relative;
  border-radius: var(--border-radius-md); }
  .search-input-container .search-magnifier-icon,
  .search-input-container .search-delete-icon {
    color: var(--muted);
    position: absolute;
    right: 0;
    top: 0;
    width: 40px;
    height: 100%;
    text-align: center;
    cursor: pointer; }
    .search-input-container .search-magnifier-icon:hover,
    .search-input-container .search-delete-icon:hover {
      color: var(--primary); }
  .search-input-container .search-magnifier-icon svg,
  .search-input-container .search-delete-icon svg {
    width: 17px;
    height: 17px;
    margin-top: 9px; }
  .search-input-container input {
    background: transparent !important;
    width: calc(100% - 25px);
    border: initial; }
  .search-input-container.search-sm .search-magnifier-icon,
  .search-input-container.search-sm .search-delete-icon {
    width: 28px; }
    .search-input-container.search-sm .search-magnifier-icon svg,
    .search-input-container.search-sm .search-delete-icon svg {
      width: 15px;
      height: 15px;
      margin-top: 4px; }

.form-control:disabled,
.form-control[readonly] {
  background: rgba(var(--separator-rgb), 0.5) !important;
  border-color: rgba(var(--separator-rgb), 0.5) !important;
  color: var(--muted);
  -webkit-text-fill-color: var(--muted) !important; }

.form-control:disabled ~ span {
  background: transparent !important; }

.filled {
  position: relative; }
  .filled .form-control {
    min-height: 44px;
    border: 1px solid transparent;
    background: var(--background-light);
    padding-left: 45px; }
    .filled .form-control:focus {
      border-color: rgba(var(--primary-rgb), 1);
      background: initial; }
    .filled .form-control:disabled, .filled .form-control[readonly] {
      background: rgba(var(--separator-rgb), 0.5) !important;
      color: var(--muted); }
      .filled .form-control:disabled > svg, .filled .form-control[readonly] > svg {
        color: rgba(var(--alternate-rgb), 0.25); }
  .filled .form-control-lg {
    min-height: 52px; }
  .filled .form-control-sm {
    min-height: var(--input-height); }
  .filled > svg {
    position: absolute;
    top: 12px;
    left: 16px;
    color: rgba(var(--alternate-rgb), 0.5);
    z-index: 1; }
  .filled.lg > svg {
    top: 15px; }
  .filled.sm > svg {
    top: 9px;
    font-size: 14px;
    width: 17px;
    height: 17px; }
  .filled textarea {
    padding-top: 0.7rem;
    padding-bottom: 0.7rem; }
  .filled > textarea ~ svg {
    margin-top: 0;
    top: 14px; }
  .filled.custom-control-container {
    min-height: 44px;
    border: 1px solid transparent;
    background: var(--background-light);
    padding-left: 45px;
    border-radius: var(--border-radius-md);
    padding-top: 0.75rem;
    padding-bottom: 0.25rem;
    padding-right: 0.75rem; }
  .filled .form-check {
    margin-bottom: initial;
    margin-top: 0; }

*[disabled] .filled i {
  opacity: 0.3; }

.top-label .form-control,
.top-label .bootstrap-tagsinput {
  padding: 1.5rem 0.75rem 0.25rem 0.75rem; }

.top-label {
  display: block;
  position: relative; }

.top-label label:not(.form-check-label),
.top-label > span:last-of-type {
  position: absolute;
  cursor: text;
  font-size: 0.8em !important;
  line-height: 1.1rem !important;
  opacity: 1;
  top: 0.5rem;
  left: 0.75rem;
  z-index: 1;
  line-height: 1;
  padding: 0 1px;
  background: var(--foreground); }

.top-label label:not(.form-check-label)::after,
.top-label > span::after {
  content: ' ';
  display: block;
  position: absolute;
  height: 2px;
  top: 50%;
  left: -0.2em;
  right: -0.2em;
  z-index: -1; }

.input-group .top-label {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center; }

.top-label textarea ~ span {
  left: 0 !important;
  top: 0 !important;
  margin-top: 1px;
  margin-left: 1px;
  padding-left: 0.75rem !important;
  padding-top: calc(0.5rem - 1px) !important;
  width: calc(100% - 2px);
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.top-label .form-control,
.top-label .bootstrap-tagsinput {
  height: auto;
  min-height: 52px; }

.top-label textarea.form-control {
  min-height: 52px;
  height: initial; }

.top-label select.form-control:not([size]):not([multiple]) {
  height: auto;
  min-height: 52px;
  padding: 1.7rem 0.75rem 0.5rem 0.5rem; }

.top-label label:not(.form-check-label),
.top-label > span {
  color: var(--muted); }

.top-label .form-control.form-control-lg,
.top-label .bootstrap-tagsinput.form-control-lg {
  min-height: 62px;
  height: auto;
  padding: 1.8rem 0.75rem 0.5rem 0.75rem; }

.top-label textarea.form-control.form-control-lg {
  min-height: 62px;
  height: auto;
  padding: 1.8rem 0.75rem 0.5rem 0.75rem; }

.top-label select.form-control.form-control-lg:not([size]):not([multiple]) {
  min-height: 62px;
  height: auto;
  padding: 1.8rem 0.75rem 0.5rem 0.75rem; }

.top-label .form-control.form-control-sm,
.top-label .bootstrap-tagsinput.form-control-sm {
  min-height: 44px;
  height: auto;
  padding: 1.4rem 0.75rem 0.25rem 0.75rem; }

.top-label textarea.form-control.form-control-sm {
  min-height: 44px;
  height: initial;
  padding: 1.4rem 0.75rem 0.25rem 0.75rem; }

.top-label select.form-control.form-control-sm:not([size]):not([multiple]) {
  min-height: 44px;
  height: auto;
  padding: 1.4rem 0.75rem 0.25rem 0.75rem; }

.top-label .form-check {
  margin-left: 4px;
  margin-bottom: initial;
  margin-top: 0; }

.top-label.custom-control-container {
  border: 1px solid var(--separator) !important;
  background: var(--foreground) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--alternate) !important;
  padding: 1.5rem 0.75rem 0.1rem 0.75rem !important;
  height: auto;
  min-height: 52px; }

input:-webkit-autofill,
input:-webkit-autofill:hover,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
select:-webkit-autofill,
select:-webkit-autofill:hover {
  -webkit-text-fill-color: var(--body);
  border-color: var(--separator) !important;
  box-shadow: 0 0 0 1000px var(--foreground) inset !important;
  -webkit-box-shadow: 0 0 0 1000px var(--foreground) inset !important;
  background-color: initial !important;
  background-clip: content-box !important; }

input:-webkit-autofill ~ label,
textarea:-webkit-autofill ~ label,
select:-webkit-autofill ~ label {
  transition: initial; }

input:-webkit-autofill:focus,
textarea:-webkit-autofill:focus,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--body);
  border-color: rgba(var(--primary-rgb), 1) !important;
  box-shadow: 0 0 0 1000px var(--foreground) inset !important;
  -webkit-box-shadow: 0 0 0 1000px var(--foreground) inset !important;
  background-color: initial !important;
  background-clip: content-box !important; }

.filled input:-webkit-autofill,
.filled input:-webkit-autofill:hover,
.filled textarea:-webkit-autofill,
.filled textarea:-webkit-autofill:hover,
.filled select:-webkit-autofill,
.filled select:-webkit-autofill:hover,
.filled input:-webkit-autofill:focus,
.filled textarea:-webkit-autofill:focus,
.filled select:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--body);
  border-color: var(--separator) !important;
  box-shadow: 0 0 0 1000px var(--separator-light) inset !important;
  -webkit-box-shadow: 0 0 0 1000px var(--foreground) inset !important;
  background-color: initial !important;
  background-clip: content-box !important; }

.form-check-input[type='checkbox'] {
  border-radius: var(--border-radius-sm); }

.form-switch.form-check {
  padding-left: 2.5em; }
  .form-switch.form-check .form-check-input {
    margin-left: -2.5em; }

.form-switch .form-check-input {
  border-radius: var(--border-radius-md); }

.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,0.25%29'/%3e%3c/svg%3e"); }

.form-switch .form-check-input:checked:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e"); }

.form-check.custom-icon .content {
  display: flex !important;
  flex-direction: column; }

.form-check.custom-icon .form-check-label {
  padding-top: initial; }
  .form-check.custom-icon .form-check-label::before {
    width: 26px;
    min-width: 26px;
    height: 26px;
    left: -40px;
    top: 0;
    border-radius: var(--border-radius-xl);
    border-color: var(--muted) !important;
    opacity: 0.3; }

.form-check.custom-icon .form-check-input {
  width: 25px;
  height: 25px;
  background: initial !important;
  font-size: 16px;
  text-align: center;
  padding-top: 1px;
  border-radius: var(--border-radius-xl);
  border-color: var(--muted) !important;
  color: var(--muted) !important;
  opacity: 0.3;
  margin-top: 0;
  margin-right: 1rem; }

.form-check.custom-icon .form-check-input:checked {
  border-color: var(--primary) !important;
  background: initial !important;
  opacity: 1; }
  .form-check.custom-icon .form-check-input:checked:after {
    font-family: 'Plex-Regular' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    color: var(--primary) !important;
    opacity: 1;
    content: '\e913'; }

.form-check.custom-icon .form-check-input[type='radio']:checked:after {
  content: '\e922'; }

.form-check.custom-card:hover .form-check-label::before,
.form-check.custom-card .form-check-input:checked ~ .form-check-label::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-md);
  box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5) !important; }

.form-check.custom-card .form-check-input {
  border: initial;
  background-color: initial !important; }

.form-check.custom-card .form-check-input:checked {
  border: 1px solid var(--primary);
  background-color: var(--primary) !important; }

.form-check .custom-border {
  border: initial !important;
  box-shadow: initial !important; }
  .form-check .custom-border:hover .form-check-label::before,
  .form-check .custom-border .form-check-input:checked ~ .form-check-label::before {
    box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5) !important; }
  .form-check .custom-border.form-check-label::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-md);
    box-shadow: inset 0 0 0 1px var(--separator); }

.custom-icon input[type='radio'] .form-check-label::after {
  content: '\e9d3'; }

/* Input Spinner */
.input-group.spinner {
  z-index: 0; }

.input-group.spinner .input-group-text {
  flex-direction: column;
  justify-content: center;
  padding-top: 0;
  padding-bottom: 0;
  padding: 0; }

.input-group.spinner .input-group-text .spin-up,
.input-group.spinner .input-group-text .spin-down {
  display: flex;
  background: none;
  border: none;
  padding: 0;
  height: 40%;
  padding: 0 1rem;
  display: flex;
  align-items: center; }
  .input-group.spinner .input-group-text .spin-up.single,
  .input-group.spinner .input-group-text .spin-down.single {
    height: 100%;
    color: var(--alternate); }
  .input-group.spinner .input-group-text .spin-up:hover .arrow,
  .input-group.spinner .input-group-text .spin-down:hover .arrow {
    border-color: var(--primary); }
  .input-group.spinner .input-group-text .spin-up:hover,
  .input-group.spinner .input-group-text .spin-down:hover {
    color: var(--primary); }
  .input-group.spinner .input-group-text .spin-up .arrow,
  .input-group.spinner .input-group-text .spin-down .arrow {
    border: initial;
    border-top: 1px solid var(--alternate);
    border-right: 1px solid var(--alternate);
    width: 5px;
    height: 5px;
    margin: 0 auto; }

.input-group.spinner .input-group-text .spin-up .arrow {
  transform: rotate(-45deg); }

.input-group.spinner .input-group-text .spin-down .arrow {
  transform: rotate(135deg); }

.form-check.checked-line-through .form-check-input:checked ~ .form-check-label {
  text-decoration: line-through; }
  .form-check.checked-line-through .form-check-input:checked ~ .form-check-label span {
    text-decoration: line-through; }

.form-check.checked-opacity-100 .form-check-input:checked ~ .form-check-label > span:first-of-type,
.form-check.checked-opacity-100 .form-check-input:checked ~ .form-check-label > div:first-of-type {
  opacity: 1; }

.form-check.checked-opacity-75 .form-check-input:checked ~ .form-check-label > span:first-of-type,
.form-check.checked-opacity-75 .form-check-input:checked ~ .form-check-label > div:first-of-type {
  opacity: 0.75; }

.form-check.checked-opacity-50 .form-check-input:checked ~ .form-check-label > span:first-of-type,
.form-check.checked-opacity-50 .form-check-input:checked ~ .form-check-label > div:first-of-type {
  opacity: 0.5; }

.form-check.checked-opacity-25 .form-check-input:checked ~ .form-check-label > span:first-of-type,
.form-check.checked-opacity-25 .form-check-input:checked ~ .form-check-label > div:first-of-type {
  opacity: 0.25; }

.form-check.checked-opacity-0 .form-check-input:checked ~ .form-check-label > span:first-of-type,
.form-check.checked-opacity-0 .form-check-input:checked ~ .form-check-label > div:first-of-type {
  opacity: 0; }

.form-check.unchecked-opacity-100 .form-check-input:not(:checked) ~ .form-check-label > span:first-of-type,
.form-check.unchecked-opacity-100 .form-check-input:not(:checked) ~ .form-check-label > div:first-of-type {
  opacity: 1; }

.form-check.unchecked-opacity-75 .form-check-input:not(:checked) ~ .form-check-label > span:first-of-type,
.form-check.unchecked-opacity-75 .form-check-input:not(:checked) ~ .form-check-label > div:first-of-type {
  opacity: 0.75; }

.form-check.unchecked-opacity-50 .form-check-input:not(:checked) ~ .form-check-label > span:first-of-type,
.form-check.unchecked-opacity-50 .form-check-input:not(:checked) ~ .form-check-label > div:first-of-type {
  opacity: 0.5; }

.form-check.unchecked-opacity-25 .form-check-input:not(:checked) ~ .form-check-label > span:first-of-type,
.form-check.unchecked-opacity-25 .form-check-input:not(:checked) ~ .form-check-label > div:first-of-type {
  opacity: 0.25; }

.form-check.unchecked-opacity-0 .form-check-input:not(:checked) ~ .form-check-label > span:first-of-type,
.form-check.unchecked-opacity-0 .form-check-input:not(:checked) ~ .form-check-label > div:first-of-type {
  opacity: 0; }

textarea {
  resize: none; }

.form-check {
  margin-bottom: 0.25rem;
  padding-left: 1.75em;
  line-height: 1.5; }

.form-check-input[type='checkbox']:indeterminate {
  background-color: var(--primary);
  border-color: var(--primary); }

.form-check-input,
.form-check-input label,
.form-check-input input {
  outline: initial !important;
  box-shadow: initial !important; }

.form-check-input {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.form-check .form-check-input {
  border-color: var(--muted) !important;
  background-color: initial;
  margin-top: 0.2em;
  margin-left: -1.75em; }

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary) !important; }

.form-check-label {
  color: var(--body);
  margin-top: 1px; }

.form-floating > label {
  color: var(--alternate);
  padding: 0.85rem 0.75rem; }

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  color: var(--muted);
  opacity: 1; }

.form-floating > textarea.form-control ~ label {
  transition: transform 0.1s ease-in-out, padding 0.1s ease-in-out, background-color 0s ease-in-out, height 0.1s ease-in-out; }

.form-floating > textarea.form-control:not(:placeholder-shown) ~ label,
.form-floating > textarea.form-control:focus ~ label {
  background-color: var(--foreground);
  padding-top: 0.25rem;
  padding-bottom: 0.05rem;
  border-top-left-radius: var(--border-radius-md);
  height: auto;
  -webkit-transform: scale(0.85) translateY(1px) translateX(0.15rem);
  transform: scale(0.85) translateY(1px) translateX(0.15rem);
  transition: transform 0.1s ease-in-out, padding 0.1s ease-in-out, background-color 0.1s ease-in-out 0.1s, height 0.1s ease-in-out; }

.form-floating > .form-control,
.form-floating > .form-select {
  height: auto;
  min-height: 52px;
  padding: 0.85rem 0.75rem; }

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.45rem;
  padding-bottom: 0.25rem; }

.form-floating .form-control.form-control-lg {
  min-height: 62px;
  height: auto;
  padding: 1.2rem 0.75rem 1.2rem 0.75rem; }
  .form-floating .form-control.form-control-lg ~ label {
    padding: 1.2rem 0.75rem 1.2rem 0.75rem; }
  .form-floating .form-control.form-control-lg:focus, .form-floating .form-control.form-control-lg:not(:placeholder-shown) {
    padding-top: 2rem;
    padding-bottom: 0.4rem; }

.form-floating .form-control.form-control-sm {
  min-height: 44px;
  height: auto;
  padding: 0.7rem 0.75rem 0.8rem 0.75rem; }
  .form-floating .form-control.form-control-sm ~ label {
    padding: 0.7rem 0.75rem 0.8rem 0.75rem; }
  .form-floating .form-control.form-control-sm:focus, .form-floating .form-control.form-control-sm:not(:placeholder-shown) {
    padding-top: 1.4rem;
    padding-bottom: 0.2rem; }

/*
*
* Grid
*
* Bootstrap auto column fix.
*
*/
.col,
.col-sm,
.col-md,
.col-lg,
.col-xl {
  min-width: 0; }

/*
*
* Input Group
*
* Bootstrap input group styles.
*
*/
.input-group-text {
  border-radius: var(--border-radius-md);
  font-size: 1em;
  padding: 0.25rem 0.75rem 0.35rem 0.75rem;
  background-color: var(--foreground);
  border-color: var(--separator);
  color: var(--body); }

.input-group .form-control {
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md); }

.input-group-lg > .form-control,
.input-group-lg > .custom-select {
  border-radius: var(--border-radius-md); }

.input-group .top-label .form-control {
  width: 100%; }

.input-group .top-label:not(:last-child),
.input-group .top-label:not(:last-child) .form-control {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 0; }

.input-group .top-label:not(:first-child),
.input-group .top-label:not(:first-child) .form-control {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.custom-file-label,
.custom-file,
.custom-file-input {
  height: var(--input-height);
  border-color: var(--separator);
  background: var(--foreground); }

.custom-file-label {
  border-radius: var(--border-radius-md); }

.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.custom-file-label::after {
  border-radius: var(--border-radius-md);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  height: 34px;
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--light-text); }

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  height: 44px;
  font-size: 1.25em;
  line-height: 1.5; }

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  border-radius: var(--border-radius-md);
  font-size: 0.9em;
  padding: 0.25rem 0.75rem;
  height: 28px;
  min-height: 28px; }

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  border-radius: var(--border-radius-md); }

.input-group-text .form-check-input {
  margin-top: 1px !important; }

.input-group .form-control[type='file'] {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  line-height: 1.6; }

input[type='file']::file-selector-button {
  background: var(--foreground);
  color: var(--body); }

/*
*
* Modal
*
* Bootstrap modal styles.
*
*/
.modal-header,
.modal-body,
.modal-footer {
  padding: var(--card-spacing); }

.modal-content {
  border-radius: var(--border-radius-lg);
  background: var(--foreground); }

.modal-header {
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
  border-color: var(--separator); }

.modal-footer {
  border-bottom-left-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--border-radius-lg);
  border-color: var(--separator); }

.modal-under-nav .modal-dialog {
  margin-top: var(--nav-size-slim); }

.modal-close-out .btn-close {
  position: absolute;
  right: -50px;
  top: 0;
  background: var(--foreground);
  opacity: 1;
  color: var(--primary);
  border-radius: var(--border-radius-md);
  width: var(--input-height);
  height: var(--input-height);
  margin: 0;
  padding: 0; }
  @media (max-width: 767.98px) {
    .modal-close-out .btn-close {
      right: 0;
      top: -50px; } }

.modal-close-out .modal-dialog:not(.modal-dialog-centered) {
  margin-top: var(--nav-size-slim); }

.modal-right {
  padding-right: 0 !important; }
  .modal-right .modal-dialog {
    margin: 0 auto;
    margin-right: 0;
    margin-top: 0;
    margin-bottom: 0;
    height: 100%;
    max-width: 330px; }
  .modal-right .modal-body {
    height: 100% !important; }
  .modal-right .modal-content {
    min-height: 100%;
    border: initial;
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg); }
  .modal-right .modal-header {
    flex: 0 0 var(--nav-size-slim);
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-top-left-radius: var(--border-radius-lg); }
  .modal-right .modal-footer {
    justify-content: center;
    flex-grow: 0;
    flex-shrink: 0;
    border-bottom-left-radius: var(--border-radius-lg); }
  .modal-right.modal.fade .modal-dialog {
    transform: translate(25%, 0); }
  .modal-right.modal.show .modal-dialog {
    transform: translate(0, 0); }
  .modal-right.large .modal-dialog {
    max-width: 530px; }

.modal-left {
  padding-left: 0 !important; }
  .modal-left .modal-dialog {
    margin: 0 auto;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
    height: 100%;
    max-width: 330px; }
  .modal-left .modal-body {
    height: 100% !important; }
  .modal-left .modal-content {
    min-height: 100%;
    border: initial;
    border-radius: initial;
    border-top-right-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg); }
  .modal-left .modal-header {
    flex: 0 0 var(--nav-size-slim);
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-top-right-radius: var(--border-radius-lg); }
  .modal-left .modal-footer {
    justify-content: center;
    flex-grow: 0;
    flex-shrink: 0;
    border-bottom-right-radius: var(--border-radius-lg); }
  .modal-left.modal.fade .modal-dialog {
    transform: translate(-25%, 0); }
  .modal-left.modal.show .modal-dialog {
    transform: translate(0, 0); }
  .modal-left.large .modal-dialog {
    max-width: 530px; }

.modal-dialog-scrollable.long .modal-body {
  overflow: initial; }
  .modal-dialog-scrollable.long .modal-body .scroll,
  .modal-dialog-scrollable.long .modal-body .scroll-track-visible {
    max-height: calc(100vh - 25rem); }

.modal-dialog-scrollable.full {
  max-height: 100% !important; }
  .modal-dialog-scrollable.full .modal-content {
    height: 100%;
    min-height: unset; }
  .modal-dialog-scrollable.full .modal-body {
    overflow: initial;
    max-height: 100% !important; }
    .modal-dialog-scrollable.full .modal-body .scroll,
    .modal-dialog-scrollable.full .modal-body .scroll-track-visible {
      max-height: 100%;
      height: 100%; }

.modal-dialog-scrollable.short .modal-body {
  overflow: initial; }
  .modal-dialog-scrollable.short .modal-body .scroll,
  .modal-dialog-scrollable.short .modal-body .scroll-track-visible {
    max-height: calc(50vh); }

.modal.modal-right .os-content {
  padding: 0 !important; }

.modal.modal-right .os-host-resize-disabled.os-host-scrollbar-horizontal-hidden > .os-scrollbar-vertical {
  padding-right: 0;
  padding-left: 4px; }

.modal.modal-left .os-content {
  padding: 0 !important; }

.modal.modal-left .os-host-resize-disabled.os-host-scrollbar-horizontal-hidden > .os-scrollbar-vertical {
  padding-right: 4px;
  padding-left: 0; }

.modal-semi-full {
  max-width: 90%;
  height: 95%;
  margin-left: auto;
  margin-right: auto; }
  .modal-semi-full .modal-content {
    height: 100%; }

/*
*
* Nav
*
* Bootstrap navigation styles.
*
*/
.page-item {
  margin: 0 5px;
  text-align: center; }

.page-link {
  outline: initial !important;
  box-shadow: initial !important;
  border: initial;
  padding: 8px 9px;
  line-height: 1.5;
  font-size: 1em;
  width: var(--input-height);
  height: var(--input-height);
  color: var(--primary);
  border-radius: var(--border-radius-md) !important;
  background: var(--foreground); }
  .page-link:focus {
    background: var(--foreground);
    color: var(--primary); }
  .page-link:hover, .page-link:hover:focus {
    background: var(--light);
    color: var(--light-text); }
  .page-link svg {
    width: 15px;
    height: 15px;
    vertical-align: top;
    margin-top: 2px;
    margin-left: 2px; }
  .page-link i {
    margin-left: 1px; }

.page-item.active .page-link {
  background: var(--primary);
  box-shadow: initial !important; }

.page-item.disabled .page-link {
  opacity: 0.5;
  color: var(--primary);
  background: var(--foreground);
  box-shadow: initial !important; }

.page-item:first-child .page-link,
.page-item:last-child .page-link {
  border-radius: var(--border-radius-md); }

.pagination.bordered .page-item .page-link {
  color: var(--primary);
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  border-radius: var(--border-radius-md);
  background-color: transparent; }
  .pagination.bordered .page-item .page-link:hover {
    background: var(--primary);
    color: var(--light-text); }

.pagination.bordered .page-item.active .page-link {
  box-shadow: initial !important;
  background: var(--primary);
  color: var(--light-text); }

.pagination.semibordered .page-item .page-link:hover {
  background-color: transparent;
  color: var(--primary);
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  border-radius: var(--border-radius-md); }

.pagination.semibordered .page-item.active .page-link {
  background-color: transparent;
  color: var(--primary);
  box-shadow: inset 0 0 0 1px var(--primary) !important;
  border-radius: var(--border-radius-md); }

.pagination.pagination-xl .page-link {
  line-height: 1;
  font-size: 1em;
  height: 44px;
  padding: 14px 26px;
  width: 65px; }
  .pagination.pagination-xl .page-link svg {
    margin-top: 0; }

.pagination.pagination-lg .page-link {
  font-size: 1em;
  line-height: 18px;
  width: 50px;
  padding: 8px 15px; }

.pagination.pagination-sm .page-link {
  font-size: 0.9em;
  height: 30px;
  width: 30px;
  line-height: 1;
  padding: 9px 3px; }
  .pagination.pagination-sm .page-link svg {
    margin-top: -1px; }

/* Navigation */
.nav-tabs {
  border-color: var(--separator); }

.nav-link {
  font-family: var(--font);
  font-size: 1em;
  line-height: 1;
  border: initial;
  box-shadow: initial !important;
  transition: all var(--transition-time-short);
  transition-property: color, background-color, background-image, background;
  color: var(--body); }
  .nav-link:hover, .nav-link:active, .nav-link.active {
    color: var(--primary); }

.nav:not(.nav-pills):not(.nav-tabs-line) .nav-link {
  padding-top: 0.6rem;
  padding-bottom: 0.6rem; }

.nav-tabs .nav-link {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md); }

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--body);
  background: var(--foreground); }

.nav-item.disabled .nav-link {
  opacity: 0.5; }

.card-header-tabs .nav-link {
  border-bottom-left-radius: initial;
  border-bottom-right-radius: initial; }

.card-header-tabs .nav-link:not(a) {
  color: var(--body); }

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: var(--primary); }

.nav-pills .dropdown-menu .nav-item .nav-link.active {
  background: initial !important; }

.card-header-tabs-border > li > .nav-link,
.card-header-tabs-border > div > .nav-link {
  border: initial;
  border-radius: initial;
  position: relative;
  padding-top: 15px; }

.card-header-tabs-border .nav-link.active::before,
.card-header-tabs-border .nav-item.show .nav-link::before {
  content: ' ';
  border-radius: 10px;
  position: absolute;
  width: 100%;
  height: 5px;
  left: 0;
  top: 0;
  background: var(--primary);
  color: var(--light-text); }

.nav-pills .nav-link {
  line-height: 1.5;
  font-size: 1em;
  height: var(--input-height);
  border-radius: var(--border-radius-md); }

.card .card-header-tabs {
  margin-bottom: calc(var(--card-spacing) * -1);
  margin-top: calc(var(--card-spacing) * -0.25); }

.card-header-pills,
.card-header-tabs {
  margin-left: initial;
  margin-right: initial; }

.card .card-header-tabs.nav-tabs-line {
  margin-top: calc(var(--card-spacing) * -1);
  margin-left: calc(var(--card-spacing) / 2 * -1);
  margin-right: calc(var(--card-spacing) / 2 * -1);
  margin-bottom: initial; }

.nav-tabs-line {
  border: initial; }
  .nav-tabs-line > li > .nav-link,
  .nav-tabs-line > div > .nav-link {
    border: initial;
    position: relative;
    padding-top: 20px;
    color: var(--body); }
    .nav-tabs-line > li > .nav-link:hover, .nav-tabs-line > li > .nav-link.active,
    .nav-tabs-line > div > .nav-link:hover,
    .nav-tabs-line > div > .nav-link.active {
      color: var(--primary); }
  .nav-tabs-line > li > .nav-link.active,
  .nav-tabs-line > div > .nav-link.active {
    border: initial; }
    .nav-tabs-line > li > .nav-link.active::before,
    .nav-tabs-line > div > .nav-link.active::before {
      content: ' ';
      background: var(--primary);
      color: var(--foreground);
      border-radius: 10px;
      position: absolute;
      width: calc(100%);
      height: 3px;
      top: 0;
      left: 50%;
      transform: translateX(-50%); }
  .nav-tabs-line .dropdown-menu .nav-link.active {
    border: initial; }
    .nav-tabs-line .dropdown-menu .nav-link.active::before {
      background: initial; }

.nav-tabs-title {
  margin: initial; }
  .nav-tabs-title .nav-item {
    margin: initial; }
  .nav-tabs-title > li > .nav-link,
  .nav-tabs-title > div > .nav-link {
    height: var(--small-title-height);
    border: initial;
    background: initial;
    position: relative;
    color: var(--alternate);
    font-family: var(--font-heading);
    font-weight: 400;
    margin-bottom: 0;
    font-size: 1em;
    padding: 12px;
    padding-top: initial !important;
    padding-bottom: initial !important; }
    .nav-tabs-title > li > .nav-link:hover, .nav-tabs-title > li > .nav-link.active,
    .nav-tabs-title > div > .nav-link:hover,
    .nav-tabs-title > div > .nav-link.active {
      color: var(--primary); }
  .nav-tabs-title > li > .nav-link.active,
  .nav-tabs-title > div > .nav-link.active {
    border: initial;
    background: initial;
    color: var(--primary); }
  .nav-tabs-title > li:first-of-type > .nav-link,
  .nav-tabs-title > div:first-of-type > .nav-link {
    padding-left: initial; }
  .nav-tabs-title > li:last-of-type > .nav-link,
  .nav-tabs-title > div:last-of-type > .nav-link {
    padding-right: initial; }

.nav-tabs-line-title {
  border-bottom: 1px solid rgba(var(--separator-rgb), 0.3) !important;
  margin-bottom: calc(var(--small-title-height) / 3); }
  .nav-tabs-line-title > li {
    margin-bottom: calc(-1px - calc(var(--small-title-height) / 3)) !important;
    padding-right: 12px;
    padding-left: 12px; }
  .nav-tabs-line-title > li > .nav-link {
    padding-right: initial;
    padding-left: initial;
    padding-top: 1px !important; }
  .nav-tabs-line-title > li > .nav-link.active:after {
    content: ' ';
    left: 0;
    bottom: calc(-1px + var(--small-title-height) / 3);
    position: absolute;
    width: calc(100% + 1px);
    height: 1px;
    background: var(--primary); }
  .nav-tabs-line-title > li:first-of-type,
  .nav-tabs-line-title > div:first-of-type {
    padding-left: initial; }

.nav.horizontal-padding-0 > li:first-child a,
.nav.horizontal-padding-0 > div:first-child a,
.nav.horizontal-padding-0 > a:first-child {
  padding-left: 0; }

.nav.horizontal-padding-0 > li:last-child a,
.nav.horizontal-padding-0 > div:last-child a,
.nav.horizontal-padding-0 > a:last-child {
  padding-right: 0; }

/* Page Title */
.page-title-container {
  margin-bottom: var(--title-spacing); }
  .page-title-container h1 {
    line-height: 1; }

/* Breadcrumb */
.breadcrumb {
  background: initial;
  padding: 0;
  font-size: 0.85em;
  margin-bottom: 0.25rem; }
  .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: '|';
    color: var(--alternate); }
  .breadcrumb .breadcrumb-item,
  .breadcrumb .breadcrumb-item a {
    color: var(--alternate); }
    .breadcrumb .breadcrumb-item:not(.active):hover,
    .breadcrumb .breadcrumb-item a:not(.active):hover {
      color: var(--primary); }

.breadcrumb-back span {
  display: inline-block;
  padding-top: 2px; }

.mobile-buttons-container .dropdown-menu {
  min-width: 200px;
  padding: 15px 20px 15px 20px;
  width: unset; }

.responsive-tabs .responsive-tab-dropdown .nav-item .nav-link {
  line-height: 1.3;
  width: 100%;
  text-align: initial; }

/* Scroll Spy Menu */
#scrollSpyMenu {
  position: sticky;
  overflow-y: auto;
  width: 190px; }
  @media (min-width: 1400px) {
    #scrollSpyMenu {
      width: 260px; } }

#scrollSpyMenu .nav ul,
html .nav-container .mobile-buttons-container .dropdown-menu .nav ul {
  padding-left: 35px;
  margin-top: -2px;
  margin-bottom: 2px; }

#scrollSpyMenu li,
html .nav-container .mobile-buttons-container .dropdown-menu li {
  width: 100%;
  padding: initial; }
  #scrollSpyMenu li a,
  html .nav-container .mobile-buttons-container .dropdown-menu li a {
    width: 100%; }

#scrollSpyMenu .nav-link,
html .nav-container .mobile-buttons-container .dropdown-menu .nav-link {
  display: inline-block;
  opacity: 0.8;
  color: var(--alternate);
  padding-top: 0.1rem;
  padding-bottom: 0.1rem;
  padding-left: 0; }
  #scrollSpyMenu .nav-link i,
  html .nav-container .mobile-buttons-container .dropdown-menu .nav-link i {
    display: inline-block; }
  #scrollSpyMenu .nav-link:hover,
  html .nav-container .mobile-buttons-container .dropdown-menu .nav-link:hover {
    color: var(--primary); }
  #scrollSpyMenu .nav-link.active,
  html .nav-container .mobile-buttons-container .dropdown-menu .nav-link.active {
    color: var(--alternate); }

#scrollSpyMenu svg,
html .nav-container .mobile-buttons-container .dropdown-menu svg {
  display: block;
  float: left;
  width: 17px;
  height: 17px;
  margin-right: 0.25rem; }

#scrollSpyMenu span,
html .nav-container .mobile-buttons-container .dropdown-menu span {
  float: left;
  display: block;
  line-height: 1.4;
  width: calc(100% - 25px); }

html[data-placement='horizontal'] #scrollSpyMenu {
  top: calc(var(--nav-size-slim) + calc(var(--main-spacing-horizontal) / 2));
  height: calc(100vh - calc(calc(var(--nav-size-slim) * 2) + calc(var(--main-spacing-horizontal) + var(--card-spacing)))); }

html[data-placement='vertical'] #scrollSpyMenu {
  top: var(--main-spacing-horizontal);
  height: calc(100vh - calc(calc(var(--nav-size-slim) * 2) + calc(var(--main-spacing-horizontal) + var(--card-spacing)))); }

/*
*
* Offcanvas
*
* Bootstrap offcanvas styles.
*
*/
.offcanvas {
  background-color: var(--foreground); }

/*
*
* Progress
*
* Bootstrap progress styles.
*
*/
.progress {
  height: 0.25rem;
  border-radius: var(--border-radius-md);
  background-color: var(--separator-light); }
  .progress .progress-bar {
    background-color: var(--primary); }
  .progress .progress-bar + .progress-bar:not(:last-child) {
    border-radius: initial; }
  .progress .progress-bar:last-child {
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); }
  .progress.progress-xs {
    height: 0.1rem; }
  .progress.progress-sm {
    height: 0.15rem; }
  .progress.progress-md {
    height: 0.2rem; }
  .progress.progress-lg {
    height: 0.35rem; }
  .progress.progress-xl {
    height: 0.5rem; }
  .progress.progress-xxl {
    height: 1rem; }

.text-body .progressbar-text {
  color: var(--body) !important; }

.text-alternate .progressbar-text {
  color: var(--alternate) !important; }

.text-muted .progressbar-text {
  color: var(--muted) !important; }

.text-separator .progressbar-text {
  color: var(--separator) !important; }

.text-separator-light .progressbar-text {
  color: var(--separator-light) !important; }

/*
*
* Popover
*
* Bootstrap popover styles.
*
*/
.popover {
  border-radius: var(--border-radius-md);
  border-color: var(--separator);
  background-color: var(--foreground);
  color: var(--body); }
  .popover .popover-header {
    background: initial; }
  .popover .popover-body,
  .popover .popover-header {
    padding: var(--card-spacing-xs) var(--card-spacing-sm);
    color: var(--body);
    border: initial;
    font-size: 1em; }

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[x-placement^='bottom'] .popover-header::before {
  border-bottom: none !important; }

.bs-popover-end > .popover-arrow::before,
.bs-popover-auto[x-placement^='right'] > .popover-arrow::before {
  border-right-color: var(--separator); }

.bs-popover-end > .popover-arrow::after,
.bs-popover-auto[x-placement^='right'] > .popover-arrow::after {
  border-right-color: var(--foreground); }

.bs-popover-bottom > .popover-arrow::after,
.bs-popover-auto[x-placement^='bottom'] > .popover-arrow::after {
  border-bottom-color: var(--foreground); }

.bs-popover-bottom > .popover-arrow::before,
.bs-popover-auto[x-placement^='bottom'] > .popover-arrow::before {
  border-bottom-color: var(--separator); }

.bs-popover-top > .popover-arrow::after,
.bs-popover-auto[x-placement^='top'] > .popover-arrow::after {
  border-top-color: var(--foreground); }

.bs-popover-top > .popover-arrow::before,
.bs-popover-auto[x-placement^='top'] > .popover-arrow::before {
  border-top-color: var(--separator); }

.bs-popover-start > .popover-arrow::after,
.bs-popover-auto[x-placement^='left'] > .popover-arrow::after {
  border-left-color: var(--foreground); }

.bs-popover-start > .popover-arrow::before,
.bs-popover-auto[x-placement^='left'] > .popover-arrow::before {
  border-left-color: var(--separator); }

/*
*
* Spinner
*
* Bootstrap spinner styles.
*
*/
.spinner-border {
  border-width: 0.15em;
  margin-right: 5px; }

.spinner-border-sm {
  border-width: 0.15em;
  margin-right: 5px; }

body.spinner:after {
  content: ' ';
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid rgba(0, 0, 0, 0.5);
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
  left: 50%;
  position: fixed;
  top: 50%;
  border-width: 0.15em;
  margin-top: -15px;
  margin-left: -15px; }

body.spinner #root {
  opacity: 0.3;
  pointer-events: none;
  user-select: none; }

.overlay-spinner {
  position: relative;
  pointer-events: none;
  user-select: none; }
  .overlay-spinner:before {
    content: ' ';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-lg);
    background: rgba(var(--foreground-rgb), 0.7); }
  .overlay-spinner:after {
    content: ' ';
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25em solid var(--primary);
    border-right-color: transparent;
    border-radius: 50%;
    -webkit-animation: spinner-border 0.75s linear infinite;
    animation: spinner-border 0.75s linear infinite;
    left: 50%;
    position: absolute;
    top: 50%;
    border-width: 0.15em;
    margin-top: -15px;
    margin-left: -15px; }

.filled .input-group.spinner .input-group-text {
  background-color: var(--background-light); }

.form-floating .form-control ~ label {
  z-index: 3; }

/*
*
* Texts
*
* Bootstrap text styles.
*
*/
.text-primary {
  color: var(--primary) !important; }

.text-secondary {
  color: var(--secondary) !important; }

.text-tertiary {
  color: var(--tertiary) !important; }

.text-quaternary {
  color: var(--quaternary) !important; }

.text-success {
  color: var(--success) !important; }

.text-danger {
  color: var(--danger) !important; }

.text-warning {
  color: var(--warning) !important; }

.text-info {
  color: var(--info) !important; }

.text-light {
  color: var(--light) !important; }

.text-dark {
  color: var(--dark) !important; }

.text-body {
  color: var(--body) !important; }

.text-alternate {
  color: var(--alternate) !important; }

.text-muted {
  color: var(--muted) !important; }

.text-white {
  color: var(--light-text) !important; }

.text-black {
  color: var(--dark-text) !important; }

.text-separator {
  color: var(--separator) !important; }

.text-separator-light {
  color: var(--separator-light) !important; }

.text-uppercase {
  text-transform: uppercase; }

.text-lowercase {
  text-transform: lowercase; }

/*
*
* Table
*
* Bootstrap table styles.
*
*/
.table {
  --bs-table-bg: rgba(0, 0, 0, 0);
  --bs-table-striped-color: var(--body);
  --bs-table-striped-bg: rgba(var(--body-rgb), 0.05);
  --bs-table-active-color: var(--body);
  --bs-table-active-bg: rgba(var(--body-rgb), 0.1);
  --bs-table-hover-color: var(--body);
  --bs-table-hover-bg: rgba(var(--body-rgb), 0.075);
  color: var(--body);
  border-color: var(--separator-light); }

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: var(--separator); }

.table > :not(caption) > * > * {
  --bs-table-bg: rgba(0, 0, 0, 0);
  color: var(--body); }

.table-bordered > :not(caption) > * {
  border-color: var(--separator); }

/*
*
* Toast
*
* Bootstrap toast styles.
*
*/
.toast {
  border-radius: var(--border-radius-md);
  border-color: var(--separator);
  font-size: 1em;
  background: rgba(var(--foreground-rgb), 0.9); }
  .toast .toast-body,
  .toast .toast-header {
    padding: var(--card-spacing-sm);
    color: var(--body);
    border: initial;
    background: initial; }
  .toast .toast-header + .toast-body {
    padding-top: initial; }
  .toast .btn-close {
    margin-right: -0.25rem;
    margin-left: 0.25rem; }

/*
*
* Tooltip
*
* Bootstrap tooltip styles.
*
*/
.tooltip .tooltip-inner {
  background: var(--primary);
  color: var(--light-text);
  font-size: 0.875em;
  border-radius: var(--border-radius-md); }

.bs-tooltip-top .tooltip-arrow::before,
.bs-tooltip-auto[x-placement^='top'] .tooltip-arrow::before {
  border-top-color: var(--primary); }

.bs-tooltip-start .tooltip-arrow::before,
.bs-tooltip-auto[x-placement^='left'] .tooltip-arrow::before {
  border-left-color: var(--primary); }

.bs-tooltip-end .tooltip-arrow::before,
.bs-tooltip-auto[x-placement^='right'] .tooltip-arrow::before {
  border-right-color: var(--primary); }

.bs-tooltip-bottom .tooltip-arrow::before,
.bs-tooltip-auto[x-placement^='bottom'] .tooltip-arrow::before {
  border-bottom-color: var(--primary); }

/*
*
* Autocomplete
*
* Autocomplete form component styles.
*
*/
#searchPagesResults {
  list-style: none;
  padding-left: 0; }
  #searchPagesResults .auto-complete-result-item {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md) !important;
    border: 1px solid var(--separator) !important;
    margin-bottom: 5px; }
  #searchPagesResults .auto-complete-result-item.autoComplete_selected,
  #searchPagesResults .auto-complete-result-item:hover {
    border: 1px solid var(--primary) !important;
    cursor: pointer; }
  #searchPagesResults .autoComplete_highlighted {
    color: var(--primary) !important; }

.autocomplete-container {
  position: relative; }
  .autocomplete-container .autocomplete-results {
    position: absolute;
    list-style: none;
    background: var(--foreground);
    width: 100%;
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(var(--primary-rgb), 1) !important;
    padding: 0.75rem;
    margin-top: 4px; }
    .autocomplete-container .autocomplete-results.show {
      display: block; }
    .autocomplete-container .autocomplete-results .auto-complete-result-item {
      padding: 0.5rem 0.75rem !important; }
  .autocomplete-container .auto-complete-result-item.autoComplete_selected,
  .autocomplete-container .auto-complete-result-item:hover {
    cursor: pointer;
    color: var(--primary);
    background: var(--separator-light) !important;
    border-radius: var(--border-radius-sm); }
  .autocomplete-container .autoComplete_highlighted {
    color: var(--primary) !important; }

/*
*
* Autosize
*
* Autosize plugin styles.
*
*/
#chatInput {
  max-height: 80px; }

/*
*
* Calendar
*
* Full page calendar plugin styles.
*
*/
:root {
  --fc-small-font-size: 0.85em;
  --fc-page-bg-color: var(--foreground);
  --fc-neutral-bg-color: var(--separator-light);
  --fc-neutral-text-color: var(--separator);
  --fc-border-color: var(--separator-light);
  --fc-event-bg-color: var(--primary);
  --fc-event-border-color: var(--primary);
  --fc-event-text-color: var(--light-text);
  --fc-event-selected-overlay-color: rgba(0, 0, 0, 0.25);
  --fc-event-resizer-thickness: 8px;
  --fc-event-resizer-dot-total-width: 8px;
  --fc-event-resizer-dot-border-width: 1px;
  --fc-non-business-color: rgba(var(--primary-rgb), 0.2);
  --fc-bg-event-color: rgba(var(--primary-rgb), 0.2);
  --fc-bg-event-opacity: 0.3;
  --fc-highlight-color: rgba(var(--primary-rgb), 0.1);
  --fc-today-bg-color: rgba(var(--primary-rgb), 0.05);
  --fc-now-indicator-color: var(--danger); }

.fc {
  min-height: 675px;
  max-height: 900px; }
  .fc .fc-scrollgrid {
    border: initial; }
  .fc .table-bordered thead th,
  .fc .table-bordered thead td {
    border-bottom-width: 1px;
    border-right: initial; }
  .fc .fc-scrollgrid-section > * {
    border: initial; }
  .fc .fc-col-header-cell-cushion {
    padding-top: 15px;
    padding-bottom: 15px; }
  .fc .fc-toolbar-title {
    font-family: var(--font-heading);
    font-weight: 400;
    margin-bottom: 0;
    font-size: 1em;
    color: var(--primary);
    height: var(--small-title-height); }
  .fc .table-bordered th,
  .fc .table-bordered td {
    border-color: var(--separator); }
  .fc a.fc-col-header-cell-cushion {
    color: var(--primary); }
  .fc .fc-timegrid-divider {
    padding: 0 0 1px;
    background: var(--separator); }
  .fc .fc-daygrid-event {
    border-radius: var(--border-radius-sm);
    padding-left: 3px;
    padding-right: 3px; }

.fc .table-bordered th,
.fc .table-bordered td {
  border-width: 1px; }

/*
*
* Context Menu
*
* Context menu plugin styles.
*
*/
.context-menu-list {
  box-shadow: initial;
  border-color: var(--separator);
  padding: 0.5rem 0;
  background-color: var(--foreground);
  border-radius: var(--border-radius-md); }
  .context-menu-list:before {
    content: ''; }

.context-menu-item {
  text-decoration: initial;
  color: var(--body);
  background: initial !important;
  border: initial !important;
  padding: 0.5rem 1.5rem;
  line-height: 1;
  font-size: 1em !important; }
  .context-menu-item span {
    display: inline-block;
    font-family: var(--font);
    color: var(--body);
    font-weight: 400;
    font-size: 1em !important;
    vertical-align: top;
    padding-top: 2px; }
  .context-menu-item:before {
    margin-right: 8px; }
  .context-menu-item.context-menu-hover {
    color: var(--primary);
    text-decoration: none;
    background-color: var(--foreground); }
    .context-menu-item.context-menu-hover > span {
      color: var(--primary); }

.context-menu-item.context-menu-disabled,
.context-menu-item.context-menu-disabled span {
  color: var(--muted); }

.context-menu-item > .context-menu-list {
  margin-left: 10px; }

.context-menu-submenu:after {
  content: '';
  border: initial;
  border-width: initial;
  right: 1em;
  width: 5px;
  height: 5px;
  border-top: 1px solid var(--body);
  border-right: 1px solid var(--body);
  transform: rotate(45deg);
  order: 3;
  margin-left: 5px;
  margin-top: 3px; }

/*
*
* Datatables
*
* Datatables plugin styles.
*
*/
div.dataTables_wrapper div.dataTables_scrollHead table.dataTable {
  margin-top: 0 !important; }

div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting:before, div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting:after,
div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting_asc:before,
div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting_asc:after,
div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting_desc:before,
div.dataTables_wrapper div.dataTables_scrollBody table.dataTable thead .sorting_desc:after {
  display: none !important; }

div.dataTables_wrapper div.dataTables_paginate {
  margin-top: 15px; }
  div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    justify-content: center; }

div.dataTables_wrapper div.dataTables_processing {
  box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5), 0 4px 13px rgba(0, 0, 0, 0.04) !important; }

div.dataTables_wrapper table.dataTable {
  width: 100% !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important; }
  div.dataTables_wrapper table.dataTable th {
    height: 20px;
    padding-top: 0;
    padding-bottom: 10px; }
    div.dataTables_wrapper table.dataTable th.empty:after, div.dataTables_wrapper table.dataTable th.empty:before {
      content: ' ' !important;
      display: none !important; }
  div.dataTables_wrapper table.dataTable td {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem; }
  div.dataTables_wrapper table.dataTable thead :before {
    content: ' ' !important;
    display: none !important; }
  div.dataTables_wrapper table.dataTable thead .sorting_asc,
  div.dataTables_wrapper table.dataTable thead .sorting_desc {
    color: var(--alternate) !important; }
  div.dataTables_wrapper table.dataTable thead .sorting:after,
  div.dataTables_wrapper table.dataTable thead .sorting_desc:after,
  div.dataTables_wrapper table.dataTable thead .sorting_asc:after,
  div.dataTables_wrapper table.dataTable thead .sorting_asc_disabled:after,
  div.dataTables_wrapper table.dataTable thead .sorting_desc_disabled:after {
    content: ' ' !important;
    display: inline-block;
    width: 10px;
    height: 10px;
    position: relative;
    left: 10px;
    top: 0;
    opacity: 1; }
  html[data-color*='light'] div.dataTables_wrapper table.dataTable thead .sorting:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-1' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  html[data-color*='light'] div.dataTables_wrapper table.dataTable thead .sorting_desc:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.3;%7D.cls-2%7Bopacity:0.9;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  html[data-color*='light'] div.dataTables_wrapper table.dataTable thead .sorting_asc:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.9;%7D.cls-2%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  html[data-color*='dark'] div.dataTables_wrapper table.dataTable thead .sorting:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fff;opacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-1' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  html[data-color*='dark'] div.dataTables_wrapper table.dataTable thead .sorting_desc:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1,.cls-2%7Bfill:%23fff;%7D.cls-1%7Bopacity:0.3;%7D.cls-2%7Bopacity:0.9;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  html[data-color*='dark'] div.dataTables_wrapper table.dataTable thead .sorting_asc:after {
    background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1,.cls-2%7Bfill:%23fff;%7D.cls-1%7Bopacity:0.9;%7D.cls-2%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }
  div.dataTables_wrapper table.dataTable tbody tr.selected,
  div.dataTables_wrapper table.dataTable tbody th.selected,
  div.dataTables_wrapper table.dataTable tbody td.selected {
    color: inherit; }
    div.dataTables_wrapper table.dataTable tbody tr.selected a,
    div.dataTables_wrapper table.dataTable tbody th.selected a,
    div.dataTables_wrapper table.dataTable tbody td.selected a {
      color: inherit; }
  div.dataTables_wrapper table.dataTable > tbody > tr.child ul.dtr-details {
    padding-left: 30px;
    padding-bottom: 5px; }
    div.dataTables_wrapper table.dataTable > tbody > tr.child ul.dtr-details > li {
      border: initial;
      padding-bottom: 0;
      padding-top: 0px; }
  div.dataTables_wrapper table.dataTable.dtr-inline.collapsed > tbody > tr[role='row'] td .dtr-control:before,
  div.dataTables_wrapper table.dataTable.dtr-inline.collapsed > tbody > tr[role='row'] th .dtr-control:before {
    top: initial;
    margin-top: 2px;
    color: var(--primary);
    line-height: 19px;
    box-shadow: inset 0 0 0 1px var(--primary);
    background: initial;
    height: 18px;
    width: 18px;
    left: 0;
    border: initial; }
  div.dataTables_wrapper table.dataTable.display tbody tr.odd, div.dataTables_wrapper table.dataTable.stripe tbody tr.odd {
    background-color: rgba(var(--body-rgb), 0.05); }
  div.dataTables_wrapper table.dataTable.hover tbody > tr > .selected:hover,
  div.dataTables_wrapper table.dataTable.hover tbody > tr .selected:hover, div.dataTables_wrapper table.dataTable.display tbody > tr > .selected:hover,
  div.dataTables_wrapper table.dataTable.display tbody > tr .selected:hover {
    background-color: rgba(var(--separator-rgb), 0.4); }
  div.dataTables_wrapper table.dataTable.hover tbody tr:hover, div.dataTables_wrapper table.dataTable.display tbody tr:hover {
    background-color: rgba(var(--separator-rgb), 0.3); }

.data-table-rows div.dataTables_wrapper {
  overflow: initial; }

.data-table-rows div.dataTables_scrollBody table tbody tr:first-child td {
  border-top: 1px solid transparent; }

.data-table-rows div.dataTables_scrollBody table.dataTable.hover tbody tr:first-child:hover td,
.data-table-rows div.dataTables_scrollBody table.dataTable.display tbody tr:first-child:hover td {
  border-color: rgba(var(--separator-rgb), 1); }

.data-table-rows div.dataTables_scrollBody table.dataTable.hover tbody > tr:first-child.selected:hover td,
.data-table-rows div.dataTables_scrollBody table.dataTable.hover tbody > tr:first-child > .selected:hover td,
.data-table-rows div.dataTables_scrollBody table.dataTable.display tbody > tr:first-child.selected:hover td,
.data-table-rows div.dataTables_scrollBody table.dataTable.display tbody > tr:first-child > .selected:hover td {
  border-color: rgba(var(--primary-rgb), 1); }

.data-table-rows div.dataTables_scrollBody table.dataTable tbody > tr:first-child.selected td {
  border-color: rgba(var(--primary-rgb), 0.5); }

.data-table-rows div.dataTables_paginate a {
  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.07) !important; }

.data-table-rows table.dataTable {
  border-spacing: 0 var(--card-spacing-xs); }
  .data-table-rows table.dataTable td,
  .data-table-rows table.dataTable th {
    padding-left: var(--card-spacing-sm);
    padding-right: var(--card-spacing-sm);
    padding-top: 0;
    padding-bottom: 0;
    height: 60px;
    border: initial;
    vertical-align: middle; }
  .data-table-rows table.dataTable th {
    height: 20px;
    padding-top: 0;
    padding-bottom: 10px; }
    .data-table-rows table.dataTable th.empty {
      pointer-events: none; }
      .data-table-rows table.dataTable th.empty:after, .data-table-rows table.dataTable th.empty:before {
        content: ' ' !important;
        display: none !important; }
  .data-table-rows table.dataTable td {
    border: 1px solid transparent;
    border-width: 1px 0;
    background: var(--foreground);
    padding-top: 0;
    padding-bottom: 0; }
    .data-table-rows table.dataTable td:first-child {
      border-width: 1px 0 1px 1px;
      border-top-left-radius: var(--border-radius-lg);
      border-bottom-left-radius: var(--border-radius-lg); }
    .data-table-rows table.dataTable td:last-child {
      border-width: 1px 1px 1px 0;
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: var(--border-radius-lg); }
    .data-table-rows table.dataTable td .form-check {
      pointer-events: none; }
  .data-table-rows table.dataTable tr.selected td {
    border-color: rgba(var(--primary-rgb), 0.5); }
  .data-table-rows table.dataTable tbody tr {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
    border-radius: var(--border-radius-lg); }
    .data-table-rows table.dataTable tbody tr.selected {
      border-color: var(--primary); }
  .data-table-rows table.dataTable tbody > tr.selected,
  .data-table-rows table.dataTable tbody > tr > .selected {
    background: transparent; }
  .data-table-rows table.dataTable.hover tbody tr:hover td, .data-table-rows table.dataTable.display tbody tr:hover td {
    border-color: rgba(var(--separator-rgb), 1); }
  .data-table-rows table.dataTable.hover tbody > tr.selected:hover td,
  .data-table-rows table.dataTable.hover tbody > tr > .selected:hover td, .data-table-rows table.dataTable.display tbody > tr.selected:hover td,
  .data-table-rows table.dataTable.display tbody > tr > .selected:hover td {
    border-color: rgba(var(--primary-rgb), 1); }

.data-table-rows.slim div.dataTables_wrapper table.dataTable {
  border-spacing: 0 calc(var(--card-spacing-xs) / 10 * 7); }
  .data-table-rows.slim div.dataTables_wrapper table.dataTable td {
    height: 42px; }

.data-table-rows .table-container {
  margin-left: calc(var(--card-spacing-xs) * -1);
  margin-right: calc(var(--card-spacing-xs) * -1); }
  .data-table-rows .table-container .os-scrollbar-horizontal,
  .data-table-rows .table-container table {
    padding-left: var(--card-spacing-xs);
    padding-right: var(--card-spacing-xs); }

.data-table-boxed div.dataTables_paginate {
  margin-bottom: calc(var(--card-spacing) / 2) !important; }
  .data-table-boxed div.dataTables_paginate a {
    box-shadow: 0 4px 13px rgba(0, 0, 0, 0.07) !important; }

.data-table-boxed table.dataTable {
  border-spacing: 0 2px;
  padding-top: calc(var(--card-spacing) / 2); }
  .data-table-boxed table.dataTable tbody tr.selected,
  .data-table-boxed table.dataTable tbody tr > .selected {
    background-color: rgba(var(--separator-rgb), 0.3); }
  .data-table-boxed table.dataTable tr td:first-of-type,
  .data-table-boxed table.dataTable tr th:first-of-type {
    padding-left: calc(var(--card-spacing) / 2); }
  .data-table-boxed table.dataTable tr td:last-of-type,
  .data-table-boxed table.dataTable tr th:last-of-type {
    padding-right: calc(var(--card-spacing) / 2); }
  .data-table-boxed table.dataTable tr td:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md); }
  .data-table-boxed table.dataTable tr td:last-child {
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); }
  .data-table-boxed table.dataTable tr td .form-check {
    pointer-events: none; }

.data-table-responsive-wrapper div.dataTables_scroll div.dataTables_scrollHead table.dataTable th {
  padding-bottom: 0 !important; }

.data-table-responsive-wrapper div.dataTables_scroll div.dataTables_scrollBody table.dataTable {
  margin-top: calc(var(--card-spacing-xs) * -1) !important; }

div.dt-button-info {
  border: 1px solid var(--primary);
  border-radius: var(--border-radius-lg);
  background: var(--foreground);
  color: var(--body);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important; }
  div.dt-button-info h2 {
    padding: 1rem;
    padding-bottom: 0;
    font-size: 18px;
    background: initial;
    border: initial;
    color: var(--body); }

table.dataTable.dtr-inline.collapsed > tbody > tr[role='row'] > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr[role='row'] > th.dtr-control:before {
  background-color: var(--primary); }

/*
*
* Datepicker
*
* Datepicker form control styles.
*
*/
.datepicker.dropdown-menu {
  padding: 1.25rem;
  box-shadow: initial;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--primary);
  background: var(--foreground);
  color: var(--body);
  font-size: 1em; }

.datepicker-dropdown:before {
  border-bottom-color: var(--primary); }

.datepicker-dropdown:after {
  border-bottom: 6px solid var(--foreground); }

.datepicker-dropdown.datepicker-orient-top:before {
  border-top: 7px solid var(--primary); }

.datepicker-dropdown.datepicker-orient-top:after {
  border-top: 6px solid var(--foreground); }

.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active:active {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text); }

.datepicker table tr td {
  text-shadow: initial !important; }

.datepicker table tr td.active.active.focus,
.datepicker table tr td.active.active:focus,
.datepicker table tr td.active.active:hover,
.datepicker table tr td.active.highlighted.active.focus,
.datepicker table tr td.active.highlighted.active:focus,
.datepicker table tr td.active.highlighted.active:hover,
.datepicker table tr td.active.highlighted:active.focus,
.datepicker table tr td.active.highlighted:active:focus,
.datepicker table tr td.active.highlighted:active:hover,
.datepicker table tr td.active:active.focus,
.datepicker table tr td.active:active:focus,
.datepicker table tr td.active:active:hover {
  background-color: var(--primary-darker) !important;
  border-color: var(--primary-darker) !important;
  color: var(--light-text); }

.datepicker table tr td.new,
.datepicker table tr td.old {
  color: var(--muted); }

.datepicker table tr td.day:hover,
.datepicker table tr td.focused {
  background: rgba(var(--separator-rgb), 0.6) !important;
  cursor: pointer; }

.datepicker table tr td span.active.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active:hover:active {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text); }

.datepicker table tr td span.active.active.focus,
.datepicker table tr td span.active.active:focus,
.datepicker table tr td span.active.active:hover,
.datepicker table tr td span.active.disabled.active.focus,
.datepicker table tr td span.active.disabled.active:focus,
.datepicker table tr td span.active.disabled.active:hover,
.datepicker table tr td span.active.disabled:active.focus,
.datepicker table tr td span.active.disabled:active:focus,
.datepicker table tr td span.active.disabled:active:hover,
.datepicker table tr td span.active.disabled:hover.active.focus,
.datepicker table tr td span.active.disabled:hover.active:focus,
.datepicker table tr td span.active.disabled:hover.active:hover,
.datepicker table tr td span.active.disabled:hover:active.focus,
.datepicker table tr td span.active.disabled:hover:active:focus,
.datepicker table tr td span.active.disabled:hover:active:hover,
.datepicker table tr td span.active:active.focus,
.datepicker table tr td span.active:active:focus,
.datepicker table tr td span.active:active:hover,
.datepicker table tr td span.active:hover.active.focus,
.datepicker table tr td span.active:hover.active:focus,
.datepicker table tr td span.active:hover.active:hover,
.datepicker table tr td span.active:hover:active.focus,
.datepicker table tr td span.active:hover:active:focus,
.datepicker table tr td span.active:hover:active:hover {
  background-color: var(--primary-darker) !important;
  border-color: var(--primary-darker) !important;
  color: var(--light-text); }

.datepicker table tr td,
.datepicker table tr th,
.datepicker table tr td span {
  border-radius: var(--border-radius-sm); }

.datepicker .datepicker-switch:hover,
.datepicker .next:hover,
.datepicker .prev:hover,
.datepicker tfoot tr th:hover {
  background: var(--primary);
  color: var(--light-text); }

.datepicker table tr td span:hover {
  background: var(--primary);
  color: var(--light-text); }

.datepicker table tr td span.focused {
  background: var(--separator);
  color: var(--body); }

.datepicker.datepicker-inline,
.datepicker.datepicker-inline table {
  width: 100%; }

.datepicker table tr td,
.datepicker table tr th {
  width: 29px;
  height: 29px;
  line-height: 1; }

.datepicker table th {
  font-weight: initial;
  color: var(--alternate); }

.input-daterange {
  width: unset; }

.input-daterange input {
  border-radius: var(--border-radius-md) !important;
  text-align: left; }

.datepicker table tr td.selected,
.datepicker table tr td.selected.highlighted {
  background: var(--primary);
  color: var(--light-text); }

.datepicker table tr td.selected.highlighted:hover,
.datepicker table tr td.selected:hover {
  background-color: var(--primary-darker) !important;
  border-color: var(--primary-darker) !important;
  color: var(--light-text); }

.datepicker table tr td.range {
  background: rgba(var(--separator-rgb), 0.6) !important;
  color: var(--body); }

.datepicker table .range-start.day {
  border-top-right-radius: initial;
  border-bottom-right-radius: initial; }

.datepicker table .range-end.day {
  border-top-left-radius: initial;
  border-bottom-left-radius: initial; }

.datepicker table .range-start.range-end.day {
  border-radius: var(--border-radius-sm); }

.datepicker table tr td.range:hover {
  background-color: var(--muted) !important;
  border-color: var(--muted) !important;
  color: var(--light-text); }

.datepicker-orient-top {
  margin-top: 15px; }

.form-floating .date-picker.show ~ label {
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--muted); }

/*
*
* Dropzone
*
* Dropzone form control styles.
*
*/
.dropzone {
  min-height: 90px;
  border: 1px solid var(--separator) !important;
  background: var(--foreground) !important;
  padding: var(--card-spacing-sm) !important;
  border-radius: var(--border-radius-md) !important;
  color: var(--body) !important;
  height: auto;
  padding-right: initial !important;
  padding-bottom: initial !important; }
  .dropzone .img-thumbnail {
    height: 58px;
    width: 100% !important;
    object-fit: cover !important;
    padding: initial;
    width: 100%;
    height: 100%;
    filter: initial !important;
    transform: initial !important;
    border-radius: var(--border-radius-sm);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
    background-color: unset !important; }
  .dropzone .image-container {
    width: 25%; }
  .dropzone:hover .dz-message {
    color: var(--primary) !important; }

.dropzone.dz-clickable .dz-message {
  position: absolute;
  margin: 0 auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--body); }

.dropzone.dz-clickable .dz-message span {
  top: 50px !important; }

.dropzone .dz-preview.dz-image-preview,
.dropzone .dz-preview.dz-file-preview {
  max-width: 100%;
  min-height: unset;
  border: 1px solid rgba(var(--separator-rgb), 0.7) !important;
  border-radius: var(--border-radius-sm) !important;
  background: var(--foreground) !important;
  color: var(--body) !important;
  margin: var(--card-spacing-xs);
  margin-left: initial !important;
  margin-top: initial !important; }
  .dropzone .dz-preview.dz-image-preview > div,
  .dropzone .dz-preview.dz-file-preview > div {
    position: relative; }
  .dropzone .dz-preview.dz-image-preview .dz-image,
  .dropzone .dz-preview.dz-file-preview .dz-image {
    height: 100%;
    width: 80px;
    float: left;
    border-radius: initial; }
    .dropzone .dz-preview.dz-image-preview .dz-image img,
    .dropzone .dz-preview.dz-file-preview .dz-image img {
      width: 100%; }
  .dropzone .dz-preview.dz-image-preview .preview-container,
  .dropzone .dz-preview.dz-file-preview .preview-container {
    transition: initial !important;
    animation: initial !important;
    margin-left: 0;
    margin-top: 0;
    position: relative;
    width: 100%;
    height: 100%; }
    .dropzone .dz-preview.dz-image-preview .preview-container i,
    .dropzone .dz-preview.dz-file-preview .preview-container i {
      color: var(--primary);
      font-size: 20px;
      position: absolute;
      left: 50%;
      top: 29px;
      transform: translateX(-50%) translateY(-50%) !important;
      height: 22px; }
  .dropzone .dz-preview.dz-image-preview strong,
  .dropzone .dz-preview.dz-file-preview strong {
    font-weight: normal; }
  .dropzone .dz-preview.dz-image-preview .remove,
  .dropzone .dz-preview.dz-file-preview .remove {
    position: absolute;
    right: 8px;
    top: 8px;
    color: var(--muted) !important; }
    .dropzone .dz-preview.dz-image-preview .remove i,
    .dropzone .dz-preview.dz-file-preview .remove i {
      cursor: pointer; }
    .dropzone .dz-preview.dz-image-preview .remove:hover,
    .dropzone .dz-preview.dz-file-preview .remove:hover {
      color: var(--primary) !important; }
  .dropzone .dz-preview.dz-image-preview .dz-details,
  .dropzone .dz-preview.dz-file-preview .dz-details {
    position: static;
    display: block;
    opacity: 1;
    text-align: left;
    min-width: unset;
    z-index: initial;
    color: var(--body) !important;
    float: left;
    padding: 0.75rem 1rem;
    width: 75%; }
    .dropzone .dz-preview.dz-image-preview .dz-details .dz-size,
    .dropzone .dz-preview.dz-file-preview .dz-details .dz-size {
      margin-bottom: 0;
      font-size: 1em; }
    .dropzone .dz-preview.dz-image-preview .dz-details .dz-filename span,
    .dropzone .dz-preview.dz-file-preview .dz-details .dz-filename span {
      border: initial !important;
      background: transparent !important; }
  .dropzone .dz-preview.dz-image-preview .dz-error-mark,
  .dropzone .dz-preview.dz-image-preview .dz-success-mark,
  .dropzone .dz-preview.dz-file-preview .dz-error-mark,
  .dropzone .dz-preview.dz-file-preview .dz-success-mark {
    color: var(--primary) !important;
    margin-left: 0;
    margin-top: 0;
    bottom: initial;
    right: initial;
    top: 13px;
    left: 23px;
    padding: 7px 8px;
    background: var(--foreground);
    border-radius: var(--border-radius-xl);
    line-height: 1; }
    .dropzone .dz-preview.dz-image-preview .dz-error-mark i,
    .dropzone .dz-preview.dz-image-preview .dz-success-mark i,
    .dropzone .dz-preview.dz-file-preview .dz-error-mark i,
    .dropzone .dz-preview.dz-file-preview .dz-success-mark i {
      font-size: 18px !important;
      color: var(--primary) !important; }
  .dropzone .dz-preview.dz-image-preview .dz-error-mark i,
  .dropzone .dz-preview.dz-file-preview .dz-error-mark i {
    color: var(--primary) !important; }
  .dropzone .dz-preview.dz-image-preview .dz-progress,
  .dropzone .dz-preview.dz-file-preview .dz-progress {
    width: 100%;
    margin-left: 0;
    margin-top: 0;
    right: 0;
    height: 2px !important;
    left: 15px;
    margin-top: 5px;
    position: static; }
    .dropzone .dz-preview.dz-image-preview .dz-progress .dz-upload,
    .dropzone .dz-preview.dz-file-preview .dz-progress .dz-upload {
      width: 100%;
      background: var(--primary) !important; }
  .dropzone .dz-preview.dz-image-preview .dz-error-message,
  .dropzone .dz-preview.dz-file-preview .dz-error-message {
    background: var(--foreground) !important;
    border: 1px solid var(--primary);
    top: 60px;
    color: var(--body);
    padding: calc(var(--card-spacing-xs) / 2) var(--card-spacing-xs);
    border-radius: var(--border-radius-md);
    font-size: 0.875em;
    display: block; }
    .dropzone .dz-preview.dz-image-preview .dz-error-message:after,
    .dropzone .dz-preview.dz-file-preview .dz-error-message:after {
      border-bottom: 6px solid var(--primary) !important; }
    .dropzone .dz-preview.dz-image-preview .dz-error-message:before,
    .dropzone .dz-preview.dz-file-preview .dz-error-message:before {
      content: ' ';
      position: absolute;
      top: -5px;
      left: 64px;
      z-index: 1;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 6px solid var(--foreground) !important; }
  .dropzone .dz-preview.dz-image-preview [data-dz-name],
  .dropzone .dz-preview.dz-file-preview [data-dz-name] {
    white-space: nowrap;
    text-overflow: ellipsis;
    width: calc(100% - 35px);
    display: inline-block;
    overflow: hidden; }

.dropzone.dropzone-columns .dz-preview.dz-image-preview,
.dropzone.dropzone-columns .dz-preview.dz-file-preview {
  margin-top: var(--bs-gutter-y) !important;
  margin-bottom: initial !important; }

.dropzone:not(.dropzone-columns) .dz-preview.dz-image-preview,
.dropzone:not(.dropzone-columns) .dz-preview.dz-file-preview {
  width: 300px; }

.dropzone .dz-preview.dz-file-preview .img-thumbnail {
  display: none; }

.dropzone .dz-error.dz-preview.dz-file-preview .preview-icon {
  display: none; }

.dropzone .dz-error.dz-preview.dz-file-preview .dz-error-mark,
.dropzone .dz-error.dz-preview.dz-file-preview .dz-success-mark {
  color: var(--primary) !important;
  right: 8px;
  left: initial;
  top: initial;
  bottom: 3px; }

.dropzone .dz-preview.dz-image-preview .preview-icon {
  display: none; }

.dropzone.dz-drag-hover {
  border-color: rgba(var(--primary-rgb), 1) !important; }
  .dropzone.dz-drag-hover .dz-message {
    color: var(--primary) !important;
    opacity: 1; }

.dropzone.dropzone-top-label {
  padding: 2rem 0.5rem 0rem 1rem !important;
  min-height: 103px !important; }

.form-floating .dropzone.dropzone-floating-label {
  padding: 1rem !important;
  min-height: 101px !important; }

.form-floating .dropzone.dropzone-floating-label.dz-started {
  padding-top: 2rem !important;
  padding-bottom: 0 !important; }

.form-floating .dropzone.dropzone-floating-label.dz-started ~ label {
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--muted); }

.dropzone.dropzone-filled {
  border: 1px solid transparent !important;
  background: var(--background-light) !important;
  padding-left: 45px !important; }
  .dropzone.dropzone-filled .dz-message {
    top: initial;
    left: 45px;
    transform: initial;
    color: var(--muted) !important;
    font-weight: 300;
    top: 11px; }
  .dropzone.dropzone-filled + i {
    margin-top: 0;
    top: 14px; }
  .dropzone.dropzone-filled.dropzone.dz-drag-hover {
    background: var(--foreground) !important;
    border-color: rgba(var(--primary-rgb), 1) !important; }

.dropzone .dz-preview:not(.dz-processing) .dz-progress {
  animation: initial; }

.dropzone.row {
  min-height: 210px; }
  .dropzone.row.border-0 {
    border: initial !important; }
  .dropzone.row.p-0 {
    padding: initial !important; }
  .dropzone.row .dz-preview.dz-image-preview.col.border-0,
  .dropzone.row .dz-preview.dz-file-preview.col.border-0 {
    border: initial !important; }
  .dropzone.row .dz-preview.dz-image-preview .dz-error-mark,
  .dropzone.row .dz-preview.dz-image-preview .dz-success-mark,
  .dropzone.row .dz-preview.dz-file-preview .dz-error-mark,
  .dropzone.row .dz-preview.dz-file-preview .dz-success-mark {
    left: -16px;
    margin-left: 50%;
    top: 20px;
    margin-top: 0; }
  .dropzone.row .dz-preview.dz-image-preview .remove,
  .dropzone.row .dz-preview.dz-file-preview .remove {
    bottom: 25px;
    top: initial;
    right: 20px;
    left: initial; }
  .dropzone.row .dz-preview.dz-image-preview .dz-error-message,
  .dropzone.row .dz-preview.dz-file-preview .dz-error-message {
    left: 50%;
    right: initial;
    transform: translateX(-50%); }

/*
*
* Editor
*
* Quill editor form control styles.
*
*/
.html-editor {
  padding-right: 0.5rem;
  cursor: default; }

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border-color: var(--separator);
  background-color: var(--foreground); }

.ql-toolbar.ql-snow {
  border-color: var(--separator); }

.ql-container.ql-snow {
  border-color: var(--separator); }

.html-editor-bubble {
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md); }
  .html-editor-bubble.active {
    border-color: rgba(var(--primary-rgb), 1) !important; }

.ql-tooltip {
  z-index: 1010; }

.ql-bubble .ql-tooltip {
  border-radius: var(--border-radius-md);
  border: 1px solid var(--separator);
  background-color: var(--foreground);
  color: var(--body); }

.ql-bubble .ql-tooltip,
.ql-snow .ql-tooltip {
  color: var(--body); }

.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--primary); }

.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--primary); }

.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--primary); }

.ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow {
  border-bottom: 6px solid var(--separator); }
  .ql-bubble .ql-tooltip:not(.ql-flip) .ql-tooltip-arrow:after {
    position: absolute;
    display: block;
    content: '';
    border-color: transparent;
    border-style: solid;
    border-bottom-color: var(--foreground);
    border-width: 0 5px 5px 5px;
    width: 5px;
    height: 5px;
    left: -5px;
    top: 1px; }

.ql-editor {
  padding: 0.5rem 0.75rem 0.5rem 0.75rem;
  font-size: 1em;
  line-height: 1.5; }

.ql-bubble .ql-picker-options {
  background-color: var(--foreground);
  border: 1px solid var(--separator); }

.ql-toolbar.ql-snow,
.ql-container.ql-snow,
.ql-bubble .ql-editor {
  border-radius: var(--border-radius-md); }

.ql-toolbar.ql-snow {
  border-bottom-left-radius: initial;
  border-bottom-right-radius: initial; }

.ql-container.ql-snow {
  border-top-left-radius: initial;
  border-top-right-radius: initial; }

.ql-toolbar.ql-snow .ql-picker-label {
  border: 1px solid transparent;
  outline: initial !important;
  box-shadow: initial !important; }

.ql-editor.ql-blank::before {
  padding: initial;
  font-style: initial;
  color: var(--alternate);
  left: initial;
  right: initial; }

.ql-container.active .ql-editor {
  border-color: rgba(var(--primary-rgb), 1) !important; }

.editor-container.active .ql-toolbar {
  border-color: rgba(var(--primary-rgb), 1) !important;
  border-bottom: 1px solid var(--separator) !important; }

.editor-container.active .html-editor {
  border-color: rgba(var(--primary-rgb), 1) !important; }

.filled.custom-control-container.editor-container {
  padding-top: initial;
  padding-bottom: initial;
  padding-right: initial; }
  .filled.custom-control-container.editor-container .ql-editor {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: initial;
    padding-right: initial; }
  .filled.custom-control-container.editor-container > i {
    margin-top: 0;
    top: 14px; }
  .filled.custom-control-container.editor-container .ql-editor.ql-blank::before {
    font-family: var(--font);
    color: var(--muted);
    top: 0.75rem;
    font-size: 1em; }
  .filled.custom-control-container.editor-container.active {
    border-color: rgba(var(--primary-rgb), 1) !important;
    background: initial !important; }

.filled.custom-control-container .ql-editor {
  padding-right: 0.75rem !important; }

.top-label.custom-control-container.editor-container {
  padding: initial !important; }
  .top-label.custom-control-container.editor-container .ql-editor {
    padding: 0 0.75rem 0.5rem 0.75rem;
    color: var(--body);
    margin-top: 1.65rem; }
  .top-label.custom-control-container.editor-container .ql-editor.ql-blank::before {
    font-family: var(--font);
    color: var(--muted);
    top: 1.65rem;
    font-size: 1em; }
  .top-label.custom-control-container.editor-container.active {
    border-color: rgba(var(--primary-rgb), 1) !important; }

.form-floating .html-editor {
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md); }
  .form-floating .html-editor .ql-editor {
    padding-top: 1.65rem; }
  .form-floating .html-editor.active {
    border-color: rgba(var(--primary-rgb), 1) !important; }
  .form-floating .html-editor ~ label {
    transition: transform 0.1s ease-in-out, padding 0.1s ease-in-out, background-color 0s ease-in-out, height 0.1s ease-in-out; }
  .form-floating .html-editor.active ~ label,
  .form-floating .html-editor.full ~ label {
    color: var(--muted);
    background: var(--foreground);
    padding-top: 0.25rem;
    padding-bottom: 0.05rem;
    border-top-left-radius: var(--border-radius-md);
    height: auto;
    -webkit-transform: scale(0.85) translateY(1px) translateX(0.15rem);
    transform: scale(0.85) translateY(1px) translateX(0.15rem);
    transition: transform 0.1s ease-in-out, padding 0.1s ease-in-out, background-color 0.1s ease-in-out 0.1s, height 0.1s ease-in-out; }

.editor-container.active .ql-bubble .ql-tooltip .ql-toolbar,
.editor-container .ql-bubble .ql-tooltip .ql-toolbar {
  border-bottom: initial !important; }

.ql-bubble .ql-stroke,
.ql-snow .ql-stroke {
  stroke: var(--body); }

.ql-bubble .ql-fill,
.ql-bubble .ql-stroke.ql-fill,
.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill {
  fill: var(--body); }

.ql-bubble.ql-toolbar button:hover,
.ql-bubble .ql-toolbar button:hover,
.ql-bubble.ql-toolbar button:focus,
.ql-bubble .ql-toolbar button:focus,
.ql-bubble.ql-toolbar button.ql-active,
.ql-bubble .ql-toolbar button.ql-active,
.ql-bubble.ql-toolbar .ql-picker-label:hover,
.ql-bubble .ql-toolbar .ql-picker-label:hover,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active,
.ql-bubble.ql-toolbar .ql-picker-item:hover,
.ql-bubble .ql-toolbar .ql-picker-item:hover,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--primary); }

.ql-bubble.ql-toolbar button:hover .ql-stroke,
.ql-bubble .ql-toolbar button:hover .ql-stroke,
.ql-bubble.ql-toolbar button:focus .ql-stroke,
.ql-bubble .ql-toolbar button:focus .ql-stroke,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-bubble.ql-toolbar button:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar button:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar button:focus .ql-stroke-miter,
.ql-bubble .ql-toolbar button:focus .ql-stroke-miter,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: var(--primary); }

.ql-bubble.ql-toolbar button:hover .ql-fill,
.ql-bubble .ql-toolbar button:hover .ql-fill,
.ql-bubble.ql-toolbar button:focus .ql-fill,
.ql-bubble .ql-toolbar button:focus .ql-fill,
.ql-bubble.ql-toolbar button.ql-active .ql-fill,
.ql-bubble .ql-toolbar button.ql-active .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-bubble.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-bubble.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-bubble .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: var(--primary); }

.ql-bubble .ql-picker,
.ql-snow .ql-picker {
  color: var(--body); }

.ql-bubble .ql-toolbar .ql-formats,
.ql-bubble .ql-picker-label,
.ql-bubble .ql-picker {
  outline: initial; }

.ql-bubble .ql-picker.ql-expanded .ql-picker-options {
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1.5rem; }

.ql-bubble .ql-color-picker .ql-picker-options {
  width: 145px; }

.ql-bubble .ql-color-picker .ql-picker-item {
  border-radius: var(--border-radius-sm); }

.ql-bubble .ql-editor h1,
.ql-bubble .ql-editor h2,
.ql-bubble .ql-editor h3,
.ql-bubble .ql-editor h4,
.ql-bubble .ql-editor h5,
.ql-bubble .ql-editor h6,
.ql-bubble .ql-editor .h1,
.ql-bubble .ql-editor .h2,
.ql-bubble .ql-editor .h3,
.ql-bubble .ql-editor .h4,
.ql-bubble .ql-editor .h5,
.ql-bubble .ql-editor .h6,
.ql-snow .ql-editor h1,
.ql-snow .ql-editor h2,
.ql-snow .ql-editor h3,
.ql-snow .ql-editor h4,
.ql-snow .ql-editor h5,
.ql-snow .ql-editor h6,
.ql-snow .ql-editor .h1,
.ql-snow .ql-editor .h2,
.ql-snow .ql-editor .h3,
.ql-snow .ql-editor .h4,
.ql-snow .ql-editor .h5,
.ql-snow .ql-editor .h6 {
  font-family: var(--font-heading);
  color: var(--body);
  font-weight: 400;
  line-height: 1.25;
  margin-bottom: 0.75rem; }

.ql-bubble .ql-editor h1,
.ql-bubble .ql-editor .h1,
.ql-snow .ql-editor h1,
.ql-snow .ql-editor .h1 {
  font-size: 1.8em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h1,
    .ql-bubble .ql-editor .h1,
    .ql-snow .ql-editor h1,
    .ql-snow .ql-editor .h1 {
      font-size: 1.5em; } }

.ql-bubble .ql-editor h2,
.ql-bubble .ql-editor .h2,
.ql-snow .ql-editor h2,
.ql-snow .ql-editor .h2 {
  font-size: 1.65em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h2,
    .ql-bubble .ql-editor .h2,
    .ql-snow .ql-editor h2,
    .ql-snow .ql-editor .h2 {
      font-size: 1.3em; } }

.ql-bubble .ql-editor h3,
.ql-bubble .ql-editor .h3,
.ql-snow .ql-editor h3,
.ql-snow .ql-editor .h3 {
  font-size: 1.5em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h3,
    .ql-bubble .ql-editor .h3,
    .ql-snow .ql-editor h3,
    .ql-snow .ql-editor .h3 {
      font-size: 1.25em; } }

.ql-bubble .ql-editor h4,
.ql-bubble .ql-editor .h4,
.ql-snow .ql-editor h4,
.ql-snow .ql-editor .h4 {
  font-size: 1.35em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h4,
    .ql-bubble .ql-editor .h4,
    .ql-snow .ql-editor h4,
    .ql-snow .ql-editor .h4 {
      font-size: 1.15em; } }

.ql-bubble .ql-editor h5,
.ql-bubble .ql-editor .h5,
.ql-snow .ql-editor h5,
.ql-snow .ql-editor .h5 {
  font-size: 1.1em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h5,
    .ql-bubble .ql-editor .h5,
    .ql-snow .ql-editor h5,
    .ql-snow .ql-editor .h5 {
      font-size: 1em; } }

.ql-bubble .ql-editor h6,
.ql-bubble .ql-editor .h6,
.ql-snow .ql-editor h6,
.ql-snow .ql-editor .h6 {
  font-size: 1em; }
  @media (max-width: 767.98px) {
    .ql-bubble .ql-editor h6,
    .ql-bubble .ql-editor .h6,
    .ql-snow .ql-editor h6,
    .ql-snow .ql-editor .h6 {
      font-size: 1em; } }

/*
*
* Lightbox
*
* Lightbox plugin styles.
*
*/
.baguetteBox-button#close-button svg,
.baguetteBox-button#next-button svg,
.baguetteBox-button#previous-button svg {
  display: none; }

.baguetteBox-button {
  font-family: 'Plex-Regular' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  width: var(--input-height) !important;
  height: var(--input-height) !important;
  border-radius: var(--border-radius-md);
  background-color: rgba(var(--dark-text-rgb), 0.25) !important;
  box-shadow: inset 0 0 0 1px rgba(var(--light-text-rgb), 0.75) !important;
  color: rgba(var(--light-text-rgb), 0.75) !important; }
  .baguetteBox-button:hover {
    box-shadow: initial;
    background-color: var(--light-text) !important;
    color: var(--primary) !important; }

.baguetteBox-button#close-button {
  right: 2% !important;
  top: 2% !important; }
  .baguetteBox-button#close-button:before {
    content: '\e91b'; }

#baguetteBox-slider img {
  border-radius: var(--border-radius-lg); }

.baguetteBox-button#next-button,
.baguetteBox-button#previous-button {
  top: calc(50% - 18px); }

.baguetteBox-button#next-button:before {
  content: '\e917'; }

.baguetteBox-button#previous-button:before {
  content: '\e916'; }

#baguetteBox-overlay .full-image figcaption {
  padding: 15px 5px;
  background-color: initial;
  border-top: 1px solid rgba(var(--light-text-rgb), 0.25);
  background-color: rgba(0, 0, 0, 0.2) !important;
  color: var(--light-text); }

#baguetteBox-overlay .full-image img {
  box-shadow: initial !important; }

/*
*
* Listjs
*
* Listjs plugin styles.
*
*/
html[data-color*='light'] .custom-sort .sort:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-1' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

html[data-color*='light'] .custom-sort .sort.desc:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  top: 0;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.3;%7D.cls-2%7Bopacity:0.9;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

html[data-color*='light'] .custom-sort .sort.asc:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bopacity:0.9;%7D.cls-2%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

html[data-color*='dark'] .custom-sort .sort:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fff;opacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-1' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

html[data-color*='dark'] .custom-sort .sort.desc:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1,.cls-2%7Bfill:%23fff;%7D.cls-1%7Bopacity:0.3;%7D.cls-2%7Bopacity:0.9;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

html[data-color*='dark'] .custom-sort .sort.asc:after {
  content: ' ';
  display: inline-block;
  width: 10px;
  height: 10px;
  position: static;
  margin-left: 10px;
  opacity: 1;
  background-image: url("data:image/svg+xml,%3Csvg  xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 10'%3E%3Cdefs%3E%3Cstyle%3E.cls-1,.cls-2%7Bfill:%23fff;%7D.cls-1%7Bopacity:0.9;%7D.cls-2%7Bopacity:0.3;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M2.68.82h0a.27.27,0,0,0-.36,0h0L.82,2.32a.27.27,0,0,0,0,.36.27.27,0,0,0,.36,0L2.25,1.6V9a.25.25,0,0,0,.5,0V1.6L3.82,2.68a.27.27,0,0,0,.36,0,.27.27,0,0,0,0-.36Z'/%3E%3Cpath class='cls-2' d='M8.18,7.32a.27.27,0,0,0-.36,0L6.75,8.4V1a.25.25,0,0,0-.5,0V8.4L5.18,7.32a.25.25,0,0,0-.36.36l1.5,1.5a.27.27,0,0,0,.36,0l1.5-1.5A.27.27,0,0,0,8.18,7.32Z'/%3E%3C/svg%3E"); }

/*
*
* Player
*
* Plyr plugin styles.
*
*/
.plyr,
.plyr__video-wrapper,
.plyr audio,
.plyr iframe,
.plyr video,
.plyr__poster {
  border-radius: var(--border-radius-lg);
  background-color: initial; }

.plyr__poster {
  background-size: cover; }

.theme-filter-player .plyr__poster {
  filter: var(--theme-image-filter); }

.plyr video.cover {
  object-fit: cover; }

.plyr--video.plyr--stopped .plyr__controls {
  display: none; }

.plyr--video .plyr__control.plyr__tab-focus,
.plyr--video .plyr__control:hover,
.plyr--video .plyr__control[aria-expanded='true'],
.plyr--audio .plyr__control.plyr__tab-focus,
.plyr--audio .plyr__control:hover,
.plyr--audio .plyr__control[aria-expanded='true'] {
  background: var(--primary);
  color: var(--light-text); }

.plyr__control {
  color: var(--body); }

.plyr__control--overlaid {
  padding: 9px 24px;
  border-radius: var(--border-radius-md);
  background: var(--primary) !important;
  color: var(--light-text); }

.plyr--full-ui input[type='range'] {
  color: var(--primary); }

.plyr__menu__container .plyr__control[role='menuitemradio'][aria-checked='true']::before {
  background: var(--primary); }

.plyr--audio .plyr__controls {
  background: var(--foreground) !important; }

.plyr__menu__container {
  background: var(--foreground); }

.plyr__menu__container .plyr__control--back::before {
  background: var(--separator);
  box-shadow: initial; }

.plyr--audio .plyr__controls {
  color: var(--body); }

.plyr--video .plyr__control {
  color: var(--light-text); }

.modal-player .modal-content {
  background: initial;
  border: initial; }

.modal-player .plyr,
.modal-player .plyr audio,
.modal-player .plyr iframe,
.modal-player .plyr video,
.modal-player .plyr__poster,
.modal-player .plyr__video-wrapper {
  background: initial; }

.plyr__menu__container .plyr__control {
  color: var(--body); }

.card-img-top .plyr,
.card-img-top .plyr__video-wrapper,
.card-img-top .plyr audio,
.card-img-top .plyr iframe,
.card-img-top .plyr video,
.card-img-top .plyr__poster {
  border-bottom-left-radius: initial;
  border-bottom-right-radius: initial; }

.card-img-bottom .plyr,
.card-img-bottom .plyr__video-wrapper,
.card-img-bottom .plyr audio,
.card-img-bottom .plyr iframe,
.card-img-bottom .plyr video,
.card-img-bottom .plyr__poster {
  border-top-left-radius: initial;
  border-top-right-radius: initial; }

.plyr__menu__container {
  border-radius: var(--border-radius-md); }

.plyr__control {
  border-radius: var(--border-radius-md); }

.card .plyr {
  height: 100%; }

/*
*
* Glide
*
* Glide carousel plugin styles.
*
*/
.glide__track .glide__slide {
  padding-left: 0.75rem;
  padding-right: 0.75rem; }

.gx-2 .glide__track .glide__slide {
  padding-left: 0.25rem;
  padding-right: 0.25rem; }

.glide__bullets {
  display: inline-block;
  position: initial;
  text-align: center;
  transform: initial;
  left: initial;
  margin-left: 0.75rem;
  margin-right: 0.75rem; }
  .glide__bullets .glide__bullet {
    width: 6px;
    height: 6px;
    border-radius: 10px;
    background: var(--separator);
    outline: initial !important;
    border: initial;
    margin: 0 3px;
    padding: 0;
    cursor: pointer;
    box-shadow: initial; }
    .glide__bullets .glide__bullet.glide__bullet:hover, .glide__bullets .glide__bullet.glide__bullet:focus {
      background: var(--separator);
      border: initial; }
    .glide__bullets .glide__bullet.glide__bullet--active {
      background: var(--primary) !important; }

.glide-thumb .glide__slide,
.glide-large .glide__slide {
  cursor: pointer; }

.glide-thumb {
  margin: 0 auto; }
  .glide-thumb li {
    text-align: center;
    opacity: 0.5;
    object-fit: cover; }
    .glide-thumb li.active, .glide-thumb li:hover {
      opacity: 1; }
    .glide-thumb li img {
      width: 60px;
      margin-bottom: 0; }
  .glide-thumb .glide__arrows .btn {
    position: absolute;
    top: 5px; }
    .glide-thumb .glide__arrows .btn.left-arrow {
      left: -50px; }
    .glide-thumb .glide__arrows .btn.right-arrow {
      right: -50px; }

/*
*
* Notify
*
* Notify plugin styles.
*
*/
.alert *[data-notify='title'] {
  font-size: 1em;
  vertical-align: middle;
  padding-top: 3px;
  display: inline-block; }

.alert *[data-notify='icon'] {
  vertical-align: middle; }
  .alert *[data-notify='icon']:before, .alert *[data-notify='icon']:after {
    margin-right: 10px; }

.alert *[data-notify='icon'] {
  vertical-align: middle; }
  .alert *[data-notify='icon'] img {
    width: var(--input-height);
    margin-right: 10px;
    border-radius: var(--border-radius-xl); }

.alert *[data-notify='progressbar'] {
  margin-top: 10px;
  height: 2px; }

.alert *[data-notify='message'] {
  display: block; }

div[data-notify='container'] {
  padding: var(--card-spacing-xs) var(--card-spacing-sm); }
  div[data-notify='container'].alert-primary {
    background-color: var(--background);
    border: 1px solid var(--primary); }
  div[data-notify='container'].alert-secondary {
    background-color: var(--background);
    border: 1px solid var(--secondary); }
  div[data-notify='container'].alert-tertiary {
    background-color: var(--background);
    border: 1px solid var(--tertiary); }
  div[data-notify='container'].alert-quaternary {
    background-color: var(--background);
    border: 1px solid var(--quaternary); }
  div[data-notify='container'].alert-success {
    background-color: var(--background);
    border: 1px solid var(--success); }
  div[data-notify='container'].alert-danger {
    background-color: var(--background);
    border: 1px solid var(--danger); }
  div[data-notify='container'].alert-warning {
    background-color: var(--background);
    border: 1px solid var(--warning); }
  div[data-notify='container'].alert-info {
    background-color: var(--background);
    border: 1px solid var(--info); }
  div[data-notify='container'].alert-light {
    background-color: var(--background);
    border: 1px solid var(--light); }
  div[data-notify='container'].alert-dark {
    background-color: var(--background);
    border: 1px solid var(--dark); }
  div[data-notify='container'] span[data-notify='message'] {
    color: var(--alternate); }

/*
*
* Progressbar
*
* Progressbar plugin styles.
*
*/
.progress-bar-line,
.progress-bar-circle,
.progress-bar-semi-circle,
.progress-bar-square {
  position: relative; }

.progress-bar-line .progressbar-text {
  margin-top: 15px !important; }

/*
*
* Rating
*
* Rating form control styles.
*
*/
.br-theme-css-stars .br-widget {
  height: 28px;
  white-space: nowrap; }

.br-theme-css-stars .br-widget a {
  text-decoration: none;
  height: 18px;
  width: 18px;
  float: left;
  font-size: 23px;
  margin-right: 5px;
  line-height: 1; }

.br-theme-css-stars .br-widget a:after {
  content: '\2605';
  color: var(--separator); }

.br-theme-css-stars .br-widget a.br-active:after {
  color: var(--primary); }

.br-theme-css-stars .br-widget a.br-selected:after {
  color: var(--primary); }

.br-theme-css-stars .br-widget .br-current-rating {
  display: none; }

.br-theme-css-stars .br-readonly a {
  cursor: default; }

.br-theme-cs-icon .br-widget {
  height: 18px;
  white-space: nowrap; }

.br-theme-cs-icon .br-widget a {
  font-size: 18px;
  color: var(--muted);
  font-family: 'Plex-Regular' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }
  .br-theme-cs-icon .br-widget a:after {
    content: '\e90a'; }

.br-theme-cs-icon .br-widget a.br-active,
.br-theme-cs-icon .br-widget a.br-selected {
  color: var(--primary); }
  .br-theme-cs-icon .br-widget a.br-active:after,
  .br-theme-cs-icon .br-widget a.br-selected:after {
    content: '\e90b'; }

.br-theme-cs-icon .br-widget .br-current-rating {
  display: none; }

.br-theme-cs-icon .br-readonly a {
  cursor: default; }

.br-theme-cs-icon.sm .br-widget {
  height: 18px; }
  .br-theme-cs-icon.sm .br-widget a {
    font-size: 16px; }

.br-theme-bars-low .br-widget {
  white-space: nowrap; }

.br-theme-bars-low {
  max-width: 240px; }

.br-theme-bars-low .br-widget a {
  display: block;
  width: calc(20% - 2px);
  height: 8px;
  float: left;
  background-color: var(--separator);
  margin: 1px;
  border-radius: var(--border-radius-md); }

.br-theme-bars-low .br-widget a.br-active,
.br-theme-bars-low .br-widget a.br-selected {
  background-color: var(--primary); }

.br-theme-bars-low .br-widget .br-current-rating {
  clear: both;
  width: 220px;
  text-align: center;
  display: block;
  color: var(--primary);
  padding-top: 0.5rem;
  font-size: 0.8em; }

.br-theme-bars-low .br-readonly a {
  cursor: default; }

.br-theme-bars-low .br-readonly a.br-active,
.br-theme-bars-low .br-readonly a.br-selected {
  background-color: var(--primary); }

.br-theme-bars-low .br-readonly .br-current-rating {
  color: var(--primary); }

.br-theme-bars-tall .br-widget {
  white-space: nowrap; }

.br-theme-bars-tall .br-widget a {
  display: block;
  width: 8px;
  padding: 5px 0;
  height: 20px;
  float: left;
  background-color: var(--separator);
  margin: 1px;
  text-align: center;
  border-radius: var(--border-radius-md); }

.br-theme-bars-tall .br-widget a.br-active,
.br-theme-bars-tall .br-widget a.br-selected {
  background-color: var(--primary); }

.br-theme-bars-tall .br-widget .br-current-rating {
  float: left;
  padding: 0 20px 0 20px;
  color: var(--primary); }

.br-theme-bars-tall .br-readonly a {
  cursor: default; }

.br-theme-bars-tall .br-readonly a.br-active,
.br-theme-bars-tall .br-readonly a.br-selected {
  background-color: var(--primary); }

.br-theme-bars-tall .br-readonly .br-current-rating {
  color: var(--primary); }

.br-theme-button .br-widget {
  height: 15px;
  white-space: nowrap; }

.br-theme-button .br-widget a {
  display: block;
  width: var(--input-height);
  height: var(--input-height);
  border-radius: var(--border-radius-md);
  float: left;
  box-shadow: inset 0 0 0 1px var(--primary);
  background-color: var(--foreground);
  margin: 2px;
  text-align: center;
  color: var(--primary);
  padding: 11px 0;
  line-height: 1; }

.br-theme-button .br-widget a.br-active,
.br-theme-button .br-widget a.br-selected {
  color: var(--light-text);
  background: var(--primary);
  box-shadow: initial; }

.br-theme-button .br-readonly a {
  cursor: default; }

.top-label.rating-container {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  padding-top: 1.5rem !important; }
  .top-label.rating-container .br-theme-cs-icon .br-widget a {
    font-size: 16px; }
  .top-label.rating-container .br-theme-cs-icon .br-widget {
    height: 20px; }

.filled.rating-container {
  padding-top: 0;
  padding-bottom: 0; }

.text-white.br-theme-cs-icon .br-widget a.br-selected:after {
  color: var(--light-text) !important; }

.form-floating.rating-container {
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md);
  height: auto;
  min-height: 52px;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  padding-top: 1.5rem !important; }
  .form-floating.rating-container label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--muted); }
  .form-floating.rating-container .br-theme-cs-icon .br-widget a {
    font-size: 16px; }
  .form-floating.rating-container .br-theme-cs-icon .br-widget {
    height: 20px; }

/*
*
* Scrollbar
*
* Scrollbar plugin styles.
*
*/
.scroll-out .os-host {
  margin-right: -15px;
  padding-right: 15px;
  margin-left: -15px;
  padding-left: 15px;
  position: relative; }

.scroll-out-negative .os-padding {
  margin-right: 15px; }

.scroll-out-negative .os-host {
  margin-right: -15px; }

.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
.os-theme-light > .os-scrollbar > .os-scrollbar-track,
.os-theme-light > .os-scrollbar > .os-scrollbar-track {
  background: rgba(var(--muted-rgb), 0.15); }

.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
.os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--muted-rgb), 0.4); }

.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
.os-theme-light > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--muted-rgb), 0.5); }

.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
.os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
  background: rgba(var(--muted-rgb), 0.5); }

.os-theme-dark > .os-scrollbar-vertical,
.os-theme-light > .os-scrollbar-vertical {
  width: 8px; }

.os-theme-dark > .os-scrollbar-horizontal,
.os-theme-light > .os-scrollbar-horizontal {
  height: 8px; }

.scroll.fix-margin .os-scrollbar-vertical {
  height: calc(100% - 15px); }

.scroll-track-visible.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
.scroll-track-visible .os-theme-dark > .os-scrollbar > .os-scrollbar-track,
.scroll-track-visible.os-theme-light > .os-scrollbar > .os-scrollbar-track,
.scroll-track-visible .os-theme-light > .os-scrollbar > .os-scrollbar-track {
  background: rgba(var(--separator-rgb), 0.5); }

.scroll-track-visible.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible .os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible.os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible .os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--muted-rgb), 0.5); }

.scroll-track-visible.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible .os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible.os-theme-light > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
.scroll-track-visible .os-theme-light > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--muted-rgb), 0.6); }

.scroll-track-visible.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
.scroll-track-visible .os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
.scroll-track-visible.os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
.scroll-track-visible .os-theme-light > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
  background: rgba(var(--muted-rgb), 0.6); }

.select2-results ::-webkit-scrollbar, .select2-results::-webkit-scrollbar,
.tagify__dropdown ::-webkit-scrollbar,
.tagify__dropdown::-webkit-scrollbar,
.html-editor ::-webkit-scrollbar,
.html-editor::-webkit-scrollbar,
textarea ::-webkit-scrollbar,
textarea::-webkit-scrollbar,
#scrollSpyMenu ::-webkit-scrollbar,
#scrollSpyMenu::-webkit-scrollbar,
.override-native ::-webkit-scrollbar,
.override-native::-webkit-scrollbar,
.dataTables_scrollBody ::-webkit-scrollbar,
.dataTables_scrollBody::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background: transparent;
  border-radius: var(--border-radius-md);
  cursor: default !important; }

.select2-results ::-webkit-scrollbar-thumb, .select2-results::-webkit-scrollbar-thumb,
.tagify__dropdown ::-webkit-scrollbar-thumb,
.tagify__dropdown::-webkit-scrollbar-thumb,
.html-editor ::-webkit-scrollbar-thumb,
.html-editor::-webkit-scrollbar-thumb,
textarea ::-webkit-scrollbar-thumb,
textarea::-webkit-scrollbar-thumb,
#scrollSpyMenu ::-webkit-scrollbar-thumb,
#scrollSpyMenu::-webkit-scrollbar-thumb,
.override-native ::-webkit-scrollbar-thumb,
.override-native::-webkit-scrollbar-thumb,
.dataTables_scrollBody ::-webkit-scrollbar-thumb,
.dataTables_scrollBody::-webkit-scrollbar-thumb {
  border-radius: var(--border-radius-md);
  background: rgba(var(--muted-rgb), 0.4);
  cursor: default !important; }

.select2-results ::-webkit-scrollbar-button, .select2-results::-webkit-scrollbar-button,
.tagify__dropdown ::-webkit-scrollbar-button,
.tagify__dropdown::-webkit-scrollbar-button,
.html-editor ::-webkit-scrollbar-button,
.html-editor::-webkit-scrollbar-button,
textarea ::-webkit-scrollbar-button,
textarea::-webkit-scrollbar-button,
#scrollSpyMenu ::-webkit-scrollbar-button,
#scrollSpyMenu::-webkit-scrollbar-button,
.override-native ::-webkit-scrollbar-button,
.override-native::-webkit-scrollbar-button,
.dataTables_scrollBody ::-webkit-scrollbar-button,
.dataTables_scrollBody::-webkit-scrollbar-button {
  background: transparent;
  width: 4px;
  height: 3px;
  cursor: default !important; }

.select2-results ::-webkit-scrollbar-track-piece, .select2-results::-webkit-scrollbar-track-piece,
.tagify__dropdown ::-webkit-scrollbar-track-piece,
.tagify__dropdown::-webkit-scrollbar-track-piece,
.html-editor ::-webkit-scrollbar-track-piece,
.html-editor::-webkit-scrollbar-track-piece,
textarea ::-webkit-scrollbar-track-piece,
textarea::-webkit-scrollbar-track-piece,
#scrollSpyMenu ::-webkit-scrollbar-track-piece,
#scrollSpyMenu::-webkit-scrollbar-track-piece,
.override-native ::-webkit-scrollbar-track-piece,
.override-native::-webkit-scrollbar-track-piece,
.dataTables_scrollBody ::-webkit-scrollbar-track-piece,
.dataTables_scrollBody::-webkit-scrollbar-track-piece {
  background: rgba(var(--muted-rgb), 0.15);
  border-radius: var(--border-radius-md);
  cursor: default !important; }

.tagify__dropdown ::-webkit-scrollbar, .tagify__dropdown::-webkit-scrollbar {
  width: 14px; }

.tagify__dropdown ::-webkit-scrollbar-track, .tagify__dropdown::-webkit-scrollbar-track {
  box-shadow: inset 0 0 14px 14px rgba(var(--muted-rgb), 0.15);
  border: solid 5px transparent;
  background: initial !important; }

.tagify__dropdown ::-webkit-scrollbar-thumb, .tagify__dropdown::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 14px 14px rgba(var(--muted-rgb), 0.4);
  border: solid 5px transparent;
  border-radius: 14px;
  background: initial !important; }

.tagify__dropdown ::-webkit-scrollbar-track-piece, .tagify__dropdown::-webkit-scrollbar-track-piece {
  background: initial !important; }

body.custom-scrollbar ::-webkit-scrollbar, body.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
  border-radius: var(--border-radius-md);
  cursor: default !important; }

body.custom-scrollbar ::-webkit-scrollbar-thumb, body.custom-scrollbar::-webkit-scrollbar-thumb {
  border-radius: var(--border-radius-md);
  background: rgba(var(--alternate-rgb), 0.5);
  cursor: default !important; }

body.custom-scrollbar ::-webkit-scrollbar-button, body.custom-scrollbar::-webkit-scrollbar-button {
  background: transparent;
  width: 6px;
  height: 0;
  cursor: default !important; }

body.custom-scrollbar ::-webkit-scrollbar-track-piece, body.custom-scrollbar::-webkit-scrollbar-track-piece {
  background: rgba(var(--muted-rgb), 0.15);
  border-radius: var(--border-radius-md);
  cursor: default !important; }

/*
*
* Select2
*
* Select2 form control styles.
*
*/
.select2-container--bootstrap4.select2-container--focus .select2-selection {
  box-shadow: initial; }

.select2-container--bootstrap4 .select2-selection {
  color: var(--body);
  box-shadow: initial !important;
  background-color: var(--foreground);
  border: 1px solid var(--separator) !important;
  border-radius: var(--border-radius-md) !important;
  min-height: var(--input-height) !important;
  font-size: 1em; }

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0.25rem 0.75rem 0.375rem 0.75rem;
  min-height: var(--input-height); }

.select2-container .select2-search--inline .select2-search__field {
  margin-left: 0;
  margin-top: 6px;
  line-height: 1.4;
  background: initial;
  color: var(--body); }

.select2-container--open .select2-selection,
.select2-container--focus .select2-selection {
  border: 1px solid rgba(var(--primary-rgb), 1) !important; }

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
  line-height: 1.8;
  color: var(--body); }

.select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
  line-height: 1.8;
  color: var(--muted); }

.select2-container--bootstrap4 .select2-dropdown.select2-dropdown--below {
  margin-top: 3px; }

.select2-container--bootstrap4 .select2-dropdown.select2-dropdown--above {
  margin-top: -3px; }

.select2-container--bootstrap4 .select2-dropdown {
  border: 1px solid rgba(var(--primary-rgb), 1) !important;
  padding: 0.75rem;
  border-radius: var(--border-radius-md) !important;
  background: var(--foreground); }

.select2-search--dropdown .select2-search__field {
  border-radius: var(--border-radius-sm);
  height: 28px;
  font-size: 0.9em;
  padding: 0.25rem 0.75rem;
  background-color: var(--foreground);
  border-color: var(--separator);
  border-radius: var(--border-radius-sm);
  color: var(--body); }

.select2-search--dropdown {
  padding: 0;
  margin-bottom: 5px; }

.select2-results__option {
  padding: 0.5rem 0.75rem !important;
  line-height: 1.3; }

.select2-container--bootstrap4 .select2-results__option--highlighted,
.select2-container--bootstrap4 .select2-results__option--highlighted.select2-results__option[aria-selected='true'] {
  cursor: pointer;
  color: var(--primary);
  background: var(--separator-light) !important;
  border-radius: var(--border-radius-sm); }

.select2-results__option.select2-results__option--selectable.select2-results__option--selected {
  color: var(--primary) !important; }

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow b {
  border: initial;
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate);
  transform: rotate(135deg);
  width: 5px;
  height: 5px;
  margin-top: -4px; }

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
  padding: 0.1rem 0.75rem 0 0.75rem; }
  .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered .select2-search__field {
    padding-left: 0.25rem; }

.select2-selection__choice__remove {
  border: initial;
  background: initial;
  color: var(--alternate); }

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
  border-radius: var(--border-radius-sm);
  color: var(--body);
  border-color: var(--separator);
  margin-top: 5px;
  margin-right: 5px;
  line-height: 1.3; }

.select2-container .select2-search--inline {
  float: left; }

.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block; }

.select2-container--bootstrap4 .select2-dropdown .select2-results__option[aria-selected='true'] {
  background: var(--separator-light) !important;
  border-radius: var(--border-radius-sm); }

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: var(--primary) !important; }

.w-100 .select2 {
  width: 100% !important; }

.top-label .select2-selection {
  min-height: 52px !important; }

.top-label .select2-container .select2-selection--single .select2-selection__rendered {
  padding: 1.5rem 0.75rem 0.25rem 0.75rem !important; }

.top-label .select2-container .select2-selection--multiple .select2-selection__rendered {
  padding: 1.5rem 0.75rem 0rem 0.75rem !important; }
  .top-label .select2-container .select2-selection--multiple .select2-selection__rendered .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered .select2-search__field {
    padding-left: initial; }

.filled .select2-selection {
  min-height: 44px !important;
  border: 1px solid transparent !important;
  background: var(--background-light) !important; }

.filled .select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 45px;
  padding-top: 9px; }

.filled .select2-container--open .select2-selection {
  border: 1px solid rgba(var(--primary-rgb), 1) !important;
  background: initial !important; }

.filled .select2-container .select2-selection--multiple .select2-selection__rendered {
  padding-left: 45px;
  padding-top: 5px; }

.filled .select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
  color: var(--alternate); }

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove {
  color: var(--muted); }

.hide-search-searching .select2-search {
  display: none; }

.select2-container--bootstrap4.select2-container--disabled .select2-selection {
  background: rgba(var(--separator-rgb), 0.5) !important;
  color: var(--muted);
  border-color: var(--separator); }

.form-floating .select2-selection {
  height: auto;
  min-height: 52px !important;
  padding: 1rem 0rem; }

.form-floating .select-floating ~ label {
  -webkit-transform: initial;
  transform: initial;
  color: var(--alternate);
  transition: initial; }

.form-floating .select2.full ~ label,
.form-floating .select2.show ~ label {
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--muted);
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out; }

.form-floating .select2.full ~ .select2 .select2-selection,
.form-floating .select2.show ~ .select2 .select2-selection {
  padding-top: 1.25rem;
  padding-bottom: 0; }

.option-circle {
  width: 15px;
  height: 15px;
  border: 1px solid var(--primary); }

.select2-dropdown {
  z-index: 1061; }

/*
*
* Slider
*
* Noui slider form control styles.
*
*/
.noUi-horizontal {
  height: 8px; }

.noUi-target {
  border-radius: var(--border-radius-sm);
  border-color: var(--separator);
  background: var(--foreground);
  box-shadow: initial; }

.noUi-horizontal .noUi-handle {
  width: 18px;
  height: 18px;
  right: -9px;
  top: -6px;
  border-radius: var(--border-radius-md);
  outline: initial !important; }

.noUi-handle:after,
.noUi-handle:before {
  height: 6px;
  left: 6px;
  top: 5px;
  background: rgba(var(--primary-rgb), 0.5); }

.noUi-handle:after {
  left: 9px; }

.noUi-connect {
  background: var(--primary); }

.noUi-handle {
  box-shadow: initial;
  border-color: var(--separator);
  background: var(--foreground); }

.noUi-vertical {
  width: 8px; }

.noUi-vertical .noUi-handle {
  width: 18px;
  height: 18px;
  right: -6px;
  top: -6px;
  border-radius: var(--border-radius-md);
  outline: initial !important; }

.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  width: 6px;
  left: 5px;
  top: 6px;
  background: rgba(var(--primary-rgb), 0.5); }

.noUi-vertical .noUi-handle:after {
  top: 9px; }

.noUi-tooltip {
  background: var(--primary);
  color: var(--light-text);
  padding: calc(var(--card-spacing-xs) / 2) var(--card-spacing-xs);
  border-radius: var(--border-radius-md);
  border: initial;
  font-size: 0.875em;
  margin-bottom: 4px;
  display: table !important; }
  .noUi-tooltip:after {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: transparent;
    border-top-color: var(--primary);
    border-width: 4px;
    margin-left: -4px; }

.noUi-horizontal .noUi-tooltip {
  bottom: initial !important;
  top: 140%; }
  .noUi-horizontal .noUi-tooltip:after {
    border-bottom-color: var(--primary);
    border-top-color: transparent; }

.noUi-pips,
.noUi-value-sub {
  color: var(--alternate); }

.noUi-marker,
.noUi-marker-large {
  background: var(--separator); }

.noUi-vertical .noUi-tooltip {
  margin-bottom: 0;
  margin-left: 5px;
  right: initial;
  left: 120%; }
  .noUi-vertical .noUi-tooltip:after {
    top: 50%;
    left: -8px;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: transparent;
    border-right-color: var(--primary);
    border-width: 4px;
    margin-left: 0;
    margin-top: -4px; }

.tooltip-start .noUi-vertical .noUi-tooltip {
  margin-bottom: 0;
  margin-right: 5px;
  margin-left: initial;
  right: 120%;
  left: initial; }
  .tooltip-start .noUi-vertical .noUi-tooltip:after {
    top: 50%;
    left: initial;
    right: -8px;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: transparent;
    border-left-color: var(--primary);
    border-width: 4px;
    margin-left: 0;
    margin-top: -4px; }

[disabled] .noUi-connect {
  background: var(--separator); }

[disabled] .noUi-handle:after,
[disabled] .noUi-handle:before {
  background: var(--separator); }

.top-label.custom-control-container.slider-container {
  padding: 2.25rem 0.75rem 0.15rem 0.75rem !important; }

.filled.custom-control-container.slider-container {
  padding-top: 1.25rem; }

.noUi-horizontal .noUi-handle {
  right: -9px; }

.noUi-horizontal.noUi-target {
  padding: 0 8px; }

.noUi-horizontal .noUi-connects {
  margin: 0 -8px;
  width: calc(100% + 16px); }

.noUi-vertical .noUi-handle {
  top: initial;
  height: 16px;
  bottom: initial; }

.noUi-vertical.noUi-target {
  padding: 8px 0; }

.noUi-vertical .noUi-connects {
  margin: -8px 0;
  height: calc(100% + 16px); }

.form-floating.slider-container {
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md);
  height: auto;
  min-height: 52px;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  padding-top: 2.25rem !important; }
  .form-floating.slider-container label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--muted); }

/*
*
* Sortable
*
* Sortable plugin styles.
*
*/
.sortable * {
  cursor: move;
  cursor: -webkit-grabbing; }

/*
*
* Steps
*
* Styles for steps ui elements.
*
*/
.line-w-1 {
  width: 1px; }

.line-h-1 {
  height: 1px; }

/*
*
* Tagify
*
* Tagify plugin styles.
*
*/
.tagify {
  --tags-border-color: var(--separator);
  --tags-hover-border-color: var(--separator);
  --tags-focus-border-color: rgba(var(--primary-rgb), 1);
  --tag-bg: var(--foreground);
  --tag-text-color: var(--body);
  --tag-text-color--edit: var(--body);
  --tag-pad: 0 5px;
  --tag-invalid-color: var(--danger);
  --tag-invalid-bg: var(--foreground);
  --tag-remove-bg: var(--foreground);
  --tag-remove-btn-color: var(--body);
  --tag-remove-btn-bg: none;
  --tag-remove-btn-bg--hover: var(--danger);
  --input-color: inherit;
  --tag--max-width: auto;
  --tag-hide-transition: 0s;
  --placeholder-color: var(--muted);
  --placeholder-color-focus: var(--muted);
  --loader-size: 0.8em; }

.tagify {
  border-radius: var(--border-radius-md);
  line-height: 1.5;
  min-height: var(--input-height);
  padding-left: 0.5rem;
  transition-duration: initial !important;
  padding-right: 10px;
  padding-top: 0;
  padding-bottom: 0; }
  .tagify * {
    transition-duration: initial !important;
    transition-delay: initial !important; }
  .tagify .tagify__input {
    padding: 0.4rem 0.25rem 0.4rem 0.25rem;
    margin: initial;
    transition-duration: initial !important;
    transition-delay: initial !important; }
    .tagify .tagify__input:focus:before {
      transition-duration: initial !important;
      transition-delay: initial !important; }
    .tagify .tagify__input:empty::before {
      transition-duration: initial !important;
      transition-delay: initial !important; }

.tagify__tag > div > * {
  transition-duration: initial !important;
  transition-delay: initial !important; }

.tagify__tag {
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--separator) !important;
  margin: 7px 5px 0 0;
  box-shadow: initial !important; }
  .tagify__tag:first-of-type {
    margin-left: 1px; }

.tagify__tag > div {
  height: 18px;
  box-shadow: initial !important;
  border: initial !important;
  background: initial !important; }

.tagify__tag > div > * {
  line-height: 1.3; }

.tagify__tag > div::before {
  box-shadow: initial !important;
  border: initial !important;
  background: initial !important; }

.tagify__tag__removeBtn {
  color: var(--muted) !important;
  background: initial !important;
  font: inherit;
  font-size: 1em;
  margin-top: -2px; }

.tagify__tag__removeBtn:hover + div::before {
  box-shadow: initial !important; }

.tagify__tag__removeBtn:hover + div > span {
  opacity: initial; }

.tagify__tag__removeBtn:hover {
  color: var(--primary) !important; }

.tagify__tag__removeBtn::after {
  content: 'x';
  line-height: 1; }

.tagify__input::before {
  line-height: 1.5; }

.tagify__dropdown[placement='bottom'] {
  margin-top: 3px; }

.tagify__dropdown[placement='top'] {
  margin-top: -5px; }

.tagify__dropdown[placement='top'] .tagify__dropdown__wrapper {
  border: 1px solid rgba(var(--primary-rgb), 1); }

.tagify__dropdown__wrapper {
  border: 1px solid rgba(var(--primary-rgb), 1);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
  background: var(--foreground); }

.tagify__dropdown__item {
  padding: 0.5rem 0.75rem !important;
  line-height: 1.3;
  margin: 0;
  border-radius: var(--border-radius-sm); }
  .tagify__dropdown__item:hover, .tagify__dropdown__item.tagify__dropdown__item--active {
    background: var(--separator-light) !important;
    color: var(--primary); }

.custom-look .tagify__input {
  display: none; }

.custom-look {
  border: initial;
  padding: initial;
  display: inline; }
  .custom-look .tagify__tag {
    margin-top: 0;
    border-radius: var(--border-radius-md);
    height: var(--input-height);
    padding: 0.4rem 0.75rem 0.375rem 0.75rem;
    margin: initial;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem; }
  .custom-look .tagify__tag__removeBtn {
    margin-left: 0;
    margin-right: 0; }
  .custom-look .tagify__tag > div {
    height: initial;
    padding-left: 0; }
    .custom-look .tagify__tag > div .tagify__tag-text {
      line-height: 1.5; }
  .custom-look .tagify__input {
    display: none; }

.tagify__dropdown[position='text'] {
  margin-left: -23px;
  margin-top: 13px; }

.tagify--mix.tagify {
  padding: initial; }

.tagify--mix .tagify__input {
  padding: 0.45rem 0.75rem 0.45rem 0.75rem; }
  .tagify--mix .tagify__input .tagify__tag {
    line-height: 1.25; }

.tagify--mix .tagify__input .tagify__tag:first-of-type {
  margin-left: -0.1rem; }

.tagify--outside {
  border: 0;
  padding: initial; }
  .tagify--outside .tagify__tag:first-of-type {
    margin-left: initial; }

.tagify--outside .tagify__input {
  order: -1;
  flex: 100%;
  margin-bottom: initial;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--separator) !important;
  padding: 0.4rem 0.75rem 0.375rem 0.75rem; }

.customSuggestionsList > div {
  max-height: 300px;
  margin-top: 3px;
  overflow: auto; }

.tagify__dropdown.users-list .tagify__dropdown__item {
  padding: 0.5em 0.7em;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0 1em;
  grid-template-areas: 'avatar name' 'avatar email'; }

.tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  grid-area: avatar;
  width: var(--input-height);
  height: var(--input-height);
  border-radius: var(--border-radius-xl);
  overflow: hidden; }

.tagify__dropdown.users-list img {
  width: 100%;
  vertical-align: top; }

.tagify__dropdown.users-list strong {
  grid-area: name;
  width: 100%;
  align-self: center; }

.tagify__dropdown.users-list span {
  grid-area: email;
  width: 100%;
  font-size: 0.9em;
  opacity: 0.6; }

.tagify__dropdown.users-list .addAll {
  gap: 0; }

.users-list-container .tagify__tag {
  white-space: nowrap; }

.users-list-container .tagify__tag .tagify__tag__avatar-wrap {
  width: 18px;
  height: 18px;
  border-radius: var(--border-radius-sm);
  margin-right: 5px; }

.users-list-container .tagify__tag > div {
  padding-left: initial; }

.users-list-container .tagify__tag img {
  width: 100%;
  vertical-align: top; }

.tagify__dropdown.extra-properties .tagify__dropdown__item > img {
  display: inline-block;
  vertical-align: middle;
  height: 20px;
  transform: scale(0.75);
  margin-right: 5px;
  border-radius: 2px; }

.tagify.countries .tagify__input {
  min-width: 175px; }

.tagify.countries tag {
  white-space: nowrap; }

.tagify.countries tag img {
  display: inline-block;
  height: 18px;
  margin-right: 5px;
  border-radius: 2px;
  border-radius: var(--border-radius-sm); }

.tagify.countries tag div {
  padding-left: initial; }

.tagify[readonly]:not(.tagify--mix) > .tagify__input {
  margin: initial; }

.tagify[readonly]:not(.tagify--mix) .tagify__tag > div {
  color: var(--muted); }

.tagify[readonly]:not(.tagify--mix) {
  background: rgba(var(--separator-rgb), 0.5) !important; }

.tagify__tag[readonly] {
  background: rgba(var(--separator-rgb), 0.5) !important;
  color: var(--muted); }

.tagify--select::after {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  opacity: 1;
  right: 10px;
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate);
  transform: rotate(135deg);
  bottom: initial;
  top: 12px;
  transition: initial !important; }

.tagify--select[aria-expanded='true']::after {
  transform: rotate(-45deg);
  top: 13px; }

.tagify--select .tagify__tag__removeBtn {
  margin-top: -4px;
  margin-right: initial; }

.top-label .tagify {
  padding-top: 1rem;
  min-height: 52px; }

.top-label .tagify__tag:first-of-type {
  margin-left: 5px; }

.top-label .tagify__tag {
  margin-top: 9px; }

.top-label .tagify .tagify__input {
  padding: 0.5rem 0.25rem 0.25rem 0.25rem; }

.filled .tagify {
  --placeholder-color: var(--alternate);
  padding-top: 4px;
  padding-bottom: 4px;
  min-height: 44px !important;
  padding-left: 41px;
  background: var(--background-light) !important;
  border-color: transparent; }
  .filled .tagify.tagify--focus {
    border-color: rgba(var(--primary-rgb), 1);
    background: var(--foreground) !important; }

.filled .tagify__tag:first-of-type {
  margin-left: 5px; }

.tagify--select .tagify__tag {
  border: initial !important;
  margin: 0; }

.tagify__tag--editable.tagify--invalid > div::before {
  box-shadow: initial !important; }

.form-floating .tagify__tag {
  margin-top: 5px; }

.form-floating .tagify__input {
  padding: 0.25rem 0.25rem 0.25rem 0.25rem; }

.form-floating .tagify {
  padding-top: 1.4rem;
  padding-bottom: 0;
  height: auto;
  min-height: 52px;
  padding: 1rem 0.75rem; }
  .form-floating .tagify ~ label {
    -webkit-transform: initial;
    transform: initial;
    color: var(--alternate);
    transition: initial; }

.form-floating .tagify--empty {
  padding: 0.6rem 0.25rem 0.5rem 0.5rem; }
  .form-floating .tagify--empty ~ label {
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out; }

.form-floating .tagify--focus,
.form-floating .tagify:not(.tagify--empty) {
  padding-top: 1.25rem;
  padding-bottom: 0; }
  .form-floating .tagify--focus ~ label,
  .form-floating .tagify:not(.tagify--empty) ~ label {
    -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--muted);
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out; }

.form-floating .tagify__tag:first-of-type {
  margin-left: initial; }

/*
*
* Time Picker
*
* Time Picker form control styles.
*
*/
.time-picker {
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: 0 !important;
  border: initial !important;
  background: initial !important;
  overflow: hidden !important;
  position: absolute !important;
  white-space: nowrap !important;
  display: none; }

.time-picker-container .select2-container {
  width: 60px !important; }

.time-picker-container .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
  text-align: center;
  padding-right: 1.25rem;
  padding-left: 0.75rem; }

.time-picker-container label {
  display: block; }

.time-picker-container .select2 {
  display: inline-block;
  margin-right: 0.25rem; }

.time-picker-dropdown.select2-dropdown {
  padding: 0.75rem 0.25rem 0.75rem 0.25rem; }
  .time-picker-dropdown.select2-dropdown .select2-results__option {
    margin-left: 0.25rem;
    margin-right: 0.25rem;
    text-align: center; }

.top-label.custom-control-container.time-picker-container {
  padding: 1.25rem 0.75rem 0 0.75rem !important; }
  .top-label.custom-control-container.time-picker-container .select2-selection {
    min-height: unset !important;
    border: initial !important; }
  .top-label.custom-control-container.time-picker-container .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0.5rem 0.25rem 0 0.25rem !important;
    text-align: left; }
  .top-label.custom-control-container.time-picker-container .select2-container {
    width: 50px !important;
    margin-right: -10px !important; }
  .top-label.custom-control-container.time-picker-container .select2 {
    margin-right: initial; }
  .top-label.custom-control-container.time-picker-container .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    right: 7px;
    margin-top: 4px; }
  .top-label.custom-control-container.time-picker-container .select2-container .select2-selection--single .select2-selection__rendered {
    min-height: 30px !important;
    height: unset !important;
    line-height: 1.5; }
  .top-label.custom-control-container.time-picker-container .select2-container--bootstrap4 .select2-selection--single {
    min-height: unset !important;
    height: unset !important;
    background: transparent; }
  .top-label.custom-control-container.time-picker-container.focus {
    border-color: rgba(var(--primary-rgb), 1) !important; }

.filled.custom-control-container.time-picker-container {
  padding-top: 0.275rem;
  padding-bottom: 0.275rem; }
  .filled.custom-control-container.time-picker-container .select2-selection {
    min-height: unset !important;
    background: initial !important; }
  .filled.custom-control-container.time-picker-container .select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: initial !important; }
  .filled.custom-control-container.time-picker-container .select2-container--open .select2-selection {
    border-color: transparent !important; }
  .filled.custom-control-container.time-picker-container .select2-container .select2-selection--single .select2-selection__rendered {
    padding-top: 4px;
    text-align: left; }
  .filled.custom-control-container.time-picker-container .select2-container {
    width: 50px !important;
    margin-right: -8px !important; }
  .filled.custom-control-container.time-picker-container .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    right: 10px;
    margin-top: 2px; }
  .filled.custom-control-container.time-picker-container.focus {
    border-color: rgba(var(--primary-rgb), 1) !important;
    background: initial !important; }

.select2-dropdown.select2-dropdown--below.time-top-label-dropdown {
  width: 60px !important;
  left: -7px !important;
  margin-top: 5px !important; }

.select2-dropdown.select2-dropdown--above.time-top-label-dropdown {
  width: 60px !important;
  left: -7px !important;
  margin-top: -22px !important; }

.select2-dropdown.select2-dropdown--below.time-filled-dropdown {
  width: 60px !important;
  left: -19px !important;
  margin-top: 8px !important; }

.select2-dropdown.select2-dropdown--above.time-filled-dropdown {
  width: 60px !important;
  left: -19px !important;
  margin-top: -7px !important; }

/*
*
* Tour
*
* Introjs plugin styles.
*
*/
.introjs-helperLayer {
  padding: 1rem;
  transform: translate(-1rem, -1rem);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--primary) 0px 0px 0 0, rgba(0, 0, 0, 0.6) 0px 0px 0px 5000px !important; }

.introjs-tooltip {
  border-radius: var(--border-radius-lg);
  padding: var(--card-spacing-xs);
  background: var(--foreground); }

.introjs-overlay {
  background: #000 !important; }

.introjs-button {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-family: var(--font);
  padding: 9px 20px;
  height: var(--input-height);
  font-size: 1em;
  line-height: 1rem;
  border-radius: var(--border-radius-md);
  border: initial !important;
  box-shadow: initial !important;
  transition: all var(--transition-time-short);
  transition-property: color, background-color, background-image, background;
  border-radius: var(--border-radius-md);
  color: var(--light-text) !important;
  background-color: initial !important;
  background-image: initial !important;
  text-shadow: initial;
  box-shadow: inset 0px 0px 0px 1px var(--primary) !important;
  color: var(--primary) !important;
  margin: 3px;
  margin-bottom: 0; }
  .introjs-button i {
    font-size: 14px;
    width: 14px;
    vertical-align: middle;
    display: inline-block; }
  .introjs-button span {
    vertical-align: middle;
    display: inline-block;
    margin-left: 4px;
    margin-right: 4px; }
  .introjs-button:hover {
    color: var(--light-text) !important;
    background-color: var(--primary) !important;
    box-shadow: initial !important; }
  .introjs-button.introjs-disabled {
    opacity: 0.5;
    cursor: initial;
    pointer-events: none; }

.introjs-bullets {
  margin-bottom: 15px; }

.introjs-tooltiptext {
  font-family: var(--font-heading);
  margin-bottom: 15px; }

.introjs-tooltipbuttons {
  border-top: 1px solid var(--separator);
  text-align: center; }

.introjs-bullets ul li a {
  background: var(--separator); }

.introjs-bullets ul li a.active {
  background: var(--primary); }

.introjs-tooltipReferenceLayer *,
.introjs-tooltipReferenceLayer {
  font-family: var(--font); }

.introjs-tooltip-title {
  font-family: var(--font-heading);
  font-weight: 400;
  font-size: 1.1rem; }

.introjs-tooltip-header {
  padding-right: 10px; }

.introjs-arrow.top {
  left: 15px; }

.introjs-arrow.left {
  top: 15px;
  border-right-color: var(--foreground); }

.introjs-arrow.right-bottom {
  bottom: 15px;
  border-left-color: var(--foreground); }

.introjs-arrow.bottom {
  left: 15px;
  border-top-color: var(--foreground); }

.introjs-arrow.top,
.introjs-arrow.top-middle,
.introjs-arrow.top-right {
  border-bottom-color: var(--foreground); }

.introjs-skipbutton {
  color: var(--alternate); }
  .introjs-skipbutton:hover {
    color: var(--primary); }

/*
*
* Validation
*
* Validation plugin styles.
*
*/
.invalid-tooltip,
.valid-tooltip,
div.error {
  border-radius: var(--border-radius-md);
  font-size: 0.875em;
  color: var(--light-text);
  background: var(--primary);
  border: initial;
  text-align: center;
  width: unset !important;
  position: absolute;
  z-index: 4;
  margin-top: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  line-height: 1.5;
  padding: calc(var(--card-spacing-xs) / 3 * 2) var(--card-spacing-xs); }
  .invalid-tooltip::before,
  .valid-tooltip::before,
  div.error::before {
    content: '';
    position: absolute;
    top: 50%;
    top: -4px;
    left: -2.5px;
    margin-left: 50%;
    width: 10px;
    height: 5px;
    border-bottom: solid 5px var(--primary);
    border-left: solid 5px transparent;
    border-right: solid 5px transparent; }

.tooltip-end-bottom .invalid-tooltip,
.tooltip-end-bottom .valid-tooltip,
.tooltip-end-bottom div.error {
  left: initial;
  right: 0;
  transform: translateX(0);
  top: calc(100% + 0.5rem); }
  .tooltip-end-bottom .invalid-tooltip::before,
  .tooltip-end-bottom .valid-tooltip::before,
  .tooltip-end-bottom div.error::before {
    left: initial;
    margin-top: initial;
    right: 25px;
    margin-left: 0;
    top: -4px; }

.tooltip-center-bottom .invalid-tooltip,
.tooltip-center-bottom .valid-tooltip,
.tooltip-center-bottom div.error {
  left: 50%;
  right: initial;
  transform: translateX(-50%) translateY(0);
  top: calc(100% + 0.5rem); }
  .tooltip-center-bottom .invalid-tooltip::before,
  .tooltip-center-bottom .valid-tooltip::before,
  .tooltip-center-bottom div.error::before {
    margin-top: initial;
    top: -4px; }

.tooltip-start-bottom .invalid-tooltip,
.tooltip-start-bottom .valid-tooltip,
.tooltip-start-bottom div.error {
  left: 0;
  right: initial;
  transform: translateX(0);
  top: calc(100% + 0.5rem); }
  .tooltip-start-bottom .invalid-tooltip::before,
  .tooltip-start-bottom .valid-tooltip::before,
  .tooltip-start-bottom div.error::before {
    margin-top: initial;
    left: 25px;
    right: initial;
    margin-left: 0;
    top: -4px; }

.tooltip-center-top .invalid-tooltip,
.tooltip-center-top .valid-tooltip,
.tooltip-center-top div.error {
  bottom: initial;
  transform: translateX(-50%) translateY(50%);
  top: -0.75rem; }
  .tooltip-center-top .invalid-tooltip::before,
  .tooltip-center-top .valid-tooltip::before,
  .tooltip-center-top div.error::before {
    margin-top: initial;
    content: '';
    position: absolute;
    top: initial;
    bottom: -4px;
    border-top: solid 5px var(--primary);
    border-bottom: initial; }

.tooltip-end-top .invalid-tooltip,
.tooltip-end-top .valid-tooltip,
.tooltip-end-top div.error {
  bottom: initial;
  top: -0.75rem;
  transform: translateX(0) translateY(50%);
  left: initial;
  right: 0; }
  .tooltip-end-top .invalid-tooltip::before,
  .tooltip-end-top .valid-tooltip::before,
  .tooltip-end-top div.error::before {
    content: '';
    margin-top: initial;
    position: absolute;
    top: initial;
    bottom: -4px;
    border-top: solid 5px var(--primary);
    border-bottom: initial;
    left: initial;
    right: 25px;
    margin-left: 0; }

.tooltip-start-top .invalid-tooltip,
.tooltip-start-top .valid-tooltip,
.tooltip-start-top div.error {
  bottom: initial;
  transform: translateX(0) translateY(50%);
  top: -0.75rem;
  left: 0;
  right: initial; }
  .tooltip-start-top .invalid-tooltip::before,
  .tooltip-start-top .valid-tooltip::before,
  .tooltip-start-top div.error::before {
    content: '';
    margin-top: initial;
    position: absolute;
    top: initial;
    bottom: -4px;
    border-top: solid 5px var(--primary);
    border-bottom: initial;
    left: 25px;
    right: initial;
    margin-left: 0; }

.tooltip-label-end .invalid-tooltip,
.tooltip-label-end .valid-tooltip,
.tooltip-label-end div.error {
  transform: translateX(0) translateY(-50%);
  top: 18px;
  bottom: initial;
  right: initial; }
  .tooltip-label-end .invalid-tooltip::before,
  .tooltip-label-end .valid-tooltip::before,
  .tooltip-label-end div.error::before {
    content: '';
    position: absolute;
    left: -5px;
    right: initial;
    margin-left: 0;
    border: initial;
    border-top: solid 5px transparent;
    border-bottom: solid 5px transparent;
    border-right: solid 5px var(--primary);
    bottom: initial;
    top: 50%;
    margin-top: -5px;
    width: 5px; }

.is-invalid .invalid-tooltip {
  display: block; }

.valid-icon {
  position: absolute;
  bottom: 8px;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.5rem;
  margin-top: 0;
  font-size: 0.76rem;
  line-height: 1;
  color: var(--foreground);
  border-radius: 0.2rem;
  right: 4px;
  color: var(--success); }

.invalid-icon {
  position: absolute;
  bottom: 2px;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.5rem;
  margin-top: 0;
  font-size: 0.875em;
  line-height: 1;
  color: var(--foreground);
  border-radius: 0.2rem;
  right: 4px;
  color: var(danger); }

.was-validated .form-control:invalid,
.form-control.is-invalid,
.was-validated .custom-select:invalid,
.custom-select.is-invalid {
  border-color: var(--separator) !important; }

.was-validated .form-control:valid,
.form-control.is-valid,
.was-validated .custom-select:valid,
.custom-select.is-valid {
  border-color: var(--separator) !important; }

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus,
.was-validated .custom-select:valid:focus,
.custom-select.is-valid:focus {
  border-color: rgba(var(--primary), 0.6); }

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus,
.was-validated .custom-select:invalid:focus,
.custom-select.is-invalid:focus {
  border-color: rgba(var(--primary), 0.6); }

.was-validated .form-control:invalid,
.form-control.is-invalid,
.was-validated .form-control:valid,
.form-control.is-valid,
.was-validated .custom-select:invalid,
.custom-select.is-invalid,
.was-validated .custom-select:valid,
.custom-select.is-valid {
  background-image: initial; }

.tooltip-end-top .filled .invalid-tooltip,
.tooltip-end-top .filled .valid-tooltip,
.tooltip-end-top .filled div.error,
.tooltip-center-top .filled .invalid-tooltip,
.tooltip-center-top .filled .valid-tooltip,
.tooltip-center-top .filled div.error,
.tooltip-start-top .filled .invalid-tooltip,
.tooltip-start-top .filled .valid-tooltip,
.tooltip-start-top .filled div.error {
  top: -35px;
  bottom: initial; }

.tooltip-end-top.filled .invalid-tooltip,
.tooltip-end-top.filled .valid-tooltip,
.tooltip-end-top.filled div.error,
.tooltip-center-top.filled .invalid-tooltip,
.tooltip-center-top.filled .valid-tooltip,
.tooltip-center-top.filled div.error,
.tooltip-start-top.filled .invalid-tooltip,
.tooltip-start-top.filled .valid-tooltip,
.tooltip-start-top.filled div.error {
  top: -35px;
  bottom: initial; }

.tooltip-end-top .top-label .invalid-tooltip,
.tooltip-end-top .top-label .valid-tooltip,
.tooltip-end-top .top-label div.error,
.tooltip-end-top .form-floating .invalid-tooltip,
.tooltip-end-top .form-floating .valid-tooltip,
.tooltip-end-top .form-floating div.error,
.tooltip-center-top .top-label .invalid-tooltip,
.tooltip-center-top .top-label .valid-tooltip,
.tooltip-center-top .top-label div.error,
.tooltip-center-top .form-floating .invalid-tooltip,
.tooltip-center-top .form-floating .valid-tooltip,
.tooltip-center-top .form-floating div.error,
.tooltip-start-top .top-label .invalid-tooltip,
.tooltip-start-top .top-label .valid-tooltip,
.tooltip-start-top .top-label div.error,
.tooltip-start-top .form-floating .invalid-tooltip,
.tooltip-start-top .form-floating .valid-tooltip,
.tooltip-start-top .form-floating div.error {
  top: -35px;
  bottom: initial; }

.tooltip-end-top.top-label .invalid-tooltip,
.tooltip-end-top.top-label .valid-tooltip,
.tooltip-end-top.top-label div.error, .tooltip-end-top.form-floating .invalid-tooltip,
.tooltip-end-top.form-floating .valid-tooltip,
.tooltip-end-top.form-floating div.error,
.tooltip-center-top.top-label .invalid-tooltip,
.tooltip-center-top.top-label .valid-tooltip,
.tooltip-center-top.top-label div.error,
.tooltip-center-top.form-floating .invalid-tooltip,
.tooltip-center-top.form-floating .valid-tooltip,
.tooltip-center-top.form-floating div.error,
.tooltip-start-top.top-label .invalid-tooltip,
.tooltip-start-top.top-label .valid-tooltip,
.tooltip-start-top.top-label div.error,
.tooltip-start-top.form-floating .invalid-tooltip,
.tooltip-start-top.form-floating .valid-tooltip,
.tooltip-start-top.form-floating div.error {
  top: -35px;
  bottom: initial; }

/*
*
* Wizard
*
* Styles for wizard ui.
*
*/
.wizard .card-header {
  padding-top: var(--card-spacing-sm); }

.wizard .nav-tabs {
  position: relative;
  border: initial; }
  .wizard .nav-tabs:before {
    content: ' ';
    position: absolute;
    bottom: 1px;
    width: 100%;
    height: 1px;
    z-index: 0;
    background: rgba(var(--separator-rgb), 0.8); }

.wizard .nav-tabs.disabled .nav-link {
  pointer-events: none; }

.wizard .nav-tabs .nav-link {
  border: initial;
  position: relative;
  background: initial;
  margin: initial;
  padding-bottom: 20px !important; }
  .wizard .nav-tabs .nav-link .title {
    color: var(--alternate); }
  .wizard .nav-tabs .nav-link .description {
    color: var(--separator); }
  .wizard .nav-tabs .nav-link.active .title {
    color: var(--primary); }
  .wizard .nav-tabs .nav-link.active .description {
    color: var(--primary); }
  .wizard .nav-tabs .nav-link.active:before {
    background: var(--primary); }
  .wizard .nav-tabs .nav-link:before {
    content: ' ';
    position: absolute;
    border: initial;
    background: var(--separator);
    width: 18px;
    height: 18px;
    z-index: 1;
    display: block;
    border-radius: var(--border-radius-lg);
    color: initial;
    text-decoration: none;
    left: 50%;
    transform: translateX(-50%);
    bottom: -7px; }
  .wizard .nav-tabs .nav-link.done:before {
    background: var(--primary); }
  .wizard .nav-tabs .nav-link.done:after {
    content: ' ';
    width: 8px;
    height: 8px;
    left: 50%;
    bottom: -2px;
    transform: translateX(-50%);
    z-index: 2;
    position: absolute;
    display: block;
    background-repeat: no-repeat;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e"); }
  .wizard .nav-tabs .nav-link:not([disabled='true']):hover .title {
    color: var(--primary); }
  .wizard .nav-tabs .nav-link:not([disabled='true']):hover .description {
    color: var(--primary); }
  .wizard .nav-tabs .nav-link[disabled='true'] {
    cursor: default; }

/*
*
* Base
*
* Template styles for base core items such as root, html and body.
*
*/
:root {
  scroll-behavior: initial; }

html {
  height: 100%;
  width: 100%;
  margin: 0;
  font-size: 16px; }

body {
  width: 100%;
  margin: 0;
  font-family: var(--font);
  font-weight: 400;
  color: var(--body);
  background-color: var(--background);
  font-size: 87.5%; }

#root {
  opacity: 0; }

html:not([data-show='true']) body.spinner #root {
  opacity: 0; }

html:not([data-show='true']) body.spinner:after {
  border-color: #7d7d7d;
  border-right-color: transparent; }

html[data-show='true'] {
  scroll-behavior: smooth; }
  html[data-show='true'] #root {
    opacity: 1; }

/*
*
* Typography
*
* Template styles for typography.
*
*/
p {
  font-family: var(--font); }

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: var(--font-heading);
  color: var(--body);
  font-weight: 400;
  line-height: 1.25; }

.font-standard {
  font-family: var(--font); }

.font-heading {
  font-family: var(--font-heading); }

.text-medium {
  font-size: 0.9em !important; }

.text-semi-large {
  font-size: 1.1em !important; }

.text-xlarge {
  font-size: 2.7em !important;
  font-weight: 300 !important; }

.text-small {
  font-size: 0.75em !important;
  font-weight: initial !important; }

.text-extra-small {
  font-size: 0.7em !important;
  line-height: 1em !important; }

.text-large {
  font-size: 1.75em !important;
  font-weight: 300 !important; }

.font-weight-bold {
  font-weight: 600; }

[class*='cs-'] {
  font-size: 18px; }

.heading {
  font-size: 1.1em;
  font-weight: 500;
  margin-bottom: 0.7em; }

.small-title {
  font-family: var(--font-heading);
  font-weight: 400;
  margin-bottom: 0;
  font-size: 1.2em;
  color: var(--primary);
  height: var(--small-title-height); }

.blockquote {
  font-size: 1em; }

.display-1 {
  font-family: var(--font-heading);
  font-size: 3.5em;
  line-height: 1.2;
  font-weight: 300; }
  @media (max-width: 767.98px) {
    .display-1 {
      font-size: 3em; } }

.display-2 {
  font-family: var(--font-heading);
  font-size: 3em;
  line-height: 1.2;
  font-weight: 300; }
  @media (max-width: 767.98px) {
    .display-2 {
      font-size: 2.75em; } }

.display-3 {
  font-family: var(--font-heading);
  font-size: 2.15em;
  line-height: 1.2;
  font-weight: 300; }
  @media (max-width: 767.98px) {
    .display-3 {
      font-size: 2em; } }

.display-4 {
  font-family: "Plex-Light";
  font-size: 1.85em;
  line-height: 1.2;
  font-weight: 300; }
  @media (max-width: 991.98px) {
    .display-4 {
      font-size: 1.7em; } }
  @media (max-width: 767.98px) {
    .display-4 {
      font-size: 1.6em; } }

.display-5 {
  font-family: var(--font-heading);
  font-size: 1.75em;
  line-height: 1.2;
  font-weight: 300; }
  @media (max-width: 991.98px) {
    .display-5 {
      font-size: 1.6em; } }
  @media (max-width: 767.98px) {
    .display-5 {
      font-size: 1.5em; } }

.cta-1 {
  font-family: var(--font-heading);
  font-size: 1.5em;
  line-height: 1.4;
  font-weight: 400; }

.cta-2 {
  font-family: var(--font-heading);
  font-size: 1.35em;
  line-height: 1.4;
  font-weight: 400; }

.cta-3 {
  font-family: var(--font-heading);
  font-size: 1.25em;
  line-height: 1.4;
  font-weight: 400; }

.cta-4 {
  font-family: var(--font-heading);
  font-size: 1.15em;
  line-height: 1.25;
  font-weight: 400; }

.lead {
  font-weight: 300; }

h1,
.h1 {
  font-size: 1.8em; }
  @media (max-width: 767.98px) {
    h1,
    .h1 {
      font-size: 1.5em; } }

h2,
.h2 {
  font-size: 1.65em; }
  @media (max-width: 767.98px) {
    h2,
    .h2 {
      font-size: 1.3em; } }

h3,
.h3 {
  font-size: 1.5em; }
  @media (max-width: 767.98px) {
    h3,
    .h3 {
      font-size: 1.25em; } }

h4,
.h4 {
  font-size: 1.35em; }
  @media (max-width: 767.98px) {
    h4,
    .h4 {
      font-size: 1.15em; } }

h5,
.h5 {
  font-size: 1.1em; }
  @media (max-width: 767.98px) {
    h5,
    .h5 {
      font-size: 1em; } }

h6,
.h6 {
  font-size: 1em; }
  @media (max-width: 767.98px) {
    h6,
    .h6 {
      font-size: 1em; } }

.icon-30 {
        font-size: 30px !important; }

.icon-24 {
  font-size: 24px !important; }

.icon-22 {
  font-size: 22px !important; }

.icon-20 {
  font-size: 20px !important; }

.icon-18 {
  font-size: 18px !important; }

.icon-16 {
  font-size: 16px !important; }

.icon-14 {
  font-size: 14px !important; }

pre {
  color: var(--body); }

code {
  color: var(--body); }

mark,
.mark {
  background-color: rgba(var(--secondary-rgb), 0.1); }

.ll-nam {
  color: var(--tertiary); }

.ll-num {
  color: var(--info); }

.ll-str {
  color: var(--secondary); }

.ll-rex {
  color: var(--warning); }

.ll-pct {
  color: var(--body); }

.ll-key {
  color: var(--body);
  font-weight: bold; }

.ll-com {
  color: var(--text-muted);
  font-style: italic; }

kbd {
  background: var(--primary);
  color: var(--light-text);
  border-radius: calc(var(--border-radius-md) / 2); }

.tooltip,
.popover {
  font-family: var(--font); }

.lh-1 {
  line-height: 1 !important; }

.lh-1-25 {
  line-height: 1.25 !important; }

.lh-1-5 {
  line-height: 1.5 !important; }

.disable-text-selection {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }

.font-weight-300 {
  font-weight: 300 !important; }

.line-through {
  text-decoration: line-through; }

.blockquote-footer {
  margin-top: initial; }

/*
*
* Main
*
* Template styles for main content area.
*
*/
main {
  min-height: 100%;
  padding-left: calc(var(--nav-size) + var(--main-spacing-horizontal));
  padding-right: var(--main-spacing-horizontal);
  padding-top: var(--main-spacing-vertical);
  padding-bottom: var(--main-spacing-vertical); }

main > .container-fluid,
main > .container-lg,
main > .container-md,
main > .container-sm,
main > .container-xl {
  padding-right: 0;
  padding-left: 0; }

html[data-placement='horizontal'] main {
  padding-left: var(--main-spacing-horizontal);
  padding-top: calc(var(--nav-size-slim) + calc(var(--main-spacing-horizontal) / 2)); }

html[data-placement='vertical'][data-dimension='mobile'] main {
  padding-left: calc(var(--main-spacing-horizontal) + var(--nav-size-slim)); }

html[data-placement='vertical'][data-behaviour='unpinned'] main {
  padding-left: calc(var(--main-spacing-horizontal) + var(--nav-size-slim)); }

html[data-layout='fluid'] main .container {
  width: 100%;
  max-width: initial;
  padding-right: 0;
  padding-left: 0; }

@media (max-width: 1199.98px) {
  html[data-layout='boxed'] main .container {
    width: 100%;
    max-width: initial;
    padding-right: 0;
    padding-left: 0; } }

html[data-placement='horizontal'][data-dimension='mobile'] main,
html[data-placement='vertical'][data-dimension='mobile'] main {
  padding-left: var(--main-spacing-horizontal) !important;
  padding-top: calc(var(--nav-size-slim) + calc(var(--main-spacing-horizontal) / 2)); }

html[data-fullpage='true'] body,
html[data-fullpage='true'] #root,
html[data-fullpage='true'] main {
  height: 100%; }

html[data-fullpage='true'] main .container {
  height: calc(100%); }

html[data-footer='true'][data-fullpage='true'] body,
html[data-footer='true'][data-fullpage='true'] #root,
html[data-footer='true'][data-fullpage='true'] main {
  height: 100%; }

html[data-footer='true'][data-fullpage='true'] main .container {
  height: 100%; }

html[data-placement='horizontal'] section.scroll-section {
  scroll-margin-top: calc(var(--nav-size-slim) + calc(var(--main-spacing-horizontal) / 2)); }

html[data-placement='vertical'] section.scroll-section {
  scroll-margin-top: var(--main-spacing-vertical); }

/*
*
* Nav Primary
*
* Template styles for both vertical and horizontal navigation as well as mobile menu.
*
*/
html[data-placement='horizontal'] .nav-container,
html[data-placement='vertical'] .nav-container {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 1001;
  background-repeat: no-repeat;
  background-size: cover;
  width: var(--nav-size);
  background-position: center;
  background-image: linear-gradient(160deg, var(--gradient-1), var(--gradient-1), var(--gradient-2), var(--gradient-3)); }
  html[data-placement='horizontal'] .nav-container .nav-shadow,
  html[data-placement='vertical'] .nav-container .nav-shadow {
    width: 100%;
    height: 100%;
    position: absolute;
    box-shadow: var(--menu-shadow);
    pointer-events: none;
    z-index: 1001; }
  html[data-placement='horizontal'] .nav-container .mobile-buttons-container,
  html[data-placement='vertical'] .nav-container .mobile-buttons-container {
    display: none; }
  html[data-placement='horizontal'] .nav-container .menu-container .menu,
  html[data-placement='vertical'] .nav-container .menu-container .menu {
    display: none;
    margin: 0 auto;
    padding: 0;
    list-style: none;
    /* All li items - all of the items */
    /* Only top level li items - main menu items */
    /* Only sub level li items - sub menu items */ }
    html[data-placement='horizontal'] .nav-container .menu-container .menu ul,
    html[data-placement='vertical'] .nav-container .menu-container .menu ul {
      list-style: none; }
    html[data-placement='horizontal'] .nav-container .menu-container .menu li a,
    html[data-placement='vertical'] .nav-container .menu-container .menu li a {
      text-decoration: initial;
      position: relative;
      width: 100%;
      transition: opacity var(--transition-time);
      display: inline-block; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu li a:after,
      html[data-placement='vertical'] .nav-container .menu-container .menu li a:after {
        content: none; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu li a .icon,
      html[data-placement='horizontal'] .nav-container .menu-container .menu li a .label,
      html[data-placement='vertical'] .nav-container .menu-container .menu li a .icon,
      html[data-placement='vertical'] .nav-container .menu-container .menu li a .label {
        vertical-align: middle; }
    html[data-placement='horizontal'] .nav-container .menu-container .menu > li,
    html[data-placement='vertical'] .nav-container .menu-container .menu > li {
      margin: 0;
      position: relative; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li a,
      html[data-placement='vertical'] .nav-container .menu-container .menu > li a {
        color: var(--light-text); }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li .icon,
      html[data-placement='vertical'] .nav-container .menu-container .menu > li .icon {
        margin-right: 8px;
        display: inline-block;
        margin-top: -1px;
        margin-left: -1px; }
    html[data-placement='horizontal'] .nav-container .menu-container .menu > li li,
    html[data-placement='vertical'] .nav-container .menu-container .menu > li li {
      padding: 0; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li li a,
      html[data-placement='vertical'] .nav-container .menu-container .menu > li li a {
        padding: 0.5rem 1rem; }
        html[data-placement='horizontal'] .nav-container .menu-container .menu > li li a .label,
        html[data-placement='vertical'] .nav-container .menu-container .menu > li li a .label {
          margin-right: 1rem; }
    html[data-placement='horizontal'] .nav-container .menu-container .menu.show,
    html[data-placement='vertical'] .nav-container .menu-container .menu.show {
      display: flex; }
  html[data-placement='horizontal'] .nav-container .menu-container.os-host,
  html[data-placement='vertical'] .nav-container .menu-container.os-host {
    padding-left: 0;
    padding-right: 0; }
  html[data-placement='horizontal'] .nav-container .menu-icons,
  html[data-placement='vertical'] .nav-container .menu-icons {
    display: flex !important;
    justify-content: center;
    cursor: pointer; }
    html[data-placement='horizontal'] .nav-container .menu-icons .list-inline-item,
    html[data-placement='vertical'] .nav-container .menu-icons .list-inline-item {
      margin-left: 0;
      margin-right: 0; }
    html[data-placement='horizontal'] .nav-container .menu-icons .dropdown-menu,
    html[data-placement='vertical'] .nav-container .menu-icons .dropdown-menu {
      margin-top: 10px; }
    html[data-placement='horizontal'] .nav-container .menu-icons i,
    html[data-placement='vertical'] .nav-container .menu-icons i {
      font-size: 18px !important; }
    html[data-placement='horizontal'] .nav-container .menu-icons > li,
    html[data-placement='vertical'] .nav-container .menu-icons > li {
      height: 38px; }
    html[data-placement='horizontal'] .nav-container .menu-icons > li > a,
    html[data-placement='vertical'] .nav-container .menu-icons > li > a {
      color: var(--light-text);
      font-size: 18px;
      padding: 0.25rem 0.5rem;
      transition: opacity var(--transition-time); }
      html[data-placement='horizontal'] .nav-container .menu-icons > li > a:hover,
      html[data-placement='vertical'] .nav-container .menu-icons > li > a:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
        background: rgba(255, 255, 255, 0.05); }
    html[data-placement='horizontal'] .nav-container .menu-icons .dropdown-menu,
    html[data-placement='vertical'] .nav-container .menu-icons .dropdown-menu {
      margin-top: 5px !important; }
    html[data-placement='horizontal'] .nav-container .menu-icons .notification-dot,
    html[data-placement='vertical'] .nav-container .menu-icons .notification-dot {
      width: 3px;
      height: 3px;
      background: var(--light-text);
      top: -2px;
      right: 0; }
  html[data-placement='horizontal'] .nav-container .user-container,
  html[data-placement='vertical'] .nav-container .user-container {
    flex-direction: column; }
    html[data-placement='horizontal'] .nav-container .user-container .dropdown-menu,
    html[data-placement='vertical'] .nav-container .user-container .dropdown-menu {
      margin-top: 5px !important; }
    html[data-placement='horizontal'] .nav-container .user-container .user,
    html[data-placement='vertical'] .nav-container .user-container .user {
      text-align: center;
      transition: opacity var(--transition-time);
      border-radius: var(--border-radius-md); }
      html[data-placement='horizontal'] .nav-container .user-container .user::after,
      html[data-placement='vertical'] .nav-container .user-container .user::after {
        content: initial; }
      html[data-placement='horizontal'] .nav-container .user-container .user:hover,
      html[data-placement='vertical'] .nav-container .user-container .user:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
        background: rgba(255, 255, 255, 0.05); }
      html[data-placement='horizontal'] .nav-container .user-container .user .profile,
      html[data-placement='vertical'] .nav-container .user-container .user .profile {
        margin: 0 auto;
        margin-bottom: 5px;
        width: 60px;
        height: 60px;
        border-radius: var(--border-radius-xl); }
      html[data-placement='horizontal'] .nav-container .user-container .user .name,
      html[data-placement='vertical'] .nav-container .user-container .user .name {
        color: var(--light-text);
        line-height: 1; }
    html[data-placement='horizontal'] .nav-container .user-container .user-menu i,
    html[data-placement='vertical'] .nav-container .user-container .user-menu i {
      margin-right: 3px;
      line-height: 1.2; }
  html[data-placement='horizontal'] .nav-container .language-switch-container,
  html[data-placement='vertical'] .nav-container .language-switch-container {
    order: 2;
    display: flex !important; }
    html[data-placement='horizontal'] .nav-container .language-switch-container .dropdown-menu,
    html[data-placement='vertical'] .nav-container .language-switch-container .dropdown-menu {
      min-width: 65px;
      margin-top: 5px !important; }
    html[data-placement='horizontal'] .nav-container .language-switch-container .language-button,
    html[data-placement='vertical'] .nav-container .language-switch-container .language-button {
      padding: 0 8px;
      padding: 5px 14px;
      border-radius: var(--border-radius-md); }
      html[data-placement='horizontal'] .nav-container .language-switch-container .language-button:hover,
      html[data-placement='vertical'] .nav-container .language-switch-container .language-button:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
        background: rgba(255, 255, 255, 0.05); }
  html[data-placement='horizontal'] .nav-container .logo img,
  html[data-placement='horizontal'] .nav-container .logo .img,
  html[data-placement='vertical'] .nav-container .logo img,
  html[data-placement='vertical'] .nav-container .logo .img {
    width: 180px;
    min-height: 38px;
    object-position: left;
    object-fit: cover;
    transition: width var(--transition-time), height var(--transition-time); }
  html[data-placement='horizontal'] .nav-container .logo .img,
  html[data-placement='vertical'] .nav-container .logo .img {
    background-repeat: no-repeat; }
  html[data-placement='horizontal'] .nav-container .dropdown-menu,
  html[data-placement='vertical'] .nav-container .dropdown-menu {
    border: initial;
    margin-top: initial;
    border-radius: var(--border-radius-md);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1) !important; }
    html[data-placement='horizontal'] .nav-container .dropdown-menu a,
    html[data-placement='vertical'] .nav-container .dropdown-menu a {
      color: var(--alternate); }
    html[data-placement='horizontal'] .nav-container .dropdown-menu.wide,
    html[data-placement='vertical'] .nav-container .dropdown-menu.wide {
      width: calc(var(--nav-size) - calc(var(--main-spacing-vertical)));
      padding: 20px 30px 20px 30px; }

/*
* Horizontal
* Styles for horizontal menu
*/
html[data-placement='horizontal'] .nav-container {
  height: var(--nav-size-slim);
  right: 0;
  left: 0;
  width: 100%;
  justify-content: center;
  flex-direction: row;
  padding-left: var(--main-spacing-horizontal);
  padding-right: var(--main-spacing-horizontal);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--border-radius-lg); }
  html[data-placement='horizontal'] .nav-container .nav-content {
    flex-direction: row;
    align-items: center;
    width: 100%; }
  html[data-placement='horizontal'] .nav-container .nav-shadow {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg); }
  html[data-placement='horizontal'] .nav-container .menu-container {
    margin: initial;
    height: 100%; }
    html[data-placement='horizontal'] .nav-container .menu-container .menu {
      margin-left: 2rem;
      margin-right: 2rem;
      height: 100%;
      /* All li items - all of the items main and sub */
      /* Only top level li items - main menu items */
      /* Only sub level li items - sub menu items */
      /* All ul items except first one */
      /* Only second level of ul items */ }
      @media (max-width: 1399.98px) {
        html[data-placement='horizontal'] .nav-container .menu-container .menu {
          margin-left: 2rem;
          margin-right: 2rem; } }
      @media (max-width: 1199.98px) {
        html[data-placement='horizontal'] .nav-container .menu-container .menu {
          margin-left: 0.75rem;
          margin-right: 0.75rem; } }
      html[data-placement='horizontal'] .nav-container .menu-container .menu li a {
        white-space: nowrap; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a {
        padding: 0 1.5rem;
        height: 100%;
        display: inline-flex;
        align-items: center; }
        @media (max-width: 1399.98px) {
          html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a {
            padding: 0 1rem; } }
        @media (max-width: 1199.98px) {
          html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a {
            padding: 0 0.75rem; } }
        html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a.active, html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a:hover {
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
          background: rgba(255, 255, 255, 0.05); }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li > a {
        border-bottom-left-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md); }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li .label {
        max-width: 100%; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu ul li ul {
        left: calc(100% + 20px) !important; }
      html[data-placement='horizontal'] .nav-container .menu-container .menu ul {
        left: calc(100% + 1rem);
        background: var(--foreground);
        position: absolute;
        top: 0;
        border-top-left-radius: var(--border-radius-md);
        border-top-right-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md);
        border-bottom-left-radius: var(--border-radius-md);
        border: initial;
        padding: 1rem;
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1) !important; }
        html[data-placement='horizontal'] .nav-container .menu-container .menu ul a {
          color: var(--body);
          border-radius: var(--border-radius-sm);
          transition: color var(--transition-time); }
          html[data-placement='horizontal'] .nav-container .menu-container .menu ul a.active .label::after {
            background: var(--primary); }
          html[data-placement='horizontal'] .nav-container .menu-container .menu ul a.active {
            background: rgba(var(--separator-light-rgb), 0.5);
            color: var(--primary); }
          html[data-placement='horizontal'] .nav-container .menu-container .menu ul a:hover {
            color: var(--primary);
            opacity: 1;
            background: rgba(var(--separator-light-rgb), 0.5); }
      html[data-placement='horizontal'] .nav-container .menu-container .menu > li > ul {
        border-bottom-right-radius: var(--border-radius-md);
        border-bottom-left-radius: var(--border-radius-md);
        border-top-left-radius: var(--border-radius-md);
        border-top-right-radius: var(--border-radius-md); }
  html[data-placement='horizontal'] .nav-container .logo {
    margin-top: 0;
    margin-bottom: 0;
    text-align: initial; }
  html[data-placement='horizontal'] .nav-container .user-container {
    flex-direction: row;
    order: 3;
    align-items: center;
    height: 100%; }
    html[data-placement='horizontal'] .nav-container .user-container .user {
      align-items: center;
      height: 100%;
      padding: 0 8px;
      margin-right: -8px;
      border-radius: initial;
      border-bottom-left-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md); }
      html[data-placement='horizontal'] .nav-container .user-container .user .profile {
        order: 1;
        width: var(--input-height);
        height: var(--input-height);
        margin-bottom: 0; }
      html[data-placement='horizontal'] .nav-container .user-container .user .name {
        margin-right: 10px;
        margin-bottom: 0; }
  html[data-placement='horizontal'] .nav-container .language-switch-container {
    order: 2;
    height: 100%;
    margin-right: 10px; }
    @media (max-width: 1199.98px) {
      html[data-placement='horizontal'] .nav-container .language-switch-container {
        margin-right: 5px; } }
    html[data-placement='horizontal'] .nav-container .language-switch-container .dropdown-menu {
      min-width: 65px; }
    html[data-placement='horizontal'] .nav-container .language-switch-container .language-button {
      height: 100%;
      padding: 0 8px;
      border-radius: initial;
      border-bottom-left-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md);
      padding-top: 2px; }
      html[data-placement='horizontal'] .nav-container .language-switch-container .language-button:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
        background: rgba(255, 255, 255, 0.05); }
  html[data-placement='horizontal'] .nav-container .menu-icons {
    order: 1;
    margin: 0;
    margin-right: 10px;
    margin-left: auto;
    height: 100%; }
    html[data-placement='horizontal'] .nav-container .menu-icons li {
      height: 100%; }
      html[data-placement='horizontal'] .nav-container .menu-icons li > a {
        height: 100%;
        display: inline-flex;
        align-items: center;
        border-bottom-left-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md); }
    @media (max-width: 1199.98px) {
      html[data-placement='horizontal'] .nav-container .menu-icons {
        margin-right: 5px; } }

/*
* Vertical
* Styles for vertical and mobile menu
*/
html[data-placement='vertical'] .nav-container,
html[data-placement='horizontal'] .nav-container.mobile-side-ready,
html[data-placement='vertical'] .nav-container.mobile-side-ready {
  top: 0;
  width: var(--nav-size);
  height: 100%;
  padding-left: initial;
  padding-right: initial;
  border-top-left-radius: 0;
  border-top-right-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: 0;
  flex-direction: column;
  justify-content: initial; }
  html[data-placement='vertical'] .nav-container .nav-shadow,
  html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-shadow,
  html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-shadow {
    border-top-left-radius: 0;
    border-top-right-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: 0; }
  html[data-placement='vertical'] .nav-container .nav-content,
  html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content,
  html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content {
    flex-direction: column;
    height: 100%;
    padding-top: var(--main-spacing-vertical);
    padding-bottom: var(--main-spacing-vertical);
    align-items: center;
    padding-right: initial !important; }
    html[data-placement='vertical'] .nav-container .nav-content .mobile-buttons-container,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .mobile-buttons-container,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .mobile-buttons-container {
      display: none; }
    html[data-placement='vertical'] .nav-container .nav-content .menu-icons,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-icons,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-icons {
      display: flex !important;
      justify-content: center;
      margin: 0;
      order: 3;
      height: auto; }
      html[data-placement='vertical'] .nav-container .nav-content .menu-icons .dropdown-menu,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-icons .dropdown-menu,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-icons .dropdown-menu {
        margin-top: 10px; }
      html[data-placement='vertical'] .nav-container .nav-content .menu-icons li > a,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-icons li > a,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-icons li > a {
        padding: 0.25rem 0.5rem;
        border-radius: var(--border-radius-md);
        height: 100%;
        display: inline-flex;
        align-items: center; }
    html[data-placement='vertical'] .nav-container .nav-content .logo,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .logo,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .logo {
      margin-bottom: 20px;
      text-align: center; }
      html[data-placement='vertical'] .nav-container .nav-content .logo a,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .logo a,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .logo a {
        overflow: hidden;
        display: inline-block;
        width: 100px; }
    html[data-placement='vertical'] .nav-container .nav-content .language-switch-container,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .language-switch-container,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .language-switch-container {
      height: auto;
      margin-right: initial;
      display: flex !important; }
      @media (max-width: 1199.98px) {
        html[data-placement='vertical'] .nav-container .nav-content .language-switch-container,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .language-switch-container,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .language-switch-container {
          margin-right: initial; } }
      html[data-placement='vertical'] .nav-container .nav-content .language-switch-container .dropdown-menu,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .language-switch-container .dropdown-menu,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .language-switch-container .dropdown-menu {
        margin-top: 10px; }
      html[data-placement='vertical'] .nav-container .nav-content .language-switch-container .language-button,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .language-switch-container .language-button,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .language-switch-container .language-button {
        height: auto;
        padding: 5px 14px;
        border-radius: var(--border-radius-md); }
    html[data-placement='vertical'] .nav-container .nav-content .user-container,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .user-container,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .user-container {
      order: 1;
      display: flex !important;
      flex-direction: column;
      height: auto;
      min-height: 100px; }
      html[data-placement='vertical'] .nav-container .nav-content .user-container .user,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .user-container .user,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .user-container .user {
        flex-direction: column;
        margin-bottom: 5px;
        height: auto;
        padding: 10px;
        border-radius: var(--border-radius-md);
        margin-right: 0; }
        html[data-placement='vertical'] .nav-container .nav-content .user-container .user .name,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .user-container .user .name,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .user-container .user .name {
          margin-right: 0; }
        html[data-placement='vertical'] .nav-container .nav-content .user-container .user .profile,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .user-container .user .profile,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .user-container .user .profile {
          order: initial;
          width: 50px;
          height: 50px;
          margin-bottom: 5px; }
      html[data-placement='vertical'] .nav-container .nav-content .user-container .dropdown-menu,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .user-container .dropdown-menu,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .user-container .dropdown-menu {
        margin-top: 10px; }
    html[data-placement='vertical'] .nav-container .nav-content .menu-container,
    html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container,
    html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container {
      display: flex !important;
      align-self: flex-start;
      order: 3;
      margin-top: 2rem;
      margin-bottom: 1rem;
      width: 16rem;
      margin-left: 1rem;
      margin-right: 1rem; }
      html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu,
      html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu,
      html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        padding-left: 1rem;
        padding-right: 1rem;
        cursor: pointer;
        /* All li items - all of the items main and sub */
        /* Only top level li items - main menu items */
        /* Only sub level li items - sub menu items */ }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu ul,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu ul,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu ul {
          box-shadow: initial !important;
          background: initial;
          position: initial;
          border-radius: initial;
          padding: initial;
          padding-left: 2rem; }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu li a,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a {
          border-radius: var(--border-radius-lg);
          width: 100%;
          color: var(--light-text);
          transition: color var(--transition-time);
          margin-bottom: 0.1rem;
          margin-top: 0.1rem; }
          html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu li a .icon,
          html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a .icon,
          html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a .icon {
            color: var(--light-text); }
          html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu li a.active,
          html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a.active,
          html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a.active {
            background: rgba(var(--light-text-rgb), 0.1);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important; }
          html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu li a:hover,
          html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a:hover,
          html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu li a:hover {
            background: rgba(var(--light-text-rgb), 0.1);
            color: var(--light-text); }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu > li,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li {
          margin: 0;
          padding: 0; }
          html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu > li a,
          html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li a,
          html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li a {
            padding: 0.75rem 1rem; }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu > li li a,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li li a,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li li a {
          padding: 0.5rem 1rem; }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu > li li ul,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li li ul,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu > li li ul {
          padding-left: 1rem; }
        html[data-placement='vertical'] .nav-container .nav-content .menu-container .menu.show,
        html[data-placement='horizontal'] .nav-container.mobile-side-ready .nav-content .menu-container .menu.show,
        html[data-placement='vertical'] .nav-container.mobile-side-ready .nav-content .menu-container .menu.show {
          display: inline-block; }

@media (max-width: 1199.98px) {
  html[data-placement='horizontal']:not([data-mobile='true']) .menu-icons .user .name,
  html[data-placement='horizontal']:not([data-mobile='true']) .user-container .user .name {
    display: none; } }

html[data-placement='horizontal']:not([data-mobile='true']) .menu-icons .dropdown-menu,
html[data-placement='horizontal']:not([data-mobile='true']) .user-container .dropdown-menu,
html[data-placement='horizontal']:not([data-mobile='true']) .language-switch-container .dropdown-menu {
  margin-top: 0.5rem; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container .mobile-buttons-container,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .mobile-buttons-container {
  margin-left: auto;
  height: 100%;
  align-items: center;
  display: flex; }
  html[data-placement='vertical'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a,
  html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a {
    color: var(--light-text);
    padding: 0.25rem 0.5rem;
    height: 100%;
    align-items: center;
    display: flex;
    border-bottom-left-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md); }
    html[data-placement='vertical'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a:hover,
    html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a:hover {
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
      background: rgba(255, 255, 255, 0.05); }
    html[data-placement='vertical'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a i,
    html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .mobile-buttons-container > a i {
      font-size: 18px; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container .menu-icons,
html[data-placement='vertical'][data-dimension='mobile'] .nav-container .language-switch-container,
html[data-placement='vertical'][data-dimension='mobile'] .nav-container .user-container,
html[data-placement='vertical'][data-dimension='mobile'] .nav-container .menu-container,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .menu-icons,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .language-switch-container,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .user-container,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container .menu-container {
  display: none !important; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-top-out,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-top-out {
  transition-property: top;
  transition-duration: var(--transition-time-short);
  top: -100px; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-side-ready,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-side-ready {
  transition-property: left;
  transition-duration: initial;
  left: calc(var(--nav-size) * -1); }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-side-in,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-side-in {
  transition-property: left;
  transition-duration: var(--transition-time);
  left: 0; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-side-out,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-side-out {
  transition-property: left;
  transition-duration: var(--transition-time-short);
  left: calc(var(--nav-size) * -1); }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-top-ready,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-top-ready {
  transition-property: none;
  transition-duration: initial;
  top: -100px; }

html[data-placement='vertical'][data-dimension='mobile'] .nav-container.mobile-top-in,
html[data-placement='horizontal'][data-dimension='mobile'] .nav-container.mobile-top-in {
  transition-property: top;
  transition-duration: var(--transition-time);
  top: 0; }

html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container {
  width: var(--nav-size-slim); }
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .menu-container {
    align-self: center;
    overflow-x: hidden;
    width: 80px;
    margin-left: 0;
    margin-right: 0; }
    html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .menu-container .menu a {
      white-space: nowrap; }
      html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .menu-container .menu a:before,
      html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .menu-container .menu a .label {
        transition: initial;
        opacity: 0; }
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .logo a {
    width: 30px; }
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .menu-icons,
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .language-switch-container {
    visibility: hidden;
    opacity: 0; }
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .user .profile {
    width: 30px;
    height: 30px; }
  html[data-placement='vertical'][data-behaviour='unpinned']:not([data-menu-animate='show']) .nav-container .nav-content .user .name {
    visibility: hidden;
    opacity: 0; }

html[data-placement='vertical'][data-behaviour='unpinned'] .nav-container .nav-content .menu-container .menu ul {
  transition-duration: initial; }

html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .menu-container .menu ul,
html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu ul {
  transition-duration: var(--transition-time); }

html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .menu-container .menu a:before,
html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .menu-container .menu a .label,
html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu a:before,
html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu a .label {
  transition: opacity var(--transition-time); }

html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container {
  transition-duration: var(--transition-time);
  transition-property: width; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .menu-container {
    transition-duration: var(--transition-time);
    transition-property: opacity, width; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .logo a {
    transition-duration: var(--transition-time);
    transition-property: width; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='hidden'] .nav-container .nav-content .user .profile {
    transition-duration: var(--transition-time);
    transition-property: width, height; }

html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container {
  transition-duration: var(--transition-time);
  transition-property: width; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container {
    width: 16rem;
    margin-left: 1rem;
    margin-right: 1rem;
    overflow-x: hidden;
    transition-duration: var(--transition-time);
    transition-property: opacity, width;
    align-self: center; }
    html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu a {
      white-space: nowrap;
      transition-delay: 0.1s; }
      html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu a:before,
      html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-container .menu a .label {
        transition-delay: 0.1s; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .menu-icons,
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .language-switch-container {
    visibility: visible;
    opacity: 1;
    transition-duration: var(--transition-time-short);
    transition-property: opacity;
    transition-delay: var(--transition-time-short); }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .user .profile {
    transition-duration: var(--transition-time);
    transition-property: width, height; }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .user .name {
    visibility: visible;
    opacity: 1;
    transition-duration: var(--transition-time-short);
    transition-property: opacity;
    transition-delay: var(--transition-time-short); }
  html[data-placement='vertical'][data-behaviour='unpinned'][data-menu-animate='show'] .nav-container .nav-content .logo a {
    transition-duration: var(--transition-time);
    transition-property: width; }

.opacityIn {
  animation-duration: var(--transition-time); }

@keyframes opacityIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; }
  0% {
    opacity: 0; } }

.opacityIn {
  animation-name: opacityIn; }

html[data-placement='vertical'] .nav-container .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
html[data-placement='horizontal'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle,
html[data-placement='vertical'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--light-text-rgb), 0.3); }

html[data-placement='vertical'] .nav-container .menu-container.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
html[data-placement='horizontal'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle,
html[data-placement='vertical'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--light-text-rgb), 0.4); }

html[data-placement='vertical'] .nav-container .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
html[data-placement='horizontal'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active,
html[data-placement='vertical'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
  background: rgba(var(--light-text-rgb), 0.4); }

html[data-placement='vertical'] .nav-container .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
html[data-placement='vertical'] .nav-container .menu-container .os-theme-light > .os-scrollbar > .os-scrollbar-track,
html[data-placement='horizontal'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
html[data-placement='horizontal'] .nav-container.mobile-side-ready .menu-container .os-theme-light > .os-scrollbar > .os-scrollbar-track,
html[data-placement='vertical'] .nav-container.mobile-side-ready .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
html[data-placement='vertical'] .nav-container.mobile-side-ready .menu-container .os-theme-light > .os-scrollbar > .os-scrollbar-track {
  background: rgba(var(--light-text-rgb), 0.1); }

/*
Arrows collapse for vertical menu
*/
.menu a[data-bs-toggle='collapse']:before {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  border-top: 1px solid var(--light-text);
  border-right: 1px solid var(--light-text);
  transform: rotate(45deg);
  bottom: initial;
  top: 14px;
  left: initial;
  right: 1rem;
  top: calc(50% - 3px); }

.menu a[data-bs-toggle='collapse'][aria-expanded='true']:before {
  transform: rotate(135deg);
  top: 13px;
  top: calc(50% - 4px); }

/*
Arrows collapse for horizontal menu
*/
.menu a.dropdown-toggle:before {
  content: '';
  width: 5px;
  height: 5px;
  border-top: 1px solid var(--light-text);
  border-right: 1px solid var(--light-text);
  transform: rotate(45deg);
  order: 3;
  margin-left: 5px;
  margin-top: 3px; }

.menu li.dropdown.show > a.dropdown-toggle:before {
  content: '';
  transform: rotate(135deg);
  margin-top: 1px; }

.menu > li > a.show:before {
  content: '';
  transform: rotate(135deg);
  margin-top: 1px; }

.menu > li li a.dropdown-toggle:before {
  position: absolute;
  top: calc(50% - 2px);
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate);
  margin-top: initial;
  right: 12px; }

.menu > li li.dropdown.show > a.dropdown-toggle:before {
  content: '';
  top: calc(50% - 3px);
  margin-top: initial;
  right: 11px; }

#colorButton .light {
  display: none; }

#pinButton.disabled {
  cursor: default; }
  #pinButton.disabled i {
    opacity: 0.5; }

.notification-dropdown .scroll {
  height: 185px; }

html[data-color*='light'] #colorButton .light {
  display: inline-block; }

html[data-color*='light'] #colorButton .dark {
  display: none; }

html[data-color*='dark'] #colorButton .light {
  display: none; }

html[data-color*='dark'] #colorButton .dark {
  display: inline-block; }

html:not([data-scrollspy='true']) #scrollSpyButton,
html:not([data-scrollspy='true']) #scrollSpyDropdown {
  display: none !important; }

html[data-behaviour='pinned'] .pin-button .pin {
  display: none; }

html[data-behaviour='pinned'] .pin-button .unpin {
  display: inline-block; }

html[data-behaviour='unpinned'] .pin-button .pin {
  display: inline-block; }

html[data-behaviour='unpinned'] .pin-button .unpin {
  display: none; }

html[data-placement='horizontal'][data-behaviour='unpinned']:not([data-mobile='true']) .nav-container {
  transition-property: top;
  transition-duration: var(--transition-time-short); }

html[data-placement='horizontal'][data-behaviour='unpinned'][data-menu-animate='hidden']:not([data-mobile='true']) .nav-container {
  top: -100px; }

html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul.show {
  display: flex;
  flex-wrap: wrap;
  padding: 1rem 0;
  position: fixed !important;
  transform: initial !important;
  width: calc(100% - calc(var(--main-spacing-horizontal) * 2));
  margin-left: var(--main-spacing-horizontal) !important;
  margin-right: var(--main-spacing-horizontal) !important;
  top: var(--nav-size-slim) !important;
  margin-top: 0.5rem !important; }
  html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul.show > li > a {
    color: var(--primary) !important;
    background: initial !important; }
  html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul.show a {
    padding: 0.35rem 1rem !important; }
    html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul.show a.dropdown-toggle {
      background: initial !important;
      pointer-events: none;
      cursor: default; }
      html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul.show a.dropdown-toggle:before {
        border: initial !important; }

html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul ul {
  position: static !important;
  box-shadow: initial !important; }

html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul > li {
  padding-left: 0.75rem;
  padding-right: 0.75rem; }

html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul > li > ul {
  padding: initial; }

html[data-placement='horizontal']:not([data-mobile='true']) .nav-container .menu .mega > ul > li ul {
  padding-top: initial;
  padding-bottom: initial; }

html[data-placement='horizontal'][data-mobile='true'] .nav-container .menu > li > ul {
  margin-top: 0; }

html[data-placement='horizontal'][data-mobile='true'] .nav-container .menu > li > a {
  display: inline-block; }

html[data-navcolor='light'] .nav-shadow {
  box-shadow: var(--menu-shadow-navcolor); }

html[data-navcolor='light'] .nav-container {
  background: var(--background-navcolor-light); }

html[data-navcolor='light'] .nav-container .menu-container .menu a {
  color: var(--alternate) !important; }
  html[data-navcolor='light'] .nav-container .menu-container .menu a.active, html[data-navcolor='light'] .nav-container .menu-container .menu a:hover {
    background: rgba(0, 0, 0, 0.05) !important; }
  html[data-navcolor='light'] .nav-container .menu-container .menu a .icon {
    color: var(--alternate) !important; }

html[data-navcolor='light'] .nav-container .menu-icons > li > a {
  color: var(--alternate); }
  html[data-navcolor='light'] .nav-container .menu-icons > li > a:hover {
    background: rgba(0, 0, 0, 0.05) !important; }

html[data-navcolor='light'] .nav-container .language-switch-container .language-button {
  color: var(--alternate) !important; }
  html[data-navcolor='light'] .nav-container .language-switch-container .language-button:hover {
    background: rgba(0, 0, 0, 0.05) !important; }

html[data-navcolor='light'] .nav-container .mobile-buttons-container > a {
  color: var(--primary) !important; }
  html[data-navcolor='light'] .nav-container .mobile-buttons-container > a:hover {
    background: rgba(0, 0, 0, 0.05) !important; }

html[data-navcolor='light'] .nav-container .menu-container .menu > li > a .icon {
  color: var(--primary); }

html[data-navcolor='light'] .nav-container .user-container .user .name {
  color: var(--alternate); }

html[data-navcolor='light'] .nav-container .user-container .user:hover {
  background: rgba(0, 0, 0, 0.05) !important; }

html[data-navcolor='light'] .nav-container .nav-content .menu-container .menu ul a {
  color: var(--alternate); }

html[data-navcolor='light'] .nav-container .menu-container .menu a.active .label::after {
  background: var(--primary); }

html[data-navcolor='light'] .menu a[data-bs-toggle='collapse']:before {
  border-color: var(--alternate); }

html[data-navcolor='light'] .menu a.dropdown-toggle:before {
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate); }

html[data-navcolor='light'] .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--alternate-rgb), 0.3) !important; }

html[data-navcolor='light'] .menu-container.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
  background: rgba(var(--alternate-rgb), 0.4) !important; }

html[data-navcolor='light'] .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
  background: rgba(var(--alternate-rgb), 0.4) !important; }

html[data-navcolor='light'] .menu-container.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
html[data-navcolor='light'] .menu-container .os-theme-light > .os-scrollbar > .os-scrollbar-track {
  background: rgba(var(--alternate-rgb), 0.1) !important; }

html[data-navcolor='dark'] .nav-shadow {
  box-shadow: var(--menu-shadow-navcolor); }

html[data-navcolor='dark'] .nav-container {
  background: var(--background-navcolor-dark); }

html[data-navcolor='dark'] .nav-container .menu-container .menu > li > a {
  color: var(--light-text); }

html[data-navcolor='dark'] .nav-container .language-switch-container .language-button {
  color: var(--light-text) !important; }
  html[data-navcolor='dark'] .nav-container .language-switch-container .language-button:hover {
    background: rgba(255, 255, 255, 0.05) !important; }

html[data-navcolor='dark'] .nav-container .mobile-buttons-container > a {
  color: var(--light-text) !important; }
  html[data-navcolor='dark'] .nav-container .mobile-buttons-container > a:hover {
    background: rgba(255, 255, 255, 0.05) !important; }

html[data-navcolor='dark'] .nav-container .menu-icons > li > a {
  color: var(--light-text); }

html[data-navcolor='dark'] .nav-container .menu-container .menu > li > a .icon {
  color: var(--light-text); }

html[data-navcolor='dark'] .nav-container .user-container .user .name {
  color: var(--light-text); }

html[data-navcolor='dark'] .nav-container .menu-container .menu a.active .label::after {
  background: var(--light-text); }

html[data-navcolor='dark'] .nav-container .menu-container .menu ul {
  background: var(--background-navcolor-dark) !important; }

html[data-navcolor='dark'] .nav-container .menu-container .menu a {
  color: var(--light-text) !important; }
  html[data-navcolor='dark'] .nav-container .menu-container .menu a.active, html[data-navcolor='dark'] .nav-container .menu-container .menu a:hover {
    background: rgba(255, 255, 255, 0.05) !important; }

.collapsing {
  transition-duration: var(--transition-time); }

.logo-default {
  width: 100px;
  min-height: 35px;
  object-position: left;
  object-fit: cover;
  background-repeat: no-repeat; }

html[data-color='light-blue'] .logo .img,
html[data-color='light-green'] .logo .img,
html[data-color='light-red'] .logo .img,
html[data-color='light-pink'] .logo .img,
html[data-color='light-purple'] .logo .img,
html[data-color='dark-blue'] .logo .img,
html[data-color='dark-green'] .logo .img,
html[data-color='dark-red'] .logo .img,
html[data-color='dark-pink'] .logo .img,
html[data-color='dark-purple'] .logo .img {
  background-image: url(../img/logo/logo-light.svg); }

html[data-color='light-blue'] .logo-default {
  background-image: url(../img/logo/logo-blue-light.svg); }

html[data-color='light-green'] .logo-default {
  background-image: url(../img/logo/logo-green-light.svg); }

html[data-color='light-red'] .logo-default {
  background-image: url(../img/logo/logo-red-light.svg); }

html[data-color='light-pink'] .logo-default {
  background-image: url(../img/logo/logo-pink-light.svg); }

html[data-color='light-purple'] .logo-default {
  background-image: url(../img/logo/logo-purple-light.svg); }

html[data-color='dark-blue'] .logo-default {
  background-image: url(../img/logo/logo-blue-dark.svg); }

html[data-color='dark-green'] .logo-default {
  background-image: url(../img/logo/logo-green-dark.svg); }

html[data-color='dark-red'] .logo-default {
  background-image: url(../img/logo/logo-red-dark.svg); }

html[data-color='dark-pink'] .logo-default {
  background-image: url(../img/logo/logo-pink-dark.svg); }

html[data-color='dark-purple'] .logo-default {
  background-image: url(../img/logo/logo-purple-dark.svg); }

html[data-color='light-blue'][data-navcolor='light'] .logo .img,
html[data-color='dark-blue'][data-navcolor='light'] .logo .img {
  background-image: url(../img/logo/logo-blue-light.svg); }

html[data-color='light-green'][data-navcolor='light'] .logo .img,
html[data-color='dark-green'][data-navcolor='light'] .logo .img {
  background-image: url(../img/logo/logo-green-light.svg); }

html[data-color='light-red'][data-navcolor='light'] .logo .img,
html[data-color='dark-red'][data-navcolor='light'] .logo .img {
  background-image: url(../img/logo/logo-red-light.svg); }

html[data-color='light-pink'][data-navcolor='light'] .logo .img,
html[data-color='dark-pink'][data-navcolor='light'] .logo .img {
  background-image: url(../img/logo/logo-pink-light.svg); }

html[data-color='light-purple'][data-navcolor='light'] .logo .img,
html[data-color='dark-purple'][data-navcolor='light'] .logo .img {
  background-image: url(../img/logo/logo-purple-light.svg); }

html[data-color='light-blue'][data-navcolor='dark'] .logo .img,
html[data-color='dark-blue'][data-navcolor='dark'] .logo .img {
  background-image: url(../img/logo/logo-blue-dark.svg); }

html[data-color='light-green'][data-navcolor='dark'] .logo .img,
html[data-color='dark-green'][data-navcolor='dark'] .logo .img {
  background-image: url(../img/logo/logo-green-dark.svg); }

html[data-color='light-red'][data-navcolor='dark'] .logo .img,
html[data-color='dark-red'][data-navcolor='dark'] .logo .img {
  background-image: url(../img/logo/logo-red-dark.svg); }

html[data-color='light-pink'][data-navcolor='dark'] .logo .img,
html[data-color='dark-pink'][data-navcolor='dark'] .logo .img {
  background-image: url(../img/logo/logo-pink-dark.svg); }

html[data-color='light-purple'][data-navcolor='dark'] .logo .img,
html[data-color='dark-purple'][data-navcolor='dark'] .logo .img {
  background-image: url(../img/logo/logo-purple-dark.svg); }

.menu-container .label,
.user-container .name {
  font-family: var(--font-heading);
  font-size: 13px; }

/*
*
* Nav Side
*
* Template styles for sidebar navigation.
*
*/
html[data-dimension='mobile'] .side-menu-container {
  display: none; }

.side-menu {
  list-style: none;
  padding-left: initial;
  margin-top: -0.5rem; }
  .side-menu ul {
    list-style: none;
    padding-left: initial;
    margin-bottom: 1rem; }
  .side-menu a {
    color: var(--body);
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    display: block; }
    .side-menu a:hover, .side-menu a.active {
      color: var(--primary); }
      .side-menu a:hover .label, .side-menu a.active .label {
        color: var(--primary); }
    .side-menu a .icon {
      width: 17px;
      height: 17px;
      display: inline-block !important; }
  .side-menu a[data-bs-target] {
    color: var(--muted);
    font-size: 0.75rem !important;
    line-height: 1.1rem !important;
    font-weight: initial !important;
    text-transform: uppercase;
    padding-bottom: 0.5rem;
    cursor: initial !important; }
    .side-menu a[data-bs-target] .icon {
      display: none !important; }
  .side-menu .icon {
    width: 20px;
    vertical-align: middle;
    margin-right: 10px;
    color: var(--alternate); }
  .side-menu .label {
    vertical-align: middle;
    color: var(--body); }

.side-menu.primary a {
  color: var(--alternate); }
  .side-menu.primary a:hover, .side-menu.primary a.active {
    color: var(--primary); }
    .side-menu.primary a:hover .icon,
    .side-menu.primary a:hover .label, .side-menu.primary a.active .icon,
    .side-menu.primary a.active .label {
      color: var(--primary); }

.side-menu.primary a[data-bs-target] {
  color: var(--muted); }
  .side-menu.primary a[data-bs-target] .label {
    color: var(--muted); }

/*
*
* Footer
*
* Template styles for footer.
*
*/
html[data-footer='true'] body {
  min-height: 100%;
  position: relative;
  padding-bottom: var(--footer-size); }

html[data-footer='true'] #root > footer {
  border-top: 1px solid var(--separator);
  position: absolute;
  bottom: 0;
  width: 100%;
  height: var(--footer-size);
  display: flex;
  padding-left: calc(var(--nav-size) + var(--main-spacing-horizontal));
  padding-right: var(--main-spacing-horizontal); }
  html[data-footer='true'] #root > footer > .container-fluid,
  html[data-footer='true'] #root > footer > .container-lg,
  html[data-footer='true'] #root > footer > .container-md,
  html[data-footer='true'] #root > footer > .container-sm,
  html[data-footer='true'] #root > footer > .container-xl {
    padding-right: 0;
    padding-left: 0; }
  html[data-footer='true'] #root > footer .footer-content {
    display: flex;
    width: 100%;
    align-items: center; }

html[data-footer='true'][data-placement='horizontal'] #root > footer {
  padding-left: var(--main-spacing-horizontal); }

@media (max-width: 1199.98px) {
  html[data-footer='true'][data-placement='vertical'] #root > footer {
    padding-left: calc(var(--main-spacing-horizontal) + var(--footer-size)); } }

html[data-footer='true'][data-placement='vertical'][data-behaviour='unpinned'] #root > footer {
  padding-left: calc(var(--main-spacing-horizontal) + var(--footer-size)); }

html[data-footer='true'][data-layout='fluid'] #root > footer .container {
  width: 100%;
  max-width: initial;
  padding-right: 0;
  padding-left: 0; }

@media (max-width: 1199.98px) {
  html[data-footer='true'][data-layout='boxed'] #root > footer .container {
    width: 100%;
    max-width: initial;
    padding-right: 0;
    padding-left: 0; } }

html:not([data-footer='true']) #root > footer {
  display: none; }

/*
*
* Print
*
* Print styles.
*
*/
@media print {
  * {
    transition: initial !important; }
  body,
  html,
  main {
    padding: 0 !important;
    margin: 0 !important; }
  #settingsButton,
  #nav,
  footer {
    display: none !important; }
  a {
    text-decoration: initial !important; }
  .card.card-print {
    margin: initial !important;
    box-shadow: initial !important; }
    .card.card-print .card-body {
      padding: 0 !important; }
  main.print-restricted .container {
    padding-right: 0 !important;
    padding-left: 0 !important; }
    main.print-restricted .container > *:not(.print-me) {
      display: none; }
  html[data-placement='horizontal'] main,
  html[data-placement='vertical'] main {
    padding: 0 !important;
    margin: 0 !important; } }

/*
*
* Settings
*
* Settings modal styles.
*
*/
#settings .card {
  height: 56px;
  border: 1px solid var(--separator);
  display: block; }

#settings .radius-rounded .figure {
  border-radius: 5px !important; }

#settings .radius-regular .figure {
  border-radius: 2px !important; }

#settings .radius-flat .figure {
  border-radius: 0 !important; }

#settings .option.active .card, #settings .option:hover .card {
  border: 1px solid var(--primary); }

#settings .option.active .text-part, #settings .option:hover .text-part {
  color: var(--primary) !important; }

#settings .figure {
  border-radius: 5px;
  display: block; }
  #settings .figure.figure-primary {
    background: var(--primary); }
  #settings .figure.figure-muted {
    background: var(--muted); }
  #settings .figure.figure-secondary {
    background: rgba(var(--separator-rgb), 0.6); }
  #settings .figure.figure-dark {
    background: var(--dark); }
  #settings .figure.figure-light {
    background: var(--light); }
  #settings .figure.top {
    width: 100%;
    height: 6px;
    margin-bottom: 5px; }
  #settings .figure.bottom {
    width: 100%;
    height: 12px; }
    #settings .figure.bottom.small {
      width: 50%;
      margin-left: 25%; }
  #settings .figure.left {
    height: 100%;
    width: 6px;
    margin-right: 5px;
    float: left; }
    #settings .figure.left.large {
      width: 14px; }
  #settings .figure.right {
    height: 100%;
    float: right;
    width: 80px; }
    #settings .figure.right.small {
      width: 70px; }
      #settings .figure.right.small.top {
        height: 6px;
        margin-bottom: 5px; }
      #settings .figure.right.small.bottom {
        height: 12px;
        margin-left: initial; }

#settings .color {
  height: 56px; }
  #settings .color div {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-size: contain;
    background-repeat: no-repeat; }

#settings .blue-light {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%232499e3;%7D.cls-2%7Bfill:%2350c6db;%7D.cls-3%7Bfill:%2313467a;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .blue-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%231d72a7;%7D.cls-2%7Bfill:%23319cdf;%7D.cls-3%7Bfill:%23135299;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .red-light {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fb3d5e;%7D.cls-2%7Bfill:%23fa764e;%7D.cls-3%7Bfill:%23f79d3e;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .red-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23bb3f53;%7D.cls-2%7Bfill:%23d46745;%7D.cls-3%7Bfill:%23963444;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .green-light {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%2315a350;%7D.cls-2%7Bfill:%2364c40b;%7D.cls-3%7Bfill:%23147a3f;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .green-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23138b45;%7D.cls-2%7Bfill:%2345a133;%7D.cls-3%7Bfill:%231a5c35;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .pink-light {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23dd428d;%7D.cls-2%7Bfill:%23f782ba;%7D.cls-3%7Bfill:%23be447f;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .pink-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23b14579;%7D.cls-2%7Bfill:%23d87aa7;%7D.cls-3%7Bfill:%238f4468;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .purple-light {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%238650b3;%7D.cls-2%7Bfill:%23b577e9;%7D.cls-3%7Bfill:%23692c9b;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

#settings .purple-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%237844a3;%7D.cls-2%7Bfill:%239e6cc7;%7D.cls-3%7Bfill:%23622296;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E"); }

html[data-radius='flat'] #settings .figure {
  border-radius: 0; }

html[data-radius='standard'] #settings .figure {
  border-radius: 2px; }

.settings-button {
  opacity: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
  padding: 0.5rem 0.9rem 0.5rem 1.1rem;
  height: 46px;
  font-size: 18px;
  position: fixed !important;
  right: 0;
  top: 50%;
  z-index: 1000 !important;
  margin-top: -23px; }
  .settings-button::before {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important; }

html[data-show='true'] .settings-button {
  opacity: 1; }


.fs16 {font-size:16px}
.fs18 {font-size:18px}
.fs20 {font-size:20px}

.fleft{ float: left; }

.text_red{color:#b62836;}

.ptb0{  padding-top:2px !important; padding-bottom:0px !important;  } 

.tableheadcolor {background-color:#b9daee;}	 

.sw100 {width: 100px;}
.sw200 {width: 200px;}

.trhover  {background-color:transparent;}
.trhover:hover  {background-color: #e7f0f3;}

.va_middle  {vertical-align: middle;}

.ta_right  {text-align:right;}
.ta_center  {text-align:center;}

.skills_select2 {
  width: 120px;
}
 