/*
*
* Slider
*
* Noui slider form control styles.
*
*/

.noUi-horizontal {
  height: 8px;
}

.noUi-target {
  border-radius: var(--border-radius-sm);
  border-color: var(--separator);
  background: var(--foreground);
  box-shadow: initial;
}

.noUi-horizontal .noUi-handle {
  width: 18px;
  height: 18px;
  right: -9px;
  top: -6px;
  border-radius: var(--border-radius-md);
  outline: initial !important;
}

.noUi-handle:after,
.noUi-handle:before {
  height: 6px;
  left: 6px;
  top: 5px;
  background: rgba(var(--primary-rgb), 0.5);
}

.noUi-handle:after {
  left: 9px;
}

.noUi-connect {
  background: var(--primary);
}

.noUi-handle {
  box-shadow: initial;
  border-color: var(--separator);
  background: var(--foreground);
}

.noUi-vertical {
  width: 8px;
}

.noUi-vertical .noUi-handle {
  width: 18px;
  height: 18px;
  right: -6px;
  top: -6px;
  border-radius: var(--border-radius-md);
  outline: initial !important;
}

.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  width: 6px;
  left: 5px;
  top: 6px;
  background: rgba(var(--primary-rgb), 0.5);
}
.noUi-vertical .noUi-handle:after {
  top: 9px;
}

.noUi-tooltip {
  background: var(--primary);
  color: var(--light-text);
  padding: calc(var(--card-spacing-xs) / 2) var(--card-spacing-xs);
  border-radius: var(--border-radius-md);
  border: initial;
  font-size: 0.875em;
  margin-bottom: 4px;
  display: table !important;
  &:after {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: transparent;
    border-top-color: var(--primary);
    border-width: 4px;
    margin-left: -4px;
  }
}

.noUi-horizontal {
  .noUi-tooltip {
    bottom: initial !important;
    top: 140%;

    &:after {
      border-bottom-color: var(--primary);
      border-top-color: transparent;
    }
  }
}

.noUi-pips,
.noUi-value-sub {
  color: var(--alternate);
}

.noUi-marker,
.noUi-marker-large {
  background: var(--separator);
}

.noUi-vertical {
  .noUi-tooltip {
    margin-bottom: 0;
    margin-left: 5px;
    right: initial;
    left: 120%;
    &:after {
      top: 50%;
      left: -8px;
      border: solid transparent;
      content: ' ';
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      border-color: transparent;
      border-right-color: var(--primary);
      border-width: 4px;
      margin-left: 0;
      margin-top: -4px;
    }
  }
}

.tooltip-start {
  .noUi-vertical {
    .noUi-tooltip {
      margin-bottom: 0;
      margin-right: 5px;
      margin-left: initial;
      right: 120%;
      left: initial;
      &:after {
        top: 50%;
        left: initial;
        right: -8px;
        border: solid transparent;
        content: ' ';
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        border-color: transparent;
        border-left-color: var(--primary);
        border-width: 4px;
        margin-left: 0;
        margin-top: -4px;
      }
    }
  }
}

[disabled] .noUi-connect {
  background: var(--separator);
}

[disabled] .noUi-handle:after,
[disabled] .noUi-handle:before {
  background: var(--separator);
}

.top-label {
  &.custom-control-container.slider-container {
    padding: 2.25rem 0.75rem 0.15rem 0.75rem !important;
  }
}

.filled {
  &.custom-control-container.slider-container {
    padding-top: 1.25rem;
  }
}

.noUi-horizontal {
  .noUi-handle {
    right: -9px;
  }

  &.noUi-target {
    padding: 0 8px;
  }

  .noUi-connects {
    margin: 0 -8px;
    width: calc(100% + 16px);
  }
}

.noUi-vertical {
  .noUi-handle {
    top: initial;
    height: 16px;
    bottom: initial;
  }

  &.noUi-target {
    padding: 8px 0;
  }

  .noUi-connects {
    margin: -8px 0;
    height: calc(100% + 16px);
  }
}

.form-floating.slider-container {
  border: 1px solid var(--separator);
  border-radius: var(--border-radius-md);
  height: auto;
  min-height: 52px;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  padding-top: 2.25rem !important;

  label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--muted);
  }
}
