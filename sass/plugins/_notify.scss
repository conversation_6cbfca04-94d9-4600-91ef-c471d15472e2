/*
*
* Notify
*
* Notify plugin styles.
*
*/

.alert *[data-notify='title'] {
  font-size: 1em;
  vertical-align: middle;
  padding-top: 3px;
  display: inline-block;
}

.alert *[data-notify='icon'] {
  vertical-align: middle;
  &:before,
  &:after {
    margin-right: 10px;
  }
}

.alert *[data-notify='icon'] {
  vertical-align: middle;
  img {
    width: var(--input-height);
    margin-right: 10px;
    border-radius: var(--border-radius-xl);
  }
}

.alert *[data-notify='progressbar'] {
  margin-top: 10px;
  height: 2px;
}

.alert *[data-notify='message'] {
  display: block;
}

div[data-notify='container'] {
  padding: var(--card-spacing-xs) var(--card-spacing-sm);

  &.alert-primary {
    background-color: var(--background);
    border: 1px solid var(--primary);
  }

  &.alert-secondary {
    background-color: var(--background);
    border: 1px solid var(--secondary);
  }

  &.alert-tertiary {
    background-color: var(--background);
    border: 1px solid var(--tertiary);
  }

  &.alert-quaternary {
    background-color: var(--background);
    border: 1px solid var(--quaternary);
  }

  &.alert-success {
    background-color: var(--background);
    border: 1px solid var(--success);
  }
  &.alert-danger {
    background-color: var(--background);
    border: 1px solid var(--danger);
  }

  &.alert-warning {
    background-color: var(--background);
    border: 1px solid var(--warning);
  }

  &.alert-info {
    background-color: var(--background);
    border: 1px solid var(--info);
  }

  &.alert-light {
    background-color: var(--background);
    border: 1px solid var(--light);
  }

  &.alert-dark {
    background-color: var(--background);
    border: 1px solid var(--dark);
  }

  span[data-notify='message'] {
    color: var(--alternate);
  }
}
