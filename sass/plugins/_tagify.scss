/*
*
* Tagify
*
* Tagify plugin styles.
*
*/

.tagify {
  --tags-border-color: var(--separator);
  --tags-hover-border-color: var(--separator);
  --tags-focus-border-color: rgba(var(--primary-rgb), 1);
  --tag-bg: var(--foreground);
  --tag-text-color: var(--body);
  --tag-text-color--edit: var(--body);
  --tag-pad: 0 5px;
  --tag-invalid-color: var(--danger);
  --tag-invalid-bg: var(--foreground);
  --tag-remove-bg: var(--foreground);
  --tag-remove-btn-color: var(--body);
  --tag-remove-btn-bg: none;
  --tag-remove-btn-bg--hover: var(--danger);
  --input-color: inherit;
  --tag--max-width: auto;
  --tag-hide-transition: 0s;
  --placeholder-color: var(--muted);
  --placeholder-color-focus: var(--muted);
  --loader-size: 0.8em;
}

.tagify {
  border-radius: var(--border-radius-md);
  line-height: 1.5;
  min-height: var(--input-height);
  padding-left: 0.5rem;
  transition-duration: initial !important;
  padding-right: 10px;
  padding-top: 0;
  padding-bottom: 0;

  * {
    transition-duration: initial !important;
    transition-delay: initial !important;
  }
  .tagify__input {
    padding: 0.4rem 0.25rem 0.4rem 0.25rem;
    margin: initial;
    transition-duration: initial !important;
    transition-delay: initial !important;

    &:focus:before {
      transition-duration: initial !important;
      transition-delay: initial !important;
    }
    &:empty::before {
      transition-duration: initial !important;
      transition-delay: initial !important;
    }
  }
}

.tagify__tag > div > * {
  transition-duration: initial !important;
  transition-delay: initial !important;
}

.tagify__tag {
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--separator) !important;
  margin: 7px 5px 0 0;
  box-shadow: initial !important;

  &:first-of-type {
    margin-left: 1px;
  }
}

.tagify__tag > div {
  height: 18px;
  box-shadow: initial !important;
  border: initial !important;
  background: initial !important;
}

.tagify__tag > div > * {
  line-height: 1.3;
}

.tagify__tag > div::before {
  box-shadow: initial !important;
  border: initial !important;
  background: initial !important;
}

.tagify__tag__removeBtn {
  color: var(--muted) !important;
  background: initial !important;
  font: inherit;
  font-size: 1em;
  margin-top: -2px;
}

.tagify__tag__removeBtn:hover + div::before {
  box-shadow: initial !important;
}

.tagify__tag__removeBtn:hover + div > span {
  opacity: initial;
}

.tagify__tag__removeBtn:hover {
  color: var(--primary) !important;
}

.tagify__tag__removeBtn::after {
  content: 'x';
  line-height: 1;
}

.tagify__input::before {
  line-height: 1.5;
}

.tagify__dropdown[placement='bottom'] {
  margin-top: 3px;
}

.tagify__dropdown[placement='top'] {
  margin-top: -5px;
}

.tagify__dropdown[placement='top'] .tagify__dropdown__wrapper {
  border: 1px solid rgba(var(--primary-rgb), 1);
}

.tagify__dropdown__wrapper {
  border: 1px solid rgba(var(--primary-rgb), 1);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
  background: var(--foreground);
}

.tagify__dropdown__item {
  padding: 0.5rem 0.75rem !important;
  line-height: 1.3;
  margin: 0;
  border-radius: var(--border-radius-sm);

  &:hover,
  &.tagify__dropdown__item--active {
    background: var(--separator-light) !important;
    color: var(--primary);
  }
}

// Custom Look
.custom-look .tagify__input {
  display: none;
}

.custom-look {
  border: initial;
  padding: initial;
  display: inline;

  .tagify__tag {
    margin-top: 0;
    border-radius: var(--border-radius-md);
    height: var(--input-height);
    padding: 0.4rem 0.75rem 0.375rem 0.75rem;
    margin: initial;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
  }

  .tagify__tag__removeBtn {
    margin-left: 0;
    margin-right: 0;
  }

  .tagify__tag > div {
    height: initial;
    padding-left: 0;

    .tagify__tag-text {
      line-height: 1.5;
    }
  }

  .tagify__input {
    display: none;
  }
}

.tagify__dropdown[position='text'] {
  margin-left: -23px;
  margin-top: 13px;
}

// Mix
.tagify--mix {
  &.tagify {
    padding: initial;
  }
  .tagify__input {
    padding: 0.45rem 0.75rem 0.45rem 0.75rem;
    .tagify__tag {
      line-height: 1.25;
    }
  }

  .tagify__input .tagify__tag:first-of-type {
    margin-left: -0.1rem;
  }
}

// Outside
.tagify--outside {
  border: 0;
  padding: initial;

  .tagify__tag:first-of-type {
    margin-left: initial;
  }
}

.tagify--outside .tagify__input {
  order: -1;
  flex: 100%;
  margin-bottom: initial;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--separator) !important;
  padding: 0.4rem 0.75rem 0.375rem 0.75rem;
}

.customSuggestionsList > div {
  max-height: 300px;
  margin-top: 3px;
  overflow: auto;
}

// Users List
.tagify__dropdown.users-list .tagify__dropdown__item {
  padding: 0.5em 0.7em;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0 1em;
  grid-template-areas:
    'avatar name'
    'avatar email';
}

.tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap {
  grid-area: avatar;
  width: var(--input-height);
  height: var(--input-height);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
}

.tagify__dropdown.users-list img {
  width: 100%;
  vertical-align: top;
}

.tagify__dropdown.users-list strong {
  grid-area: name;
  width: 100%;
  align-self: center;
}

.tagify__dropdown.users-list span {
  grid-area: email;
  width: 100%;
  font-size: 0.9em;
  opacity: 0.6;
}

.tagify__dropdown.users-list .addAll {
  gap: 0;
}

.users-list-container .tagify__tag {
  white-space: nowrap;
}

.users-list-container .tagify__tag .tagify__tag__avatar-wrap {
  width: 18px;
  height: 18px;
  border-radius: var(--border-radius-sm);
  margin-right: 5px;
}

.users-list-container .tagify__tag > div {
  padding-left: initial;
}

.users-list-container .tagify__tag img {
  width: 100%;
  vertical-align: top;
}

// Props Countries
.tagify__dropdown.extra-properties .tagify__dropdown__item > img {
  display: inline-block;
  vertical-align: middle;
  height: 20px;
  transform: scale(0.75);
  margin-right: 5px;
  border-radius: 2px;
}

.tagify.countries .tagify__input {
  min-width: 175px;
}

.tagify.countries tag {
  white-space: nowrap;
}
.tagify.countries tag img {
  display: inline-block;
  height: 18px;
  margin-right: 5px;
  border-radius: 2px;
  border-radius: var(--border-radius-sm);
}

.tagify.countries tag div {
  padding-left: initial;
}

.tagify[readonly]:not(.tagify--mix) > .tagify__input {
  margin: initial;
}

.tagify[readonly]:not(.tagify--mix) .tagify__tag > div {
  color: var(--muted);
}

.tagify[readonly]:not(.tagify--mix) {
  background: rgba(var(--separator-rgb), 0.5) !important;
}

.tagify__tag[readonly] {
  background: rgba(var(--separator-rgb), 0.5) !important;
  color: var(--muted);
}

.tagify--select::after {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  opacity: 1;
  right: 10px;
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate);
  transform: rotate(135deg);
  bottom: initial;
  top: 12px;
  transition: initial !important;
}

.tagify--select[aria-expanded='true']::after {
  transform: rotate(-45deg);
  top: 13px;
}

.tagify--select .tagify__tag__removeBtn {
  margin-top: -4px;
  margin-right: initial;
}

.top-label {
  .tagify {
    padding-top: 1rem;
    min-height: 52px;
  }

  .tagify__tag:first-of-type {
    margin-left: 5px;
  }

  .tagify__tag {
    margin-top: 9px;
  }

  .tagify .tagify__input {
    padding: 0.5rem 0.25rem 0.25rem 0.25rem;
  }
}

.filled {
  .tagify {
    --placeholder-color: var(--alternate);
    padding-top: 4px;
    padding-bottom: 4px;
    min-height: 44px !important;
    padding-left: 41px;
    background: var(--background-light) !important;
    border-color: transparent;

    &.tagify--focus {
      border-color: rgba(var(--primary-rgb), 1);
      background: var(--foreground) !important;
    }
  }

  .tagify__tag:first-of-type {
    margin-left: 5px;
  }
}

.tagify--select .tagify__tag {
  border: initial !important;
  margin: 0;
}

.tagify__tag--editable.tagify--invalid > div::before {
  box-shadow: initial !important;
}

.form-floating {
  .tagify__tag {
    margin-top: 5px;
  }
  .tagify__input {
    padding: 0.25rem 0.25rem 0.25rem 0.25rem;
  }
  .tagify {
    padding-top: 1.4rem;
    padding-bottom: 0;
    height: auto;
    min-height: 52px;
    padding: 1rem 0.75rem;

    & ~ label {
      -webkit-transform: initial;
      transform: initial;
      color: var(--alternate);
      transition: initial;
    }
  }

  .tagify--empty {
    padding: 0.6rem 0.25rem 0.5rem 0.5rem;
    & ~ label {
      transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
    }
  }

  .tagify--focus,
  .tagify:not(.tagify--empty) {
    padding-top: 1.25rem;
    padding-bottom: 0;
    & ~ label {
      -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      color: var(--muted);
      transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
    }
  }

  .tagify__tag:first-of-type {
    margin-left: initial;
  }
}
