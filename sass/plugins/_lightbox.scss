/*
*
* Lightbox
*
* Lightbox plugin styles.
*
*/

.baguetteBox-button#close-button svg,
.baguetteBox-button#next-button svg,
.baguetteBox-button#previous-button svg {
  display: none;
}

.baguetteBox-button {
  font-family: 'CS-Interface' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  width: var(--input-height) !important;
  height: var(--input-height) !important;
  border-radius: var(--border-radius-md);
  background-color: rgba(var(--dark-text-rgb), 0.25) !important;
  box-shadow: inset 0 0 0 1px rgba(var(--light-text-rgb), 0.75) !important;
  color: rgba(var(--light-text-rgb), 0.75) !important;

  &:hover {
    box-shadow: initial;
    background-color: var(--light-text) !important;
    color: var(--primary) !important;
  }
}

.baguetteBox-button#close-button {
  right: 2% !important;
  top: 2% !important;
  &:before {
    content: '\e91b';
  }
}

#baguetteBox-slider img {
  border-radius: var(--border-radius-lg);
}

.baguetteBox-button#next-button,
.baguetteBox-button#previous-button {
  top: calc(50% - 18px);
}

.baguetteBox-button#next-button {
  &:before {
    content: '\e917';
  }
}

.baguetteBox-button#previous-button {
  &:before {
    content: '\e916';
  }
}

#baguetteBox-overlay .full-image figcaption {
  padding: 15px 5px;
  background-color: initial;
  border-top: 1px solid rgba(var(--light-text-rgb), 0.25);
  background-color: rgba(0, 0, 0, 0.2) !important;
  color: var(--light-text);
}

#baguetteBox-overlay .full-image img {
  box-shadow: initial !important;
}
