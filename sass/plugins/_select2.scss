/*
*
* Select2
*
* Select2 form control styles.
*
*/

.select2-container--bootstrap4.select2-container--focus .select2-selection {
  box-shadow: initial;
}

.select2-container--bootstrap4 .select2-selection {
  color: var(--body);
  box-shadow: initial !important;
  background-color: var(--foreground);
  border: 1px solid var(--separator) !important;
  border-radius: var(--border-radius-md) !important;
  min-height: var(--input-height) !important;
  font-size: 1em;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding: 0.25rem 0.75rem 0.375rem 0.75rem;
  min-height: var(--input-height);
}

.select2-container .select2-search--inline .select2-search__field {
  margin-left: 0;
  margin-top: 6px;
  line-height: 1.4;
  background: initial;
  color: var(--body);
}

.select2-container--open .select2-selection,
.select2-container--focus .select2-selection {
  border: 1px solid rgba(var(--primary-rgb), 1) !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
  line-height: 1.8;
  color: var(--body);
}
.select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
  line-height: 1.8;
  color: var(--muted);
}

.select2-container--bootstrap4 .select2-dropdown.select2-dropdown--below {
  margin-top: 3px;
}

.select2-container--bootstrap4 .select2-dropdown.select2-dropdown--above {
  margin-top: -3px;
}

.select2-container--bootstrap4 .select2-dropdown {
  border: 1px solid rgba(var(--primary-rgb), 1) !important;
  padding: 0.75rem;
  border-radius: var(--border-radius-md) !important;
  background: var(--foreground);
}

.select2-search--dropdown .select2-search__field {
  border-radius: var(--border-radius-sm);
  height: 28px;
  font-size: 0.9em;
  padding: 0.25rem 0.75rem;
  background-color: var(--foreground);
  border-color: var(--separator);
  border-radius: var(--border-radius-sm);
  color: var(--body);
}

.select2-search--dropdown {
  padding: 0;
  margin-bottom: 5px;
}

.select2-results__option {
  padding: 0.5rem 0.75rem !important;
  line-height: 1.3;
}

.select2-container--bootstrap4 .select2-results__option--highlighted,
.select2-container--bootstrap4 .select2-results__option--highlighted.select2-results__option[aria-selected='true'] {
  cursor: pointer;
  color: var(--primary);
  background: var(--separator-light) !important;
  border-radius: var(--border-radius-sm);
}

.select2-results__option.select2-results__option--selectable.select2-results__option--selected {
  color: var(--primary) !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow b {
  border: initial;
  border-top: 1px solid var(--alternate);
  border-right: 1px solid var(--alternate);
  transform: rotate(135deg);
  width: 5px;
  height: 5px;
  margin-top: -4px;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
  padding: 0.1rem 0.75rem 0 0.75rem;
  .select2-search__field {
    padding-left: 0.25rem;
  }
}

.select2-selection__choice__remove {
  border: initial;
  background: initial;
  color: var(--alternate);
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
  border-radius: var(--border-radius-sm);
  color: var(--body);
  border-color: var(--separator);
  margin-top: 5px;
  margin-right: 5px;
  line-height: 1.3;
}

.select2-container .select2-search--inline {
  float: left;
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline-block;
}

.select2-container--bootstrap4 .select2-dropdown .select2-results__option[aria-selected='true'] {
  background: var(--separator-light) !important;
  border-radius: var(--border-radius-sm);
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: var(--primary) !important;
}

.w-100 .select2 {
  width: 100% !important;
}

.top-label {
  .select2-selection {
    min-height: 52px !important;
  }

  .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 1.5rem 0.75rem 0.25rem 0.75rem !important;
  }

  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 1.5rem 0.75rem 0rem 0.75rem !important;

    .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered .select2-search__field {
      padding-left: initial;
    }
  }
}

.filled {
  .select2-selection {
    min-height: 44px !important;
    border: 1px solid transparent !important;
    background: var(--background-light) !important;
  }

  .select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 45px;
    padding-top: 9px;
  }

  .select2-container--open .select2-selection {
    border: 1px solid rgba(var(--primary-rgb), 1) !important;
    background: initial !important;
  }

  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding-left: 45px;
    padding-top: 5px;
  }
  .select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
    color: var(--alternate);
  }
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove {
  color: var(--muted);
}

.hide-search-searching {
  .select2-search {
    display: none;
  }
}

.select2-container--bootstrap4.select2-container--disabled .select2-selection {
  background: rgba(var(--separator-rgb), 0.5) !important;
  color: var(--muted);
  border-color: var(--separator);
}

.form-floating {
  .select2-selection {
    height: auto;
    min-height: 52px !important;
    padding: 1rem 0rem;
  }

  .select-floating ~ label {
    -webkit-transform: initial;
    transform: initial;
    color: var(--alternate);
    transition: initial;
  }

  .select2.full,
  .select2.show {
    & ~ label {
      -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      color: var(--muted);
      transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
    }

    & ~ .select2 .select2-selection {
      padding-top: 1.25rem;
      padding-bottom: 0;
    }
  }
}

.option-circle {
  width: 15px;
  height: 15px;
  border: 1px solid var(--primary);
}

.select2-dropdown {
  z-index: 1061;
}
