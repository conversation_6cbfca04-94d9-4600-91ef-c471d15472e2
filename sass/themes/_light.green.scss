/*
*
* Light Green
*
* Theme variables.
*
*/

html[data-color='light-green'] {
  --primary: #16a34f;
  --secondary: #41c119;
  --tertiary: #007e63;
  --quaternary: #085327;
  --primary-rgb: 22, 163, 79;
  --secondary-rgb: 68, 189, 12;
  --tertiary-rgb: 0, 126, 99;
  --quaternary-rgb: 8, 83, 39;

  --primary-darker: #127a3c;
  --secondary-darker: #349b14;
  --tertiary-darker: #014b3b;
  --quaternary-darker: #053118;

  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;

  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;

  --background-theme: #eaf1ec;
  --background-light: #f8f8f8;

  --gradient-1: #16923b;
  --gradient-2: #32af5b;
  --gradient-3: #42ca6f;

  --gradient-1-darker: #137c3d;
  --gradient-2-darker: #2c924e;
  --gradient-3-darker: #489b3d;

  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;

  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;

  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;

  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;

  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-light: 0px 3px 10px rgba(0, 0, 0, 0.07);

  --background-navcolor-light: #fff;
  --background-navcolor-dark: #24352b;

  --theme-image-filter: hue-rotate(283deg) contrast(0.9);
}
