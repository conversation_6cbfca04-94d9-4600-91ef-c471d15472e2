/*
*
* Dark Green
*
* Theme variables.
*
*/

html[data-color='dark-green'] {
  --primary: #24af5c;
  --secondary: #62c943;
  --tertiary: #0c8033;
  --quaternary: #0e856b;
  --primary-rgb: 36, 175, 92;
  --secondary-rgb: 98, 201, 67;
  --tertiary-rgb: 12, 128, 51;
  --quaternary-rgb: 14, 133, 107;

  --primary-darker: #1a7a40;
  --secondary-darker: #4a9732;
  --tertiary-darker: #074e1f;
  --quaternary-darker: #095040;

  --body: #c1c1c1;
  --alternate: #999999;
  --muted: #727272;
  --separator: #474747;
  --separator-light: #2e2e2e;
  --body-rgb: 193, 193, 193;
  --alternate-rgb: 153, 153, 153;
  --muted-rgb: 114, 114, 114;
  --separator-rgb: 71, 71, 71;
  --separator-light-rgb: 46, 46, 46;

  --background: #1d1d1d;
  --foreground: #242424;
  --background-rgb: 29, 29, 29;
  --foreground-rgb: 36, 36, 36;

  --background-theme: #242424;
  --background-light: #292929;

  --gradient-1: #11722e;
  --gradient-2: #298b4a;
  --gradient-3: #2d8a4c;

  --gradient-1-darker: #0d5e25;
  --gradient-2-darker: #247740;
  --gradient-3-darker: #267942;

  --light-text: #f0f0f0;
  --dark-text: #191c1f;
  --light-text-darker: #e9e9e9;
  --dark-text-darker: #08090a;

  --light-text-rgb: 240, 240, 240;
  --dark-text-rgb: 25, 28, 31;

  --danger: #b62836;
  --info: #298a99;
  --warning: #ebb71a;
  --success: #418b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #a0a0a0;
  --alternate-darker: #6e6e6e;
  --muted-darker: #4e4e4e;
  --separator-darker: #353535;

  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;

  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.2);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.4);

  --background-navcolor-light: #fff;
  --background-navcolor-dark: #242424;

  --theme-image-filter: hue-rotate(285deg) brightness(0.8) contrast(0.9);
}
