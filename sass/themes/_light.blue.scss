/*
*
* Light Blue
*
* Theme variables.
*
*/

html[data-color='light-blue'] {
  --primary: #2499e3;
  --secondary: #50c6db;
  --tertiary: #1859bb;
  --quaternary: #2a2c7c;
  --primary-rgb: 36, 153, 227;
  --secondary-rgb: 48, 198, 220;
  --tertiary-rgb: 24, 89, 187;
  --quaternary-rgb: 42, 44, 124;

  --primary-darker: #1d7ab4;
  --secondary-darker: #409faf;
  --tertiary-darker: #124188;
  --quaternary-darker: #1a1a4d;

  --body: #4e4e4e;
  --alternate: #7c7c7c;
  --muted: #afafaf;
  --separator: #dddddd;
  --separator-light: #f1f1f1;
  --body-rgb: 59, 59, 59;
  --alternate-rgb: 124, 124, 124;
  --muted-rgb: 176, 176, 176;
  --separator-rgb: 221, 221, 221;
  --separator-light-rgb: 241, 241, 241;

  --background: #f9f9f9;
  --foreground: #ffffff;
  --background-rgb: 249, 249, 249;
  --foreground-rgb: 255, 255, 255;

  --background-theme: #eaf0f1;
  --background-light: #f8f8f8;

  --gradient-1: #238dcf;
  --gradient-2: #31afe6;
  --gradient-3: #2fb9f5;

  --gradient-1-darker: #1e7cb6;
  --gradient-2-darker: #2d9fcf;
  --gradient-3-darker: #59b4c4;

  --light-text: #fff;
  --dark-text: #343a40;
  --light-text-darker: #eeeeee;
  --dark-text-darker: #23272b;

  --light-text-rgb: 255, 255, 255;
  --dark-text-rgb: 52, 58, 64;

  --danger: #cf2637;
  --info: #279aac;
  --warning: #ebb71a;
  --success: #439b38;
  --light: #dadada;
  --dark: #4e4e4e;
  --danger-darker: #771a23;
  --info-darker: #19545d;
  --warning-darker: #aa830f;
  --success-darker: #285422;
  --light-darker: #c9c9c9;
  --dark-darker: #282828;
  --body-darker: #333333;
  --alternate-darker: #616161;
  --muted-darker: #888888;
  --separator-darker: #c0c0c0;

  --danger-rgb: 182, 40, 54;
  --info-rgb: 41, 138, 153;
  --warning-rgb: 235, 183, 26;
  --success-rgb: 65, 139, 56;
  --light-rgb: 218, 218, 218;
  --dark-rgb: 78, 78, 78;

  --menu-shadow: 0px 3px 10px rgba(0, 0, 0, 0.12);
  --menu-shadow-navcolor: 0px 3px 10px rgba(0, 0, 0, 0.07);

  --background-navcolor-light: #fff;
  --background-navcolor-dark: #253a52;

  --theme-image-filter: hue-rotate(0deg);
}
