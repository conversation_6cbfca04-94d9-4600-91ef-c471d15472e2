/*
*
* Settings
*
* Settings modal styles.
*
*/

#settings {
  .card {
    height: 56px;
    border: 1px solid var(--separator);
    display: block;
  }

  .radius-rounded {
    .figure {
      border-radius: 5px !important;
    }
  }

  .radius-regular {
    .figure {
      border-radius: 2px !important;
    }
  }

  .radius-flat {
    .figure {
      border-radius: 0 !important;
    }
  }

  .option {
    &.active,
    &:hover {
      .card {
        border: 1px solid var(--primary);
      }

      .text-part {
        color: var(--primary) !important;
      }
    }
  }

  .figure {
    border-radius: 5px;
    display: block;

    &.figure-primary {
      background: var(--primary);
    }

    &.figure-muted {
      background: var(--muted);
    }

    &.figure-secondary {
      background: rgba(var(--separator-rgb), 0.6);
    }

    &.figure-dark {
      background: var(--dark);
    }

    &.figure-light {
      background: var(--light);
    }

    &.top {
      width: 100%;
      height: 6px;
      margin-bottom: 5px;
    }

    &.bottom {
      width: 100%;
      height: 12px;

      &.small {
        width: 50%;
        margin-left: 25%;
      }
    }

    &.left {
      height: 100%;
      width: 6px;
      margin-right: 5px;
      float: left;

      &.large {
        width: 14px;
      }
    }

    &.right {
      height: 100%;
      float: right;
      width: 80px;
      &.small {
        width: 70px;
        &.top {
          height: 6px;
          margin-bottom: 5px;
        }

        &.bottom {
          height: 12px;
          margin-left: initial;
        }
      }
    }
  }

  .color {
    height: 56px;
    div {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  .blue-light {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%232499e3;%7D.cls-2%7Bfill:%2350c6db;%7D.cls-3%7Bfill:%2313467a;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .blue-dark {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%231d72a7;%7D.cls-2%7Bfill:%23319cdf;%7D.cls-3%7Bfill:%23135299;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .red-light {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fb3d5e;%7D.cls-2%7Bfill:%23fa764e;%7D.cls-3%7Bfill:%23f79d3e;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .red-dark {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23bb3f53;%7D.cls-2%7Bfill:%23d46745;%7D.cls-3%7Bfill:%23963444;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .green-light {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%2315a350;%7D.cls-2%7Bfill:%2364c40b;%7D.cls-3%7Bfill:%23147a3f;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .green-dark {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23138b45;%7D.cls-2%7Bfill:%2345a133;%7D.cls-3%7Bfill:%231a5c35;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .pink-light {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23dd428d;%7D.cls-2%7Bfill:%23f782ba;%7D.cls-3%7Bfill:%23be447f;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .pink-dark {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23b14579;%7D.cls-2%7Bfill:%23d87aa7;%7D.cls-3%7Bfill:%238f4468;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .purple-light {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%238650b3;%7D.cls-2%7Bfill:%23b577e9;%7D.cls-3%7Bfill:%23692c9b;%7D.cls-4%7Bfill:%23ececec;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }

  .purple-dark {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 28'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%237844a3;%7D.cls-2%7Bfill:%239e6cc7;%7D.cls-3%7Bfill:%23622296;%7D.cls-4%7Bfill:%233d3d3d;%7D%3C/style%3E%3C/defs%3E%3Crect class='cls-1' width='49' height='28' rx='5'/%3E%3Crect class='cls-2' x='57' width='30' height='28' rx='5'/%3E%3Crect class='cls-3' x='95' width='30' height='11' rx='5'/%3E%3Crect class='cls-4' x='95' y='17' width='30' height='11' rx='5'/%3E%3C/svg%3E");
  }
}

html[data-radius='flat'] #settings {
  .figure {
    border-radius: 0;
  }
}

html[data-radius='standard'] #settings {
  .figure {
    border-radius: 2px;
  }
}

.settings-button {
  opacity: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
  padding: 0.5rem 0.9rem 0.5rem 1.1rem;
  height: 46px;
  font-size: 18px;
  position: fixed !important;
  right: 0;
  top: 50%;
  z-index: 1000 !important;
  margin-top: -23px;

  &::before {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
  }
}

html[data-show='true'] {
  .settings-button {
    opacity: 1;
  }
}
