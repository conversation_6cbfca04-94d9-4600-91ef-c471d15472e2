/*
*
* Typography
*
* Template styles for typography.
*
*/

p {
  font-family: var(--font);
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: var(--font-heading);
  color: var(--body);
  font-weight: 400;
  line-height: 1.25;
}

.font-standard {
  font-family: var(--font);
}

.font-heading {
  font-family: var(--font-heading);
}

.text-medium {
  font-size: 0.9em !important;
}

.text-semi-large {
  font-size: 1.1em !important;
}

.text-xlarge {
  font-size: 2.7em !important;
  font-weight: 300 !important;
}

.text-small {
  font-size: 0.75em !important;
  font-weight: initial !important;
}

.text-extra-small {
  font-size: 0.7em !important;
  line-height: 1em !important;
}

.text-large {
  font-size: 1.75em !important;
  font-weight: 300 !important;
}

.font-weight-bold {
  font-weight: 600;
}

[class*='cs-'] {
  font-size: 18px;
}

.heading {
  font-size: 1.1em;
  font-weight: 500;
  margin-bottom: 0.7em;
}

.small-title {
  font-family: var(--font-heading);
  font-weight: 400;
  margin-bottom: 0;
  font-size: 1em;
  color: var(--primary);
  height: var(--small-title-height);
}

.blockquote {
  font-size: 1em;
}

.display-1 {
  font-family: var(--font-heading);
  font-size: 3.5em;
  line-height: 1.2;
  font-weight: 300;
  @include respond-below(sm) {
    font-size: 3em;
  }
}

.display-2 {
  font-family: var(--font-heading);
  font-size: 3em;
  line-height: 1.2;
  font-weight: 300;
  @include respond-below(sm) {
    font-size: 2.75em;
  }
}

.display-3 {
  font-family: var(--font-heading);
  font-size: 2.15em;
  line-height: 1.2;
  font-weight: 300;
  @include respond-below(sm) {
    font-size: 2em;
  }
}

.display-4 {
  font-family: var(--font-heading);
  font-size: 1.85em;
  line-height: 1.2;
  font-weight: 300;
  @include respond-below(md) {
    font-size: 1.7em;
  }
  @include respond-below(sm) {
    font-size: 1.6em;
  }
}

.display-5 {
  font-family: var(--font-heading);
  font-size: 1.75em;
  line-height: 1.2;
  font-weight: 300;
  @include respond-below(md) {
    font-size: 1.6em;
  }
  @include respond-below(sm) {
    font-size: 1.5em;
  }
}

.cta-1 {
  font-family: var(--font-heading);
  font-size: 1.5em;
  line-height: 1.4;
  font-weight: 400;
}

.cta-2 {
  font-family: var(--font-heading);
  font-size: 1.35em;
  line-height: 1.4;
  font-weight: 400;
}

.cta-3 {
  font-family: var(--font-heading);
  font-size: 1.25em;
  line-height: 1.4;
  font-weight: 400;
}

.cta-4 {
  font-family: var(--font-heading);
  font-size: 1.15em;
  line-height: 1.25;
  font-weight: 400;
}

.lead {
  font-weight: 300;
}

h1,
.h1 {
  font-size: 1.8em;
  @include respond-below(sm) {
    font-size: 1.5em;
  }
}

h2,
.h2 {
  font-size: 1.65em;
  @include respond-below(sm) {
    font-size: 1.3em;
  }
}

h3,
.h3 {
  font-size: 1.5em;
  @include respond-below(sm) {
    font-size: 1.25em;
  }
}

h4,
.h4 {
  font-size: 1.35em;
  @include respond-below(sm) {
    font-size: 1.15em;
  }
}

h5,
.h5 {
  font-size: 1.1em;
  @include respond-below(sm) {
    font-size: 1em;
  }
}

h6,
.h6 {
  font-size: 1em;
  @include respond-below(sm) {
    font-size: 1em;
  }
}

.icon-24 {
  font-size: 24px !important;
}

.icon-22 {
  font-size: 22px !important;
}

.icon-20 {
  font-size: 20px !important;
}

.icon-18 {
  font-size: 18px !important;
}

.icon-16 {
  font-size: 16px !important;
}

.icon-14 {
  font-size: 14px !important;
}

pre {
  color: var(--body);
}

code {
  color: var(--body);
}

mark,
.mark {
  background-color: rgba(var(--secondary-rgb), 0.1);
}

// Syntax Highlighter
.ll-nam {
  color: var(--tertiary);
}
.ll-num {
  color: var(--info);
}
.ll-str {
  color: var(--secondary);
}
.ll-rex {
  color: var(--warning);
}
.ll-pct {
  color: var(--body);
}
.ll-key {
  color: var(--body);
  font-weight: bold;
}
.ll-com {
  color: var(--text-muted);
  font-style: italic;
}

kbd {
  background: var(--primary);
  color: var(--light-text);
  border-radius: calc(var(--border-radius-md) / 2);
}

.tooltip,
.popover {
  font-family: var(--font);
}

.lh-1 {
  line-height: 1 !important;
}

.lh-1-25 {
  line-height: 1.25 !important;
}

.lh-1-5 {
  line-height: 1.5 !important;
}

.disable-text-selection {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.font-weight-300 {
  font-weight: 300 !important;
}

.line-through {
  text-decoration: line-through;
}

.blockquote-footer {
  margin-top: initial;
}
