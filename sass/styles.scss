/*

Acorn - Bootstrap 5 Html Laravel 8 .Net 5 Admin Template

Table of Contents
|
+-- utils
|   |
|   \-- mixins
|   \-- sizes
|   \-- positions
|   \-- shared
|   
+-- themes
|   |
|   \-- rest
|   \-- dark.blue
|   \-- dark.green
|   \-- dark.red
|   \-- dark.purple
|   \-- dark.pink
|   \-- light.blue
|   \-- light.green
|   \-- light.red
|   \-- light.purple
|   \-- light.pink
|   
+-- bootstrap
|   |
|   \-- accordion
|   \-- alert
|   \-- background
|   \-- badge
|   \-- border
|   \-- button
|   \-- card
|   \-- dropdown
|   \-- form
|   \-- grid
|   \-- inputgroup
|   \-- modal
|   \-- nav
|   \-- offcanvas
|   \-- progress
|   \-- popover
|   \-- spinner
|   \-- texts
|   \-- tables
|   \-- toast
|   \-- tooltip
|   
+-- plugins
|   |
|   \-- autocomplete
|   \-- autosize
|   \-- calendar
|   \-- contextmenu
|   \-- datatable
|   \-- datepicker
|   \-- dropzone
|   \-- editor
|   \-- lightbox
|   \-- listjs
|   \-- player
|   \-- glide
|   \-- notify
|   \-- progressbar
|   \-- rating
|   \-- scrollbar
|   \-- select2
|   \-- slider
|   \-- sortable
|   \-- steps
|   \-- tagify
|   \-- timepicker
|   \-- tour
|   \-- validation
|   \-- wizard
|   
+-- layout
    |
    \-- base
    \-- typography
    \-- main
    \-- nav.primary
    \-- nav.side
    \-- footer
    \-- print
    \-- settings
 
*/

@import 'utils/mixins';
@import 'utils/sizes';
@import 'utils/positions';
@import 'utils/shared';

@import 'themes/shared';
@import 'themes/dark.blue';
@import 'themes/dark.green';
@import 'themes/dark.red';
@import 'themes/dark.purple';
@import 'themes/dark.pink';
@import 'themes/light.blue';
@import 'themes/light.green';
@import 'themes/light.red';
@import 'themes/light.purple';
@import 'themes/light.pink';

@import 'bootstrap/accordion';
@import 'bootstrap/alert';
@import 'bootstrap/background';
@import 'bootstrap/badge';
@import 'bootstrap/border';
@import 'bootstrap/button';
@import 'bootstrap/card';
@import 'bootstrap/dropdown';
@import 'bootstrap/form';
@import 'bootstrap/grid';
@import 'bootstrap/inputgroup';
@import 'bootstrap/modal';
@import 'bootstrap/nav';
@import 'bootstrap/offcanvas';
@import 'bootstrap/progress';
@import 'bootstrap/popover';
@import 'bootstrap/spinner';
@import 'bootstrap/texts';
@import 'bootstrap/tables';
@import 'bootstrap/toast';
@import 'bootstrap/tooltip';

@import 'plugins/autocomplete';
@import 'plugins/autosize';
@import 'plugins/calendar';
@import 'plugins/contextmenu';
@import 'plugins/datatable';
@import 'plugins/datepicker';
@import 'plugins/dropzone';
@import 'plugins/editor';
@import 'plugins/lightbox';
@import 'plugins/listjs';
@import 'plugins/player';
@import 'plugins/glide';
@import 'plugins/notify';
@import 'plugins/progressbar';
@import 'plugins/rating';
@import 'plugins/scrollbar';
@import 'plugins/select2';
@import 'plugins/slider';
@import 'plugins/sortable';
@import 'plugins/steps';
@import 'plugins/tagify';
@import 'plugins/timepicker';
@import 'plugins/tour';
@import 'plugins/validation';
@import 'plugins/wizard';

@import 'layout/base';
@import 'layout/typography';
@import 'layout/main';
@import 'layout/nav.primary';
@import 'layout/nav.side';
@import 'layout/footer';
@import 'layout/print';
@import 'layout/settings';
