<?

include "../db.php";


$user = filter_var($_POST["username"],FILTER_SANITIZE_STRING);
$sifre = filter_var($_POST["password"],FILTER_SANITIZE_STRING);


$admins = $DB->row("select * from yonetici where ad=?", $user);



if (count($admins)<1) {
	header ("Location: index.php?hata=1");
	
} else {
	if ($admins["sifre"] != md5($sifre)) {
		header ("Location: index.php?hata=2");
		
	} else {
		if 	($admins["durum"] == "N") {
			header ("Location: index.php?hata=3");
			
		} else {
			setcookie ("site_login","login_OK");
			setcookie ("site_login_username",$user);
			setcookie ("site_login_userid",$admins["id"]);
			setcookie ("site_login_name",$admins["aciklama"]);
			setcookie ("site_login_tip",$admins["tip"]);
			header ("Location: main.php");
		}
	}
}


?>