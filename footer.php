
    <!-- Vendor Scripts Start -->
    <script src="js/vendor/jquery-3.5.1.min.js"></script>
    <script src="js/vendor/bootstrap.bundle.min.js"></script>
    <script src="js/vendor/OverlayScrollbars.min.js"></script>
    <script src="js/vendor/autoComplete.min.js"></script>
    <script src="js/vendor/clamp.min.js"></script>
    <script src="js/vendor/jquery.validate/jquery.validate.min.js"></script>
    <script src="js/vendor/jquery.validate/additional-methods.min.js"></script>
    <!-- Vendor Scripts End -->
  <script type="text/javascript" src="js/jquery.autocomplete.min.js"></script>

    <!-- Template Base Scripts Start -->
    <script src="font/CS-Line/csicons.min.js"></script>
    <script src="js/base/helpers.js"></script>
    <script src="js/base/globals.js"></script>
    <script src="js/base/nav.js"></script>
    <script src="js/base/search.js"></script>
    <script src="js/base/settings.js"></script>
    <script src="js/base/init.js"></script>
    <!-- Template Base Scripts End -->
    <!-- Page Specific Scripts Start -->
    <script src="js/pages/auth.login.js"></script>
    <script src="js/common.js"></script>
    <script src="js/scripts.js"></script>
	<script src="js/cs/glide.custom.js"></script>
    <script src="js/cs/charts.extend.js"></script>
    <script src="js/pages/dashboard.default.js"></script>

    <!-- Page Specific Scripts End -->


	<!-- Vendor Scripts Start -->
    <script src="js/vendor/Chart.bundle.min.js"></script>
    <script src="js/vendor/chartjs-plugin-datalabels.js"></script>
    <script src="js/vendor/chartjs-plugin-rounded-bar.min.js"></script>
    <script src="js/vendor/glide.min.js"></script>
    <script src="js/vendor/intro.min.js"></script>
    <script src="js/vendor/select2.full.min.js"></script>
    <script src="js/vendor/plyr.min.js"></script>
    <!-- Vendor Scripts End -->

	<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>


$(function() {
	$("#ajaxform").submit(function(e) {
    e.preventDefault();
		var actionurl = e.currentTarget.action;
    $.ajax({
			url: actionurl,
			type: 'post',
			dataType: 'json',
			data: $("#ajaxform").serialize()+"&lang=<?php echo $lang;?>",
			success: function(data) {
			if (data.ok == "-1") {
				alert("İşlem hatası.")       
			} else {
				alert("İşlem başarılı.")
			}
			}
		});
    return false;
  });
});


</script>  


<script type="text/javascript">
	$(document).ready(function(){
	
   	$('#msgform').submit(function() {
	   var form = jQuery('#msgform');
	   q = form.formSerialize();
	   savemsg(q);
	   return false;
	}); 
	
	
	$('#saveform').submit(function() {
	   var form = jQuery('#saveform');
	   q = form.formSerialize();
	   savecizelge(q);
	   return false;
	}); 
	/*
	$("#bolge").autocomplete("getbolge.php", {
			width: 320,
			max: 34,
			highlight: false,
			scroll: true,
			scrollHeight: 300,
			formatItem: function(data, i, n, value) {
				return  value.split("|")[0];
			},
			formatResult: function(row) {
				
			}
			
	});*/
	$('#bolge').autocomplete({
     serviceUrl: "getbolge.php",
     minChars: 3,
     onSelect: function (suggestion) {
		var bolgeid = suggestion.data.id;
		var bolgead = suggestion.value;
			if (bolgead) {
				$('#mybolge').append($('<option>', { 
					value: bolgeid,
					text : bolgead 
				}));
			 
			}
			$("#bolge").val("");
			$('#mybolge option').prop('selected', true);

    }, 
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {

    }
});

/*

	$("a.anketrapor").fancybox({
			'transitionIn'	:	'elastic',
			'transitionOut'	:	'elastic',
			'width'		:	500, 
			'height'		:	300, 
			'scrolling'   : 'no', 
			'overlayShow'	:	false
		});*/


	
}); 

function DelItem(allvalue,selectitem) {
	$("#"+allvalue+" option:selected").remove(); 
	$("#"+allvalue+" option" ).attr("selected","selected");

}
function beforeSubmit() {
	$('#mybolge option').prop('selected', true);
    }

function get_ilce(il) {
	var links = "get_ilce.php?q="+il;
	$.get(links, function(data) {
		$("#filcead").html(data);
	});
}

function set_ilce (bad) {
	$("#ilcead").attr("value",bad);
}
	
</script>

  </body>
</html>
