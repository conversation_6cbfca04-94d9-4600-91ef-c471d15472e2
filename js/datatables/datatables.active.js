(function ($) {
    "use strict";
    
    /*Default*/
    if( $('.data-table-default').length ) {
        $('.data-table-default').DataTable({
            responsive: true,
            "order": [[ 0, "desc" ]],
            stateSave: true,
            "pageLength": 100,
            language: {
                paginate: {
                    previous: '<i class="zmdi zmdi-chevron-left"></i>',
                    next:     '<i class="zmdi zmdi-chevron-right"></i>'
                }
            }
        });
    }
    
    /*Default*/
    if( $('.data-table-nonresponse').length ) {
        $('.data-table-nonresponse').DataTable({
            responsive: false,
            "order": [[ 0, "desc" ]],
            stateSave: true,
            "pageLength": 100,
            language: {
                paginate: {
                    previous: '<i class="zmdi zmdi-chevron-left"></i>',
                    next:     '<i class="zmdi zmdi-chevron-right"></i>'
                }
            }
        });
    }
    

    /*Export Buttons*/
    if( $('.data-table-export').length ) {
        $('.data-table-export').DataTable({
            responsive: true,
            "order": [[ 1, "desc" ]],
            stateSave: true,
            "pageLength": 100,
            dom: 'Bfrtip',
            buttons: ['copy', 'csv', 'excel', 'pdf', 'print'],
            language: {
                paginate: {
                    previous: '<i class="zmdi zmdi-chevron-left"></i>',
                    next:     '<i class="zmdi zmdi-chevron-right"></i>'
                }
            }
        });
    }
    
})(jQuery);