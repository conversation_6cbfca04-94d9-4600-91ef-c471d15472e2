!(function (a) {
  a.fn.datepicker.dates.fo = {
    days: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
    daysMin: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>'],
    months: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Jul<PERSON>', 'August', 'Septembur', '<PERSON><PERSON><PERSON>', 'Novembur', '<PERSON><PERSON><PERSON>'],
    monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', '<PERSON>', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', '<PERSON>'],
    today: '<PERSON> Dag',
    clear: '<PERSON><PERSON><PERSON>',
  };
})(j<PERSON><PERSON>y);
