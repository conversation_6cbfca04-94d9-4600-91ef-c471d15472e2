!(function (a) {
  a.fn.datepicker.dates.et = {
    days: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
    daysMin: ['<PERSON>', '<PERSON>', 'T', '<PERSON>', '<PERSON>', 'R', '<PERSON>'],
    months: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'August', 'September', 'Oktoober', 'November', 'Detsember'],
    monthsShort: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Aug', 'Sept', 'Okt', 'Nov', 'Dets'],
    today: 'Täna',
    clear: 'Tüh<PERSON><PERSON>',
    weekStart: 1,
    format: 'dd.mm.yyyy',
  };
})(jQ<PERSON>y);
