!(function (a) {
  a.fn.datepicker.dates.pl = {
    days: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    daysShort: ['Niedz.', 'Pon.', 'Wt.', 'Śr.', 'Czw.', '<PERSON>ąt.', 'Sob.'],
    daysMin: ['Ndz.', 'Pn.', 'Wt.', 'Śr.', 'Czw.', 'Pt.', 'Sob.'],
    months: ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Listopad', '<PERSON><PERSON><PERSON><PERSON>'],
    monthsShort: ['Sty.', 'Lut.', 'Mar.', '<PERSON>wi.', '<PERSON>', '<PERSON><PERSON>.', '<PERSON>p.', '<PERSON><PERSON>.', '<PERSON><PERSON>.', '<PERSON><PERSON>.', '<PERSON><PERSON>.', '<PERSON><PERSON>.'],
    today: 'D<PERSON><PERSON><PERSON>',
    weekStart: 1,
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    format: 'dd.mm.yyyy',
  };
})(j<PERSON><PERSON>y);
