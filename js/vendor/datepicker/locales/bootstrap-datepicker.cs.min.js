!(function (a) {
  a.fn.datepicker.dates.cs = {
    days: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>t<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Čtv', '<PERSON><PERSON><PERSON>', 'Sob'],
    daysMin: ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>t', 'P<PERSON>', 'So'],
    months: ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>erve<PERSON>', 'Červenec', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Říjen', 'Listopad', 'Prosinec'],
    monthsShort: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Srp', '<PERSON><PERSON><PERSON>', 'Říj', 'Lis', 'Pro'],
    today: 'Dnes',
    clear: 'Vymazat',
    monthsTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    weekStart: 1,
    format: 'dd.mm.yyyy',
  };
})(jQ<PERSON>y);
