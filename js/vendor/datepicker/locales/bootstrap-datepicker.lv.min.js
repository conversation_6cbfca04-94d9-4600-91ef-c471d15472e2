!(function (a) {
  a.fn.datepicker.dates.lv = {
    days: ['<PERSON>v<PERSON>t<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    daysShort: ['Sv', 'P', 'O', 'T', 'C', 'Pk', 'S'],
    daysMin: ['Sv', 'Pr', 'Ot', 'Tr', 'Ce', 'Pk', 'Se'],
    months: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Augusts', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Nov<PERSON><PERSON><PERSON>', 'Dec<PERSON>bri<PERSON>'],
    monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', '<PERSON>', '<PERSON>ū<PERSON>', '<PERSON><PERSON><PERSON>', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'],
    monthsTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    today: '<PERSON><PERSON><PERSON>',
    clear: '<PERSON>d<PERSON><PERSON>st',
    weekStart: 1,
  };
})(jQ<PERSON>y);
