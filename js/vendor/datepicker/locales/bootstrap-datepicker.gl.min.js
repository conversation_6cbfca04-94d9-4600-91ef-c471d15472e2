!(function (a) {
  a.fn.datepicker.dates.gl = {
    days: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    daysMin: ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'V<PERSON>', '<PERSON>'],
    months: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Agos<PERSON>', 'Set<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Nov<PERSON><PERSON>', 'Dec<PERSON><PERSON>'],
    monthsShort: ['<PERSON><PERSON>', 'Feb', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Xu<PERSON>', 'Xu<PERSON>', 'A<PERSON>', 'Sep', 'Out', 'Nov', 'Dec'],
    today: 'Hoxe',
    clear: 'Lim<PERSON>',
    weekStart: 1,
    format: 'dd/mm/yyyy',
  };
})(jQuery);
