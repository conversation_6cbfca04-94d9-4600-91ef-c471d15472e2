/*! lolight v1.3.0 - https://larsjung.de/lolight/ */
!(function (e, t) {
  'object' == typeof exports && 'object' == typeof module
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
    ? define([], t)
    : (e.lolight = t());
})(this, function () {
  function t(e) {
    if ('string' != typeof e) throw new Error('tok: no string');
    for (var t = [], n = u.length, o = !1; e; )
      for (var r = 0; r < n; r += 1) {
        var i = u[r][1].exec(e);
        if (i && 0 === i.index) {
          var a = u[r][0];
          if ('rex' !== a || !o) {
            var l = i[0];
            a === s && c.test(l) && (a = 'key'),
              'spc' === a ? 0 <= l.indexOf('\n') && (o = !1) : (o = a === f || a === s),
              (e = e.slice(l.length)),
              t.push([a, l]);
            break;
          }
        }
      }
    return t;
  }
  function e(e, t) {
    if ('undefined' != typeof document) t(document);
    else if (e) throw new Error('no doc');
  }
  function n(o) {
    e(!0, function (n) {
      var e = t(o.textContent);
      (o.innerHTML = ''),
        e.forEach(function (e) {
          var t = n.createElement('span');
          (t.className = 'll-' + e[0]), (t.textContent = e[1]), o.appendChild(t);
        });
    });
  }
  function o(t) {
    e(!0, function (e) {
      [].forEach.call(e.querySelectorAll(t || '.lolight'), function (e) {
        n(e);
      });
    });
  }
  var r = '_nam#2196f3}_num#ec407a}_str#43a047}_rex#ef6c00}_pct#666}_key#555;font-weight:bold}_com#aaa;font-style:italic}'
      .replace(/_/g, '.ll-')
      .replace(/#/g, '{color:#'),
    c = /^(a(bstract|lias|nd|rguments|rray|s(m|sert)?|uto)|b(ase|egin|ool(ean)?|reak|yte)|c(ase|atch|har|hecked|lass|lone|ompl|onst|ontinue)|de(bugger|cimal|clare|f(ault|er)?|init|l(egate|ete)?)|do|double|e(cho|ls?if|lse(if)?|nd|nsure|num|vent|x(cept|ec|p(licit|ort)|te(nds|nsion|rn)))|f(allthrough|alse|inal(ly)?|ixed|loat|or(each)?|riend|rom|unc(tion)?)|global|goto|guard|i(f|mp(lements|licit|ort)|n(it|clude(_once)?|line|out|stanceof|t(erface|ernal)?)?|s)|l(ambda|et|ock|ong)|m(odule|utable)|NaN|n(amespace|ative|ext|ew|il|ot|ull)|o(bject|perator|r|ut|verride)|p(ackage|arams|rivate|rotected|rotocol|ublic)|r(aise|e(adonly|do|f|gister|peat|quire(_once)?|scue|strict|try|turn))|s(byte|ealed|elf|hort|igned|izeof|tatic|tring|truct|ubscript|uper|ynchronized|witch)|t(emplate|hen|his|hrows?|ransient|rue|ry|ype(alias|def|id|name|of))|u(n(checked|def(ined)?|ion|less|signed|til)|se|sing)|v(ar|irtual|oid|olatile)|w(char_t|hen|here|hile|ith)|xor|yield)$/,
    i = 'com',
    s = 'nam',
    f = 'num',
    u = [
      [f, /#([0-9a-f]{6}|[0-9a-f]{3})\b/],
      [i, /(\/\/|#).*?(?=\n|$)/],
      [i, /\/\*[\s\S]*?\*\//],
      [i, /<!--[\s\S]*?-->/],
      ['rex', /\/(\\\/|[^\n])*?\//],
      ['str', /(['"`])(\\\1|[\s\S])*?\1/],
      [f, /[+-]?([0-9]*\.?[0-9]+|[0-9]+\.?[0-9]*)([eE][+-]?[0-9]+)?/],
      ['pct', /[\\.,:;+\-*\/=<>()[\]{}|?!&@~]/],
      ['spc', /\s+/],
      [s, /[\w$]+/],
      ['unk', /./],
    ];
  return (
    e(!1, function (e) {
      var t = e.querySelector('head'),
        n = e.createElement('style');
      (n.textContent = r),
        t.insertBefore(n, t.firstChild),
        /^(i|c|loade)/.test(e.readyState)
          ? o()
          : e.addEventListener('DOMContentLoaded', function () {
              o();
            });
    }),
    (o.tok = t),
    (o.el = n),
    o
  );
});
