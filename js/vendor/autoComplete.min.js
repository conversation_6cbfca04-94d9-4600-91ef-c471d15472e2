var a, b;
(a = this),
  (b = function () {
    'use strict';
    function i(e, t) {
      for (var n = 0; n < t.length; n++) {
        var i = t[n];
        (i.enumerable = i.enumerable || !1), (i.configurable = !0), 'value' in i && (i.writable = !0), Object.defineProperty(e, i.key, i);
      }
    }
    function s(e, t) {
      (null == t || t > e.length) && (t = e.length);
      for (var n = 0, i = new Array(t); n < t; n++) i[n] = e[n];
      return i;
    }
    function l(e) {
      if ('undefined' == typeof Symbol || null == e[Symbol.iterator]) {
        if (
          Array.isArray(e) ||
          (e = (function (e, t) {
            if (e) {
              if ('string' == typeof e) return s(e, t);
              var n = Object.prototype.toString.call(e).slice(8, -1);
              return (
                'Object' === n && e.constructor && (n = e.constructor.name),
                'Map' === n || 'Set' === n ? Array.from(n) : 'Arguments' === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? s(e, t) : void 0
              );
            }
          })(e))
        ) {
          var t = 0,
            n = function () {};
          return {
            s: n,
            n: function () {
              return t >= e.length ? {done: !0} : {done: !1, value: e[t++]};
            },
            e: function (e) {
              throw e;
            },
            f: n,
          };
        }
        throw new TypeError(
          'Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.',
        );
      }
      var i,
        o,
        r = !0,
        a = !1;
      return {
        s: function () {
          i = e[Symbol.iterator]();
        },
        n: function () {
          var e = i.next();
          return (r = e.done), e;
        },
        e: function (e) {
          (a = !0), (o = e);
        },
        f: function () {
          try {
            r || null == i.return || i.return();
          } finally {
            if (a) throw o;
          }
        },
      };
    }
    function Q(e) {
      return 'string' == typeof e ? document.querySelector(e) : e();
    }
    function u(e) {
      return '<span class='.concat(t, '>').concat(e, '</span>');
    }
    function a(e) {
      return (e.innerHTML = '');
    }
    function d(t, e, n, i, o, r) {
      i({
        event: t,
        query: e instanceof HTMLInputElement ? e.value : e.innerHTML,
        matches: o.matches,
        results: o.list.map(function (e) {
          return e.value;
        }),
        selection: o.list.find(function (e) {
          return t.keyCode === m
            ? e.index === Number(r.getAttribute(h))
            : 'mousedown' === t.type
            ? e.index === Number(t.currentTarget.getAttribute(h))
            : void 0;
        }),
      }),
        a(n);
    }
    function c(t, n, i, o) {
      function r(e) {
        c.classList.remove(v), (s = 1 === e ? c.nextSibling : c.previousSibling);
      }
      function a(e) {
        (c = e).classList.add(v);
      }
      var s,
        l = n.childNodes,
        u = l.length - 1,
        c = void 0;
      (t.onkeydown = function (e) {
        if (0 < l.length)
          switch (e.keyCode) {
            case p:
              c ? (r(0), a(s || l[u])) : a(l[u]);
              break;
            case y:
              c ? (r(1), a(s || l[0])) : a(l[0]);
              break;
            case m:
              c && d(e, t, n, i, o, c);
          }
      }),
        l.forEach(function (e) {
          e.onmousedown = function (e) {
            return d(e, t, n, i, o);
          };
        });
    }
    function e(e, t) {
      t = t || {bubbles: !1, cancelable: !1, detail: void 0};
      var n = document.createEvent('CustomEvent');
      return n.initCustomEvent(e, t.bubbles, t.cancelable, t.detail), n;
    }
    var h = 'data-id',
      X = 'autoComplete_list',
      f = 'autoComplete_result',
      t = 'autoComplete_highlighted',
      v = 'autoComplete_selected',
      m = 13,
      p = 38,
      y = 40;
    e.prototype = window.Event.prototype;
    var g = {
      CustomEventWrapper: ('function' == typeof window.CustomEvent && window.CustomEvent) || e,
      initElementClosestPolyfill: function () {
        Element.prototype.matches || (Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector),
          Element.prototype.closest ||
            (Element.prototype.closest = function (e) {
              var t = this;
              do {
                if (t.matches(e)) return t;
                t = t.parentElement || t.parentNode;
              } while (null !== t && 1 === t.nodeType);
              return null;
            });
      },
    };
    return (function () {
      function K(e) {
        !(function (e, t) {
          if (!(e instanceof t)) throw new TypeError('Cannot call a class as a function');
        })(this, K);
        var t,
          n,
          i = e.selector,
          o = void 0 === i ? '#autoComplete' : i,
          r = e.data,
          a = r.key,
          s = r.src,
          l = r.cache,
          u = void 0 === l || l,
          c = e.query,
          d = e.trigger,
          h = (d = void 0 === d ? {} : d).event,
          f = void 0 === h ? ['input'] : h,
          v = d.condition,
          m = void 0 !== v && v,
          p = e.searchEngine,
          y = void 0 === p ? 'strict' : p,
          g = e.threshold,
          b = void 0 === g ? 0 : g,
          E = e.debounce,
          w = void 0 === E ? 0 : E,
          C = e.resultsList,
          S = (C = void 0 === C ? {} : C).render,
          L = void 0 !== S && S,
          k = C.container,
          x = void 0 !== k && k,
          A = C.destination,
          T = C.position,
          M = void 0 === T ? 'afterend' : T,
          R = C.element,
          q = void 0 === R ? 'ul' : R,
          H = C.navigation,
          I = void 0 !== H && H,
          j = e.sort,
          P = void 0 !== j && j,
          N = e.placeHolder,
          _ = e.maxResults,
          O = void 0 === _ ? 5 : _,
          V = e.resultItem,
          W = (V = void 0 === V ? {} : V).content,
          D = void 0 !== W && W,
          F = V.element,
          U = void 0 === F ? 'li' : F,
          $ = e.noResults,
          z = e.highlight,
          B = void 0 !== z && z,
          G = e.onSelection,
          J = L
            ? ((t = {container: x, destination: A || Q(o), position: M, element: q}),
              (n = document.createElement(t.element)).setAttribute('id', X),
              t.container && t.container(n),
              t.destination.insertAdjacentElement(t.position, n),
              n)
            : null;
        (this.selector = o),
          (this.data = {
            src: function () {
              return 'function' == typeof s ? s() : s;
            },
            key: a,
            cache: u,
          }),
          (this.query = c),
          (this.trigger = {event: f, condition: m}),
          (this.searchEngine = 'loose' === y ? 'loose' : 'function' == typeof y ? y : 'strict'),
          (this.threshold = b),
          (this.debounce = w),
          (this.resultsList = {render: L, view: J, navigation: I}),
          (this.sort = P),
          (this.placeHolder = N),
          (this.maxResults = O),
          (this.resultItem = {content: D, element: U}),
          (this.noResults = $),
          (this.highlight = B),
          (this.onSelection = G),
          this.init();
      }
      var e, t, n;
      return (
        (e = K),
        (t = [
          {
            key: 'search',
            value: function (e, t) {
              var n = t.toLowerCase();
              if ('loose' === this.searchEngine) {
                e = e.replace(/ /g, '');
                for (var i = [], o = 0, r = 0; r < n.length; r++) {
                  var a = t[r];
                  o < e.length && n[r] === e[o] && ((a = this.highlight ? u(a) : a), o++), i.push(a);
                }
                return o !== e.length ? !1 : i.join('');
              }
              if (n.includes(e)) return (e = new RegExp(''.concat(e), 'i').exec(t)), this.highlight ? t.replace(e, u(e)) : t;
            },
          },
          {
            key: 'listMatchedResults',
            value: function (n) {
              var a = this;
              return new Promise(function (e) {
                var r = [];
                n.filter(function (i, o) {
                  function e(e) {
                    var t = e ? i[e] : i;
                    if (t) {
                      var n = 'function' == typeof a.searchEngine ? a.searchEngine(a.queryValue, t) : a.search(a.queryValue, t);
                      n && e ? r.push({key: e, index: o, match: n, value: i}) : n && !e && r.push({index: o, match: n, value: i});
                    }
                  }
                  if (a.data.key) {
                    var t,
                      n = l(a.data.key);
                    try {
                      for (n.s(); !(t = n.n()).done; ) {
                        e(t.value);
                      }
                    } catch (e) {
                      n.e(e);
                    } finally {
                      n.f();
                    }
                  } else e();
                });
                var t = a.sort ? r.sort(a.sort).slice(0, a.maxResults) : r.slice(0, a.maxResults);
                return e({matches: r.length, list: t});
              });
            },
          },
          {
            key: 'ignite',
            value: function () {
              var l = this,
                u = Q(this.selector);
              this.placeHolder && u.setAttribute('placeholder', this.placeHolder);
              function r(n) {
                function i(e, t) {
                  u.dispatchEvent(
                    new g.CustomEventWrapper('autoComplete', {
                      bubbles: !0,
                      detail: {event: e, input: o, query: r, matches: t ? t.matches : null, results: t ? t.list : null},
                      cancelable: !0,
                    }),
                  );
                }
                var o = u instanceof HTMLInputElement || u instanceof HTMLTextAreaElement ? u.value.toLowerCase() : u.innerHTML.toLowerCase(),
                  r = (l.queryValue = l.query && l.query.manipulate ? l.query.manipulate(o) : o),
                  e = l.resultsList.render,
                  t = l.trigger.condition ? l.trigger.condition(r) : r.length >= l.threshold && r.replace(/ /g, '').length;
                if (e) {
                  var s = l.resultsList.view;
                  a(s);
                  t
                    ? l.listMatchedResults(l.dataStream, n).then(function (e) {
                        var t, o, r, a;
                        i(n, e),
                          l.resultsList.render &&
                            (0 === e.list.length && l.noResults
                              ? l.noResults()
                              : ((t = s),
                                (o = e.list),
                                (r = l.resultItem),
                                (a = document.createDocumentFragment()),
                                o.forEach(function (e, t) {
                                  var n = document.createElement(r.element),
                                    i = o[t].index;
                                  n.setAttribute(h, i),
                                    n.setAttribute('class', f),
                                    r.content ? r.content(e, n) : (n.innerHTML = e.match || e),
                                    a.appendChild(n);
                                }),
                                t.appendChild(a),
                                l.onSelection && (l.resultsList.navigation ? l.resultsList.navigation(n, u, s, l.onSelection, e) : c(u, s, l.onSelection, e))));
                      })
                    : i(n);
                } else
                  !e &&
                    t &&
                    l.listMatchedResults(l.dataStream, n).then(function (e) {
                      i(n, e);
                    });
              }
              this.trigger.event.forEach(function (e) {
                var n, i, o;
                u.addEventListener(
                  e,
                  ((n = function (e) {
                    return (
                      (t = e),
                      void Promise.resolve(l.data.cache ? l.dataStream : l.data.src()).then(function (e) {
                        (l.dataStream = e), r(t);
                      })
                    );
                    var t;
                  }),
                  (i = l.debounce),
                  function () {
                    var e = this,
                      t = arguments;
                    clearTimeout(o),
                      (o = setTimeout(function () {
                        return n.apply(e, t);
                      }, i));
                  }),
                );
              });
            },
          },
          {
            key: 'init',
            value: function () {
              var t = this;
              this.data.cache
                ? Promise.resolve(this.data.src()).then(function (e) {
                    (t.dataStream = e), t.ignite();
                  })
                : this.ignite(),
                g.initElementClosestPolyfill();
            },
          },
        ]) && i(e.prototype, t),
        n && i(e, n),
        K
      );
    })();
  }),
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = b())
    : 'function' == typeof define && define.amd
    ? define(b)
    : (a.autoComplete = b());
