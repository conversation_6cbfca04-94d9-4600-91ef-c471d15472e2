/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */

!(function (t, e) {
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = e())
    : 'function' == typeof define && define.amd
    ? define(e)
    : ((t = t || self).Sortable = e());
})(this, function () {
  'use strict';
  function t(e) {
    return (t =
      'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
        ? function (t) {
            return typeof t;
          }
        : function (t) {
            return t && 'function' == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? 'symbol' : typeof t;
          })(e);
  }
  function e(t, e, n) {
    return e in t ? Object.defineProperty(t, e, {value: n, enumerable: !0, configurable: !0, writable: !0}) : (t[e] = n), t;
  }
  function n() {
    return (n =
      Object.assign ||
      function (t) {
        for (var e = 1; e < arguments.length; e++) {
          var n = arguments[e];
          for (var o in n) Object.prototype.hasOwnProperty.call(n, o) && (t[o] = n[o]);
        }
        return t;
      }).apply(this, arguments);
  }
  function o(t) {
    for (var n = 1; n < arguments.length; n++) {
      var o = null != arguments[n] ? arguments[n] : {},
        i = Object.keys(o);
      'function' == typeof Object.getOwnPropertySymbols &&
        (i = i.concat(
          Object.getOwnPropertySymbols(o).filter(function (t) {
            return Object.getOwnPropertyDescriptor(o, t).enumerable;
          }),
        )),
        i.forEach(function (n) {
          e(t, n, o[n]);
        });
    }
    return t;
  }
  function i(t, e) {
    if (null == t) return {};
    var n,
      o,
      i = (function (t, e) {
        if (null == t) return {};
        var n,
          o,
          i = {},
          r = Object.keys(t);
        for (o = 0; o < r.length; o++) (n = r[o]), e.indexOf(n) >= 0 || (i[n] = t[n]);
        return i;
      })(t, e);
    if (Object.getOwnPropertySymbols) {
      var r = Object.getOwnPropertySymbols(t);
      for (o = 0; o < r.length; o++) (n = r[o]), e.indexOf(n) >= 0 || (Object.prototype.propertyIsEnumerable.call(t, n) && (i[n] = t[n]));
    }
    return i;
  }
  function r(t) {
    return (
      (function (t) {
        if (Array.isArray(t)) {
          for (var e = 0, n = new Array(t.length); e < t.length; e++) n[e] = t[e];
          return n;
        }
      })(t) ||
      (function (t) {
        if (Symbol.iterator in Object(t) || '[object Arguments]' === Object.prototype.toString.call(t)) return Array.from(t);
      })(t) ||
      (function () {
        throw new TypeError('Invalid attempt to spread non-iterable instance');
      })()
    );
  }
  function a(t) {
    if ('undefined' != typeof window && window.navigator) return !!navigator.userAgent.match(t);
  }
  var l = a(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),
    s = a(/Edge/i),
    c = a(/firefox/i),
    u = a(/safari/i) && !a(/chrome/i) && !a(/android/i),
    d = a(/iP(ad|od|hone)/i),
    h = a(/chrome/i) && a(/android/i),
    f = {capture: !1, passive: !1};
  function p(t, e, n) {
    t.addEventListener(e, n, !l && f);
  }
  function g(t, e, n) {
    t.removeEventListener(e, n, !l && f);
  }
  function v(t, e) {
    if (e) {
      if (('>' === e[0] && (e = e.substring(1)), t))
        try {
          if (t.matches) return t.matches(e);
          if (t.msMatchesSelector) return t.msMatchesSelector(e);
          if (t.webkitMatchesSelector) return t.webkitMatchesSelector(e);
        } catch (t) {
          return !1;
        }
      return !1;
    }
  }
  function m(t) {
    return t.host && t !== document && t.host.nodeType ? t.host : t.parentNode;
  }
  function b(t, e, n, o) {
    if (t) {
      n = n || document;
      do {
        if ((null != e && ('>' === e[0] ? t.parentNode === n && v(t, e) : v(t, e))) || (o && t === n)) return t;
        if (t === n) break;
      } while ((t = m(t)));
    }
    return null;
  }
  var y,
    w = /\s+/g;
  function E(t, e, n) {
    if (t && e)
      if (t.classList) t.classList[n ? 'add' : 'remove'](e);
      else {
        var o = (' ' + t.className + ' ').replace(w, ' ').replace(' ' + e + ' ', ' ');
        t.className = (o + (n ? ' ' + e : '')).replace(w, ' ');
      }
  }
  function D(t, e, n) {
    var o = t && t.style;
    if (o) {
      if (void 0 === n)
        return (
          document.defaultView && document.defaultView.getComputedStyle
            ? (n = document.defaultView.getComputedStyle(t, ''))
            : t.currentStyle && (n = t.currentStyle),
          void 0 === e ? n : n[e]
        );
      e in o || -1 !== e.indexOf('webkit') || (e = '-webkit-' + e), (o[e] = n + ('string' == typeof n ? '' : 'px'));
    }
  }
  function S(t, e) {
    var n = '';
    if ('string' == typeof t) n = t;
    else
      do {
        var o = D(t, 'transform');
        o && 'none' !== o && (n = o + ' ' + n);
      } while (!e && (t = t.parentNode));
    var i = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;
    return i && new i(n);
  }
  function _(t, e, n) {
    if (t) {
      var o = t.getElementsByTagName(e),
        i = 0,
        r = o.length;
      if (n) for (; i < r; i++) n(o[i], i);
      return o;
    }
    return [];
  }
  function C() {
    var t = document.scrollingElement;
    return t || document.documentElement;
  }
  function T(t, e, n, o, i) {
    if (t.getBoundingClientRect || t === window) {
      var r, a, s, c, u, d, h;
      if (
        (t !== window && t !== C()
          ? ((a = (r = t.getBoundingClientRect()).top), (s = r.left), (c = r.bottom), (u = r.right), (d = r.height), (h = r.width))
          : ((a = 0), (s = 0), (c = window.innerHeight), (u = window.innerWidth), (d = window.innerHeight), (h = window.innerWidth)),
        (e || n) && t !== window && ((i = i || t.parentNode), !l))
      )
        do {
          if (i && i.getBoundingClientRect && ('none' !== D(i, 'transform') || (n && 'static' !== D(i, 'position')))) {
            var f = i.getBoundingClientRect();
            (a -= f.top + parseInt(D(i, 'border-top-width'))), (s -= f.left + parseInt(D(i, 'border-left-width'))), (c = a + r.height), (u = s + r.width);
            break;
          }
        } while ((i = i.parentNode));
      if (o && t !== window) {
        var p = S(i || t),
          g = p && p.a,
          v = p && p.d;
        p && ((c = (a /= v) + (d /= v)), (u = (s /= g) + (h /= g)));
      }
      return {top: a, left: s, bottom: c, right: u, width: h, height: d};
    }
  }
  function x(t, e, n) {
    for (var o = I(t, !0), i = T(t)[e]; o; ) {
      var r = T(o)[n];
      if (!('top' === n || 'left' === n ? i >= r : i <= r)) return o;
      if (o === C()) break;
      o = I(o, !1);
    }
    return !1;
  }
  function M(t, e, n) {
    for (var o = 0, i = 0, r = t.children; i < r.length; ) {
      if ('none' !== r[i].style.display && r[i] !== Yt.ghost && r[i] !== Yt.dragged && b(r[i], n.draggable, t, !1)) {
        if (o === e) return r[i];
        o++;
      }
      i++;
    }
    return null;
  }
  function O(t, e) {
    for (var n = t.lastElementChild; n && (n === Yt.ghost || 'none' === D(n, 'display') || (e && !v(n, e))); ) n = n.previousElementSibling;
    return n || null;
  }
  function A(t, e) {
    var n = 0;
    if (!t || !t.parentNode) return -1;
    for (; (t = t.previousElementSibling); ) 'TEMPLATE' === t.nodeName.toUpperCase() || t === Yt.clone || (e && !v(t, e)) || n++;
    return n;
  }
  function N(t) {
    var e = 0,
      n = 0,
      o = C();
    if (t)
      do {
        var i = S(t),
          r = i.a,
          a = i.d;
        (e += t.scrollLeft * r), (n += t.scrollTop * a);
      } while (t !== o && (t = t.parentNode));
    return [e, n];
  }
  function I(t, e) {
    if (!t || !t.getBoundingClientRect) return C();
    var n = t,
      o = !1;
    do {
      if (n.clientWidth < n.scrollWidth || n.clientHeight < n.scrollHeight) {
        var i = D(n);
        if (
          (n.clientWidth < n.scrollWidth && ('auto' == i.overflowX || 'scroll' == i.overflowX)) ||
          (n.clientHeight < n.scrollHeight && ('auto' == i.overflowY || 'scroll' == i.overflowY))
        ) {
          if (!n.getBoundingClientRect || n === document.body) return C();
          if (o || e) return n;
          o = !0;
        }
      }
    } while ((n = n.parentNode));
    return C();
  }
  function P(t, e) {
    return (
      Math.round(t.top) === Math.round(e.top) &&
      Math.round(t.left) === Math.round(e.left) &&
      Math.round(t.height) === Math.round(e.height) &&
      Math.round(t.width) === Math.round(e.width)
    );
  }
  function k(t, e) {
    return function () {
      if (!y) {
        var n = arguments;
        1 === n.length ? t.call(this, n[0]) : t.apply(this, n),
          (y = setTimeout(function () {
            y = void 0;
          }, e));
      }
    };
  }
  function R(t, e, n) {
    (t.scrollLeft += e), (t.scrollTop += n);
  }
  function X(t) {
    var e = window.Polymer,
      n = window.jQuery || window.Zepto;
    return e && e.dom ? e.dom(t).cloneNode(!0) : n ? n(t).clone(!0)[0] : t.cloneNode(!0);
  }
  function Y(t, e) {
    D(t, 'position', 'absolute'), D(t, 'top', e.top), D(t, 'left', e.left), D(t, 'width', e.width), D(t, 'height', e.height);
  }
  function B(t) {
    D(t, 'position', ''), D(t, 'top', ''), D(t, 'left', ''), D(t, 'width', ''), D(t, 'height', '');
  }
  var F = 'Sortable' + new Date().getTime();
  function H() {
    var t,
      e = [];
    return {
      captureAnimationState: function () {
        ((e = []), this.options.animation) &&
          [].slice.call(this.el.children).forEach(function (t) {
            if ('none' !== D(t, 'display') && t !== Yt.ghost) {
              e.push({target: t, rect: T(t)});
              var n = o({}, e[e.length - 1].rect);
              if (t.thisAnimationDuration) {
                var i = S(t, !0);
                i && ((n.top -= i.f), (n.left -= i.e));
              }
              t.fromRect = n;
            }
          });
      },
      addAnimationState: function (t) {
        e.push(t);
      },
      removeAnimationState: function (t) {
        e.splice(
          (function (t, e) {
            for (var n in t) if (t.hasOwnProperty(n)) for (var o in e) if (e.hasOwnProperty(o) && e[o] === t[n][o]) return Number(n);
            return -1;
          })(e, {target: t}),
          1,
        );
      },
      animateAll: function (n) {
        var o = this;
        if (!this.options.animation) return clearTimeout(t), void ('function' == typeof n && n());
        var i = !1,
          r = 0;
        e.forEach(function (t) {
          var e = 0,
            n = t.target,
            a = n.fromRect,
            l = T(n),
            s = n.prevFromRect,
            c = n.prevToRect,
            u = t.rect,
            d = S(n, !0);
          d && ((l.top -= d.f), (l.left -= d.e)),
            (n.toRect = l),
            n.thisAnimationDuration &&
              P(s, l) &&
              !P(a, l) &&
              (u.top - l.top) / (u.left - l.left) == (a.top - l.top) / (a.left - l.left) &&
              (e = (function (t, e, n, o) {
                return (
                  (Math.sqrt(Math.pow(e.top - t.top, 2) + Math.pow(e.left - t.left, 2)) /
                    Math.sqrt(Math.pow(e.top - n.top, 2) + Math.pow(e.left - n.left, 2))) *
                  o.animation
                );
              })(u, s, c, o.options)),
            P(l, a) || ((n.prevFromRect = a), (n.prevToRect = l), e || (e = o.options.animation), o.animate(n, u, l, e)),
            e &&
              ((i = !0),
              (r = Math.max(r, e)),
              clearTimeout(n.animationResetTimer),
              (n.animationResetTimer = setTimeout(function () {
                (n.animationTime = 0), (n.prevFromRect = null), (n.fromRect = null), (n.prevToRect = null), (n.thisAnimationDuration = null);
              }, e)),
              (n.thisAnimationDuration = e));
        }),
          clearTimeout(t),
          i
            ? (t = setTimeout(function () {
                'function' == typeof n && n();
              }, r))
            : 'function' == typeof n && n(),
          (e = []);
      },
      animate: function (t, e, n, o) {
        if (o) {
          D(t, 'transition', ''), D(t, 'transform', '');
          var i = S(this.el),
            r = i && i.a,
            a = i && i.d,
            l = (e.left - n.left) / (r || 1),
            s = (e.top - n.top) / (a || 1);
          (t.animatingX = !!l),
            (t.animatingY = !!s),
            D(t, 'transform', 'translate3d(' + l + 'px,' + s + 'px,0)'),
            (function (t) {
              t.offsetWidth;
            })(t),
            D(t, 'transition', 'transform ' + o + 'ms' + (this.options.easing ? ' ' + this.options.easing : '')),
            D(t, 'transform', 'translate3d(0,0,0)'),
            'number' == typeof t.animated && clearTimeout(t.animated),
            (t.animated = setTimeout(function () {
              D(t, 'transition', ''), D(t, 'transform', ''), (t.animated = !1), (t.animatingX = !1), (t.animatingY = !1);
            }, o));
        }
      },
    };
  }
  var L = [],
    j = {initializeByDefault: !0},
    K = {
      mount: function (t) {
        for (var e in j) !j.hasOwnProperty(e) || e in t || (t[e] = j[e]);
        L.push(t);
      },
      pluginEvent: function (t, e, n) {
        var i = this;
        (this.eventCanceled = !1),
          (n.cancel = function () {
            i.eventCanceled = !0;
          });
        var r = t + 'Global';
        L.forEach(function (i) {
          e[i.pluginName] &&
            (e[i.pluginName][r] && e[i.pluginName][r](o({sortable: e}, n)),
            e.options[i.pluginName] && e[i.pluginName][t] && e[i.pluginName][t](o({sortable: e}, n)));
        });
      },
      initializePlugins: function (t, e, o, i) {
        for (var r in (L.forEach(function (i) {
          var r = i.pluginName;
          if (t.options[r] || i.initializeByDefault) {
            var a = new i(t, e, t.options);
            (a.sortable = t), (a.options = t.options), (t[r] = a), n(o, a.defaults);
          }
        }),
        t.options))
          if (t.options.hasOwnProperty(r)) {
            var a = this.modifyOption(t, r, t.options[r]);
            void 0 !== a && (t.options[r] = a);
          }
      },
      getEventProperties: function (t, e) {
        var o = {};
        return (
          L.forEach(function (i) {
            'function' == typeof i.eventProperties && n(o, i.eventProperties.call(e[i.pluginName], t));
          }),
          o
        );
      },
      modifyOption: function (t, e, n) {
        var o;
        return (
          L.forEach(function (i) {
            t[i.pluginName] && i.optionListeners && 'function' == typeof i.optionListeners[e] && (o = i.optionListeners[e].call(t[i.pluginName], n));
          }),
          o
        );
      },
    };
  function W(t) {
    var e = t.sortable,
      n = t.rootEl,
      i = t.name,
      r = t.targetEl,
      a = t.cloneEl,
      c = t.toEl,
      u = t.fromEl,
      d = t.oldIndex,
      h = t.newIndex,
      f = t.oldDraggableIndex,
      p = t.newDraggableIndex,
      g = t.originalEvent,
      v = t.putSortable,
      m = t.extraEventProperties;
    if ((e = e || (n && n[F]))) {
      var b,
        y = e.options,
        w = 'on' + i.charAt(0).toUpperCase() + i.substr(1);
      !window.CustomEvent || l || s ? (b = document.createEvent('Event')).initEvent(i, !0, !0) : (b = new CustomEvent(i, {bubbles: !0, cancelable: !0})),
        (b.to = c || n),
        (b.from = u || n),
        (b.item = r || n),
        (b.clone = a),
        (b.oldIndex = d),
        (b.newIndex = h),
        (b.oldDraggableIndex = f),
        (b.newDraggableIndex = p),
        (b.originalEvent = g),
        (b.pullMode = v ? v.lastPutMode : void 0);
      var E = o({}, m, K.getEventProperties(i, e));
      for (var D in E) b[D] = E[D];
      n && n.dispatchEvent(b), y[w] && y[w].call(e, b);
    }
  }
  var z = function (t, e) {
    var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
      r = n.evt,
      a = i(n, ['evt']);
    K.pluginEvent.bind(Yt)(
      t,
      e,
      o(
        {
          dragEl: U,
          parentEl: q,
          ghostEl: V,
          rootEl: Z,
          nextEl: Q,
          lastDownEl: $,
          cloneEl: J,
          cloneHidden: tt,
          dragStarted: ft,
          putSortable: at,
          activeSortable: Yt.active,
          originalEvent: r,
          oldIndex: et,
          oldDraggableIndex: ot,
          newIndex: nt,
          newDraggableIndex: it,
          hideGhostForTarget: Pt,
          unhideGhostForTarget: kt,
          cloneNowHidden: function () {
            tt = !0;
          },
          cloneNowShown: function () {
            tt = !1;
          },
          dispatchSortableEvent: function (t) {
            G({sortable: e, name: t, originalEvent: r});
          },
        },
        a,
      ),
    );
  };
  function G(t) {
    W(o({putSortable: at, cloneEl: J, targetEl: U, rootEl: Z, oldIndex: et, oldDraggableIndex: ot, newIndex: nt, newDraggableIndex: it}, t));
  }
  var U,
    q,
    V,
    Z,
    Q,
    $,
    J,
    tt,
    et,
    nt,
    ot,
    it,
    rt,
    at,
    lt,
    st,
    ct,
    ut,
    dt,
    ht,
    ft,
    pt,
    gt,
    vt,
    mt,
    bt = !1,
    yt = !1,
    wt = [],
    Et = !1,
    Dt = !1,
    St = [],
    _t = !1,
    Ct = [],
    Tt = 'undefined' != typeof document,
    xt = d,
    Mt = s || l ? 'cssFloat' : 'float',
    Ot = Tt && !h && !d && 'draggable' in document.createElement('div'),
    At = (function () {
      if (Tt) {
        if (l) return !1;
        var t = document.createElement('x');
        return (t.style.cssText = 'pointer-events:auto'), 'auto' === t.style.pointerEvents;
      }
    })(),
    Nt = function (t, e) {
      var n = D(t),
        o = parseInt(n.width) - parseInt(n.paddingLeft) - parseInt(n.paddingRight) - parseInt(n.borderLeftWidth) - parseInt(n.borderRightWidth),
        i = M(t, 0, e),
        r = M(t, 1, e),
        a = i && D(i),
        l = r && D(r),
        s = a && parseInt(a.marginLeft) + parseInt(a.marginRight) + T(i).width,
        c = l && parseInt(l.marginLeft) + parseInt(l.marginRight) + T(r).width;
      if ('flex' === n.display) return 'column' === n.flexDirection || 'column-reverse' === n.flexDirection ? 'vertical' : 'horizontal';
      if ('grid' === n.display) return n.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';
      if (i && a.float && 'none' !== a.float) {
        var u = 'left' === a.float ? 'left' : 'right';
        return !r || ('both' !== l.clear && l.clear !== u) ? 'horizontal' : 'vertical';
      }
      return i &&
        ('block' === a.display ||
          'flex' === a.display ||
          'table' === a.display ||
          'grid' === a.display ||
          (s >= o && 'none' === n[Mt]) ||
          (r && 'none' === n[Mt] && s + c > o))
        ? 'vertical'
        : 'horizontal';
    },
    It = function (e) {
      function n(t, e) {
        return function (o, i, r, a) {
          var l = o.options.group.name && i.options.group.name && o.options.group.name === i.options.group.name;
          if (null == t && (e || l)) return !0;
          if (null == t || !1 === t) return !1;
          if (e && 'clone' === t) return t;
          if ('function' == typeof t) return n(t(o, i, r, a), e)(o, i, r, a);
          var s = (e ? o : i).options.group.name;
          return !0 === t || ('string' == typeof t && t === s) || (t.join && t.indexOf(s) > -1);
        };
      }
      var o = {},
        i = e.group;
      (i && 'object' == t(i)) || (i = {name: i}),
        (o.name = i.name),
        (o.checkPull = n(i.pull, !0)),
        (o.checkPut = n(i.put)),
        (o.revertClone = i.revertClone),
        (e.group = o);
    },
    Pt = function () {
      !At && V && D(V, 'display', 'none');
    },
    kt = function () {
      !At && V && D(V, 'display', '');
    };
  Tt &&
    document.addEventListener(
      'click',
      function (t) {
        if (yt) return t.preventDefault(), t.stopPropagation && t.stopPropagation(), t.stopImmediatePropagation && t.stopImmediatePropagation(), (yt = !1), !1;
      },
      !0,
    );
  var Rt = function (t) {
      if (U) {
        t = t.touches ? t.touches[0] : t;
        var e =
          ((i = t.clientX),
          (r = t.clientY),
          wt.some(function (t) {
            if (!O(t)) {
              var e = T(t),
                n = t[F].options.emptyInsertThreshold,
                o = i >= e.left - n && i <= e.right + n,
                l = r >= e.top - n && r <= e.bottom + n;
              return n && o && l ? (a = t) : void 0;
            }
          }),
          a);
        if (e) {
          var n = {};
          for (var o in t) t.hasOwnProperty(o) && (n[o] = t[o]);
          (n.target = n.rootEl = e), (n.preventDefault = void 0), (n.stopPropagation = void 0), e[F]._onDragOver(n);
        }
      }
      var i, r, a;
    },
    Xt = function (t) {
      U && U.parentNode[F]._isOutsideThisEl(t.target);
    };
  function Yt(t, e) {
    if (!t || !t.nodeType || 1 !== t.nodeType) throw 'Sortable: `el` must be an HTMLElement, not '.concat({}.toString.call(t));
    (this.el = t), (this.options = e = n({}, e)), (t[F] = this);
    var o = {
      group: null,
      sort: !0,
      disabled: !1,
      store: null,
      handle: null,
      draggable: /^[uo]l$/i.test(t.nodeName) ? '>li' : '>*',
      swapThreshold: 1,
      invertSwap: !1,
      invertedSwapThreshold: null,
      removeCloneOnHide: !0,
      direction: function () {
        return Nt(t, this.options);
      },
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      ignore: 'a, img',
      filter: null,
      preventOnFilter: !0,
      animation: 0,
      easing: null,
      setData: function (t, e) {
        t.setData('Text', e.textContent);
      },
      dropBubble: !1,
      dragoverBubble: !1,
      dataIdAttr: 'data-id',
      delay: 0,
      delayOnTouchOnly: !1,
      touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,
      forceFallback: !1,
      fallbackClass: 'sortable-fallback',
      fallbackOnBody: !1,
      fallbackTolerance: 0,
      fallbackOffset: {x: 0, y: 0},
      supportPointer: !1 !== Yt.supportPointer && 'PointerEvent' in window,
      emptyInsertThreshold: 5,
    };
    for (var i in (K.initializePlugins(this, t, o), o)) !(i in e) && (e[i] = o[i]);
    for (var r in (It(e), this)) '_' === r.charAt(0) && 'function' == typeof this[r] && (this[r] = this[r].bind(this));
    (this.nativeDraggable = !e.forceFallback && Ot),
      this.nativeDraggable && (this.options.touchStartThreshold = 1),
      e.supportPointer ? p(t, 'pointerdown', this._onTapStart) : (p(t, 'mousedown', this._onTapStart), p(t, 'touchstart', this._onTapStart)),
      this.nativeDraggable && (p(t, 'dragover', this), p(t, 'dragenter', this)),
      wt.push(this.el),
      e.store && e.store.get && this.sort(e.store.get(this) || []),
      n(this, H());
  }
  function Bt(t, e, n, o, i, r, a, c) {
    var u,
      d,
      h = t[F],
      f = h.options.onMove;
    return (
      !window.CustomEvent || l || s
        ? (u = document.createEvent('Event')).initEvent('move', !0, !0)
        : (u = new CustomEvent('move', {bubbles: !0, cancelable: !0})),
      (u.to = e),
      (u.from = t),
      (u.dragged = n),
      (u.draggedRect = o),
      (u.related = i || e),
      (u.relatedRect = r || T(e)),
      (u.willInsertAfter = c),
      (u.originalEvent = a),
      t.dispatchEvent(u),
      f && (d = f.call(h, u, a)),
      d
    );
  }
  function Ft(t) {
    t.draggable = !1;
  }
  function Ht() {
    _t = !1;
  }
  function Lt(t) {
    for (var e = t.tagName + t.className + t.src + t.href + t.textContent, n = e.length, o = 0; n--; ) o += e.charCodeAt(n);
    return o.toString(36);
  }
  function jt(t) {
    return setTimeout(t, 0);
  }
  function Kt(t) {
    return clearTimeout(t);
  }
  (Yt.prototype = {
    constructor: Yt,
    _isOutsideThisEl: function (t) {
      this.el.contains(t) || t === this.el || (pt = null);
    },
    _getDirection: function (t, e) {
      return 'function' == typeof this.options.direction ? this.options.direction.call(this, t, e, U) : this.options.direction;
    },
    _onTapStart: function (t) {
      if (t.cancelable) {
        var e = this,
          n = this.el,
          o = this.options,
          i = o.preventOnFilter,
          r = t.type,
          a = (t.touches && t.touches[0]) || (t.pointerType && 'touch' === t.pointerType && t),
          l = (a || t).target,
          s = (t.target.shadowRoot && ((t.path && t.path[0]) || (t.composedPath && t.composedPath()[0]))) || l,
          c = o.filter;
        if (
          ((function (t) {
            Ct.length = 0;
            var e = t.getElementsByTagName('input'),
              n = e.length;
            for (; n--; ) {
              var o = e[n];
              o.checked && Ct.push(o);
            }
          })(n),
          !U &&
            !(
              (/mousedown|pointerdown/.test(r) && 0 !== t.button) ||
              o.disabled ||
              s.isContentEditable ||
              ((l = b(l, o.draggable, n, !1)) && l.animated) ||
              $ === l
            ))
        ) {
          if (((et = A(l)), (ot = A(l, o.draggable)), 'function' == typeof c)) {
            if (c.call(this, t, l, this))
              return (
                G({sortable: e, rootEl: s, name: 'filter', targetEl: l, toEl: n, fromEl: n}),
                z('filter', e, {evt: t}),
                void (i && t.cancelable && t.preventDefault())
              );
          } else if (
            c &&
            (c = c.split(',').some(function (o) {
              if ((o = b(s, o.trim(), n, !1)))
                return G({sortable: e, rootEl: o, name: 'filter', targetEl: l, fromEl: n, toEl: n}), z('filter', e, {evt: t}), !0;
            }))
          )
            return void (i && t.cancelable && t.preventDefault());
          (o.handle && !b(s, o.handle, n, !1)) || this._prepareDragStart(t, a, l);
        }
      }
    },
    _prepareDragStart: function (t, e, n) {
      var o,
        i = this,
        r = i.el,
        a = i.options,
        u = r.ownerDocument;
      if (n && !U && n.parentNode === r) {
        var d = T(n);
        if (
          ((Z = r),
          (q = (U = n).parentNode),
          (Q = U.nextSibling),
          ($ = n),
          (rt = a.group),
          (Yt.dragged = U),
          (lt = {target: U, clientX: (e || t).clientX, clientY: (e || t).clientY}),
          (dt = lt.clientX - d.left),
          (ht = lt.clientY - d.top),
          (this._lastX = (e || t).clientX),
          (this._lastY = (e || t).clientY),
          (U.style['will-change'] = 'all'),
          (o = function () {
            z('delayEnded', i, {evt: t}),
              Yt.eventCanceled
                ? i._onDrop()
                : (i._disableDelayedDragEvents(),
                  !c && i.nativeDraggable && (U.draggable = !0),
                  i._triggerDragStart(t, e),
                  G({sortable: i, name: 'choose', originalEvent: t}),
                  E(U, a.chosenClass, !0));
          }),
          a.ignore.split(',').forEach(function (t) {
            _(U, t.trim(), Ft);
          }),
          p(u, 'dragover', Rt),
          p(u, 'mousemove', Rt),
          p(u, 'touchmove', Rt),
          p(u, 'mouseup', i._onDrop),
          p(u, 'touchend', i._onDrop),
          p(u, 'touchcancel', i._onDrop),
          c && this.nativeDraggable && ((this.options.touchStartThreshold = 4), (U.draggable = !0)),
          z('delayStart', this, {evt: t}),
          !a.delay || (a.delayOnTouchOnly && !e) || (this.nativeDraggable && (s || l)))
        )
          o();
        else {
          if (Yt.eventCanceled) return void this._onDrop();
          p(u, 'mouseup', i._disableDelayedDrag),
            p(u, 'touchend', i._disableDelayedDrag),
            p(u, 'touchcancel', i._disableDelayedDrag),
            p(u, 'mousemove', i._delayedDragTouchMoveHandler),
            p(u, 'touchmove', i._delayedDragTouchMoveHandler),
            a.supportPointer && p(u, 'pointermove', i._delayedDragTouchMoveHandler),
            (i._dragStartTimer = setTimeout(o, a.delay));
        }
      }
    },
    _delayedDragTouchMoveHandler: function (t) {
      var e = t.touches ? t.touches[0] : t;
      Math.max(Math.abs(e.clientX - this._lastX), Math.abs(e.clientY - this._lastY)) >=
        Math.floor(this.options.touchStartThreshold / ((this.nativeDraggable && window.devicePixelRatio) || 1)) && this._disableDelayedDrag();
    },
    _disableDelayedDrag: function () {
      U && Ft(U), clearTimeout(this._dragStartTimer), this._disableDelayedDragEvents();
    },
    _disableDelayedDragEvents: function () {
      var t = this.el.ownerDocument;
      g(t, 'mouseup', this._disableDelayedDrag),
        g(t, 'touchend', this._disableDelayedDrag),
        g(t, 'touchcancel', this._disableDelayedDrag),
        g(t, 'mousemove', this._delayedDragTouchMoveHandler),
        g(t, 'touchmove', this._delayedDragTouchMoveHandler),
        g(t, 'pointermove', this._delayedDragTouchMoveHandler);
    },
    _triggerDragStart: function (t, e) {
      (e = e || ('touch' == t.pointerType && t)),
        !this.nativeDraggable || e
          ? this.options.supportPointer
            ? p(document, 'pointermove', this._onTouchMove)
            : p(document, e ? 'touchmove' : 'mousemove', this._onTouchMove)
          : (p(U, 'dragend', this), p(Z, 'dragstart', this._onDragStart));
      try {
        document.selection
          ? jt(function () {
              document.selection.empty();
            })
          : window.getSelection().removeAllRanges();
      } catch (t) {}
    },
    _dragStarted: function (t, e) {
      if (((bt = !1), Z && U)) {
        z('dragStarted', this, {evt: e}), this.nativeDraggable && p(document, 'dragover', Xt);
        var n = this.options;
        !t && E(U, n.dragClass, !1), E(U, n.ghostClass, !0), (Yt.active = this), t && this._appendGhost(), G({sortable: this, name: 'start', originalEvent: e});
      } else this._nulling();
    },
    _emulateDragOver: function () {
      if (st) {
        (this._lastX = st.clientX), (this._lastY = st.clientY), Pt();
        for (
          var t = document.elementFromPoint(st.clientX, st.clientY), e = t;
          t && t.shadowRoot && (t = t.shadowRoot.elementFromPoint(st.clientX, st.clientY)) !== e;

        )
          e = t;
        if ((U.parentNode[F]._isOutsideThisEl(t), e))
          do {
            if (e[F]) {
              if (e[F]._onDragOver({clientX: st.clientX, clientY: st.clientY, target: t, rootEl: e}) && !this.options.dragoverBubble) break;
            }
            t = e;
          } while ((e = e.parentNode));
        kt();
      }
    },
    _onTouchMove: function (t) {
      if (lt) {
        var e = this.options,
          n = e.fallbackTolerance,
          o = e.fallbackOffset,
          i = t.touches ? t.touches[0] : t,
          r = V && S(V, !0),
          a = V && r && r.a,
          l = V && r && r.d,
          s = xt && mt && N(mt),
          c = (i.clientX - lt.clientX + o.x) / (a || 1) + (s ? s[0] - St[0] : 0) / (a || 1),
          u = (i.clientY - lt.clientY + o.y) / (l || 1) + (s ? s[1] - St[1] : 0) / (l || 1);
        if (!Yt.active && !bt) {
          if (n && Math.max(Math.abs(i.clientX - this._lastX), Math.abs(i.clientY - this._lastY)) < n) return;
          this._onDragStart(t, !0);
        }
        if (V) {
          r ? ((r.e += c - (ct || 0)), (r.f += u - (ut || 0))) : (r = {a: 1, b: 0, c: 0, d: 1, e: c, f: u});
          var d = 'matrix('.concat(r.a, ',').concat(r.b, ',').concat(r.c, ',').concat(r.d, ',').concat(r.e, ',').concat(r.f, ')');
          D(V, 'webkitTransform', d), D(V, 'mozTransform', d), D(V, 'msTransform', d), D(V, 'transform', d), (ct = c), (ut = u), (st = i);
        }
        t.cancelable && t.preventDefault();
      }
    },
    _appendGhost: function () {
      if (!V) {
        var t = this.options.fallbackOnBody ? document.body : Z,
          e = T(U, !0, xt, !0, t),
          n = this.options;
        if (xt) {
          for (mt = t; 'static' === D(mt, 'position') && 'none' === D(mt, 'transform') && mt !== document; ) mt = mt.parentNode;
          mt !== document.body && mt !== document.documentElement
            ? (mt === document && (mt = C()), (e.top += mt.scrollTop), (e.left += mt.scrollLeft))
            : (mt = C()),
            (St = N(mt));
        }
        E((V = U.cloneNode(!0)), n.ghostClass, !1),
          E(V, n.fallbackClass, !0),
          E(V, n.dragClass, !0),
          D(V, 'transition', ''),
          D(V, 'transform', ''),
          D(V, 'box-sizing', 'border-box'),
          D(V, 'margin', 0),
          D(V, 'top', e.top),
          D(V, 'left', e.left),
          D(V, 'width', e.width),
          D(V, 'height', e.height),
          D(V, 'opacity', '0.8'),
          D(V, 'position', xt ? 'absolute' : 'fixed'),
          D(V, 'zIndex', '100000'),
          D(V, 'pointerEvents', 'none'),
          (Yt.ghost = V),
          t.appendChild(V),
          D(V, 'transform-origin', (dt / parseInt(V.style.width)) * 100 + '% ' + (ht / parseInt(V.style.height)) * 100 + '%');
      }
    },
    _onDragStart: function (t, e) {
      var n = this,
        o = t.dataTransfer,
        i = n.options;
      z('dragStart', this, {evt: t}),
        Yt.eventCanceled
          ? this._onDrop()
          : (z('setupClone', this),
            Yt.eventCanceled ||
              (((J = X(U)).draggable = !1), (J.style['will-change'] = ''), this._hideClone(), E(J, this.options.chosenClass, !1), (Yt.clone = J)),
            (n.cloneId = jt(function () {
              z('clone', n), Yt.eventCanceled || (n.options.removeCloneOnHide || Z.insertBefore(J, U), n._hideClone(), G({sortable: n, name: 'clone'}));
            })),
            !e && E(U, i.dragClass, !0),
            e
              ? ((yt = !0), (n._loopId = setInterval(n._emulateDragOver, 50)))
              : (g(document, 'mouseup', n._onDrop),
                g(document, 'touchend', n._onDrop),
                g(document, 'touchcancel', n._onDrop),
                o && ((o.effectAllowed = 'move'), i.setData && i.setData.call(n, o, U)),
                p(document, 'drop', n),
                D(U, 'transform', 'translateZ(0)')),
            (bt = !0),
            (n._dragStartId = jt(n._dragStarted.bind(n, e, t))),
            p(document, 'selectstart', n),
            (ft = !0),
            u && D(document.body, 'user-select', 'none'));
    },
    _onDragOver: function (t) {
      var e,
        n,
        i,
        r,
        a = this.el,
        l = t.target,
        s = this.options,
        c = s.group,
        u = Yt.active,
        d = rt === c,
        h = s.sort,
        f = at || u,
        p = this,
        g = !1;
      if (!_t) {
        if ((void 0 !== t.preventDefault && t.cancelable && t.preventDefault(), (l = b(l, s.draggable, a, !0)), Y('dragOver'), Yt.eventCanceled)) return g;
        if (U.contains(t.target) || (l.animated && l.animatingX && l.animatingY) || p._ignoreWhileAnimating === l) return H(!1);
        if (
          ((yt = !1),
          u && !s.disabled && (d ? h || (i = !Z.contains(U)) : at === this || ((this.lastPutMode = rt.checkPull(this, u, U, t)) && c.checkPut(this, u, U, t))))
        ) {
          if (((r = 'vertical' === this._getDirection(t, l)), (e = T(U)), Y('dragOverValid'), Yt.eventCanceled)) return g;
          if (i) return (q = Z), B(), this._hideClone(), Y('revert'), Yt.eventCanceled || (Q ? Z.insertBefore(U, Q) : Z.appendChild(U)), H(!0);
          var v = O(a, s.draggable);
          if (
            !v ||
            ((function (t, e, n) {
              var o = T(O(n.el, n.options.draggable));
              return e
                ? t.clientX > o.right + 10 || (t.clientX <= o.right && t.clientY > o.bottom && t.clientX >= o.left)
                : (t.clientX > o.right && t.clientY > o.top) || (t.clientX <= o.right && t.clientY > o.bottom + 10);
            })(t, r, this) &&
              !v.animated)
          ) {
            if (v === U) return H(!1);
            if ((v && a === t.target && (l = v), l && (n = T(l)), !1 !== Bt(Z, a, U, e, l, n, t, !!l))) return B(), a.appendChild(U), (q = a), L(), H(!0);
          } else if (l.parentNode === a) {
            n = T(l);
            var m,
              y,
              w,
              S = U.parentNode !== a,
              _ = !(function (t, e, n) {
                var o = n ? t.left : t.top,
                  i = n ? t.right : t.bottom,
                  r = n ? t.width : t.height,
                  a = n ? e.left : e.top,
                  l = n ? e.right : e.bottom,
                  s = n ? e.width : e.height;
                return o === a || i === l || o + r / 2 === a + s / 2;
              })((U.animated && U.toRect) || e, (l.animated && l.toRect) || n, r),
              C = r ? 'top' : 'left',
              M = x(l, 'top', 'top') || x(U, 'top', 'top'),
              N = M ? M.scrollTop : void 0;
            if (
              (pt !== l && ((y = n[C]), (Et = !1), (Dt = (!_ && s.invertSwap) || S)),
              0 !==
                (m = (function (t, e, n, o, i, r, a, l) {
                  var s = o ? t.clientY : t.clientX,
                    c = o ? n.height : n.width,
                    u = o ? n.top : n.left,
                    d = o ? n.bottom : n.right,
                    h = !1;
                  if (!a)
                    if (l && vt < c * i) {
                      if ((!Et && (1 === gt ? s > u + (c * r) / 2 : s < d - (c * r) / 2) && (Et = !0), Et)) h = !0;
                      else if (1 === gt ? s < u + vt : s > d - vt) return -gt;
                    } else if (s > u + (c * (1 - i)) / 2 && s < d - (c * (1 - i)) / 2)
                      return (function (t) {
                        return A(U) < A(t) ? 1 : -1;
                      })(e);
                  if ((h = h || a) && (s < u + (c * r) / 2 || s > d - (c * r) / 2)) return s > u + c / 2 ? 1 : -1;
                  return 0;
                })(t, l, n, r, _ ? 1 : s.swapThreshold, null == s.invertedSwapThreshold ? s.swapThreshold : s.invertedSwapThreshold, Dt, pt === l)))
            ) {
              var I = A(U);
              do {
                (I -= m), (w = q.children[I]);
              } while (w && ('none' === D(w, 'display') || w === V));
            }
            if (0 === m || w === l) return H(!1);
            (pt = l), (gt = m);
            var P = l.nextElementSibling,
              k = !1,
              X = Bt(Z, a, U, e, l, n, t, (k = 1 === m));
            if (!1 !== X)
              return (
                (1 !== X && -1 !== X) || (k = 1 === X),
                (_t = !0),
                setTimeout(Ht, 30),
                B(),
                k && !P ? a.appendChild(U) : l.parentNode.insertBefore(U, k ? P : l),
                M && R(M, 0, N - M.scrollTop),
                (q = U.parentNode),
                void 0 === y || Dt || (vt = Math.abs(y - T(l)[C])),
                L(),
                H(!0)
              );
          }
          if (a.contains(U)) return H(!1);
        }
        return !1;
      }
      function Y(s, c) {
        z(
          s,
          p,
          o(
            {
              evt: t,
              isOwner: d,
              axis: r ? 'vertical' : 'horizontal',
              revert: i,
              dragRect: e,
              targetRect: n,
              canSort: h,
              fromSortable: f,
              target: l,
              completed: H,
              onMove: function (n, o) {
                return Bt(Z, a, U, e, n, T(n), t, o);
              },
              changed: L,
            },
            c,
          ),
        );
      }
      function B() {
        Y('dragOverAnimationCapture'), p.captureAnimationState(), p !== f && f.captureAnimationState();
      }
      function H(e) {
        return (
          Y('dragOverCompleted', {insertion: e}),
          e &&
            (d ? u._hideClone() : u._showClone(p),
            p !== f && (E(U, at ? at.options.ghostClass : u.options.ghostClass, !1), E(U, s.ghostClass, !0)),
            at !== p && p !== Yt.active ? (at = p) : p === Yt.active && at && (at = null),
            f === p && (p._ignoreWhileAnimating = l),
            p.animateAll(function () {
              Y('dragOverAnimationComplete'), (p._ignoreWhileAnimating = null);
            }),
            p !== f && (f.animateAll(), (f._ignoreWhileAnimating = null))),
          ((l === U && !U.animated) || (l === a && !l.animated)) && (pt = null),
          s.dragoverBubble || t.rootEl || l === document || (U.parentNode[F]._isOutsideThisEl(t.target), !e && Rt(t)),
          !s.dragoverBubble && t.stopPropagation && t.stopPropagation(),
          (g = !0)
        );
      }
      function L() {
        (nt = A(U)), (it = A(U, s.draggable)), G({sortable: p, name: 'change', toEl: a, newIndex: nt, newDraggableIndex: it, originalEvent: t});
      }
    },
    _ignoreWhileAnimating: null,
    _offMoveEvents: function () {
      g(document, 'mousemove', this._onTouchMove),
        g(document, 'touchmove', this._onTouchMove),
        g(document, 'pointermove', this._onTouchMove),
        g(document, 'dragover', Rt),
        g(document, 'mousemove', Rt),
        g(document, 'touchmove', Rt);
    },
    _offUpEvents: function () {
      var t = this.el.ownerDocument;
      g(t, 'mouseup', this._onDrop),
        g(t, 'touchend', this._onDrop),
        g(t, 'pointerup', this._onDrop),
        g(t, 'touchcancel', this._onDrop),
        g(document, 'selectstart', this);
    },
    _onDrop: function (t) {
      var e = this.el,
        n = this.options;
      (nt = A(U)),
        (it = A(U, n.draggable)),
        z('drop', this, {evt: t}),
        (q = U && U.parentNode),
        (nt = A(U)),
        (it = A(U, n.draggable)),
        Yt.eventCanceled
          ? this._nulling()
          : ((bt = !1),
            (Dt = !1),
            (Et = !1),
            clearInterval(this._loopId),
            clearTimeout(this._dragStartTimer),
            Kt(this.cloneId),
            Kt(this._dragStartId),
            this.nativeDraggable && (g(document, 'drop', this), g(e, 'dragstart', this._onDragStart)),
            this._offMoveEvents(),
            this._offUpEvents(),
            u && D(document.body, 'user-select', ''),
            D(U, 'transform', ''),
            t &&
              (ft && (t.cancelable && t.preventDefault(), !n.dropBubble && t.stopPropagation()),
              V && V.parentNode && V.parentNode.removeChild(V),
              (Z === q || (at && 'clone' !== at.lastPutMode)) && J && J.parentNode && J.parentNode.removeChild(J),
              U &&
                (this.nativeDraggable && g(U, 'dragend', this),
                Ft(U),
                (U.style['will-change'] = ''),
                ft && !bt && E(U, at ? at.options.ghostClass : this.options.ghostClass, !1),
                E(U, this.options.chosenClass, !1),
                G({sortable: this, name: 'unchoose', toEl: q, newIndex: null, newDraggableIndex: null, originalEvent: t}),
                Z !== q
                  ? (nt >= 0 &&
                      (G({rootEl: q, name: 'add', toEl: q, fromEl: Z, originalEvent: t}),
                      G({sortable: this, name: 'remove', toEl: q, originalEvent: t}),
                      G({rootEl: q, name: 'sort', toEl: q, fromEl: Z, originalEvent: t}),
                      G({sortable: this, name: 'sort', toEl: q, originalEvent: t})),
                    at && at.save())
                  : nt !== et &&
                    nt >= 0 &&
                    (G({sortable: this, name: 'update', toEl: q, originalEvent: t}), G({sortable: this, name: 'sort', toEl: q, originalEvent: t})),
                Yt.active && ((null != nt && -1 !== nt) || ((nt = et), (it = ot)), G({sortable: this, name: 'end', toEl: q, originalEvent: t}), this.save()))),
            this._nulling());
    },
    _nulling: function () {
      z('nulling', this),
        (Z = U = q = V = Q = J = $ = tt = lt = st = ft = nt = it = et = ot = pt = gt = at = rt = Yt.dragged = Yt.ghost = Yt.clone = Yt.active = null),
        Ct.forEach(function (t) {
          t.checked = !0;
        }),
        (Ct.length = ct = ut = 0);
    },
    handleEvent: function (t) {
      switch (t.type) {
        case 'drop':
        case 'dragend':
          this._onDrop(t);
          break;
        case 'dragenter':
        case 'dragover':
          U &&
            (this._onDragOver(t),
            (function (t) {
              t.dataTransfer && (t.dataTransfer.dropEffect = 'move');
              t.cancelable && t.preventDefault();
            })(t));
          break;
        case 'selectstart':
          t.preventDefault();
      }
    },
    toArray: function () {
      for (var t, e = [], n = this.el.children, o = 0, i = n.length, r = this.options; o < i; o++)
        b((t = n[o]), r.draggable, this.el, !1) && e.push(t.getAttribute(r.dataIdAttr) || Lt(t));
      return e;
    },
    sort: function (t) {
      var e = {},
        n = this.el;
      this.toArray().forEach(function (t, o) {
        var i = n.children[o];
        b(i, this.options.draggable, n, !1) && (e[t] = i);
      }, this),
        t.forEach(function (t) {
          e[t] && (n.removeChild(e[t]), n.appendChild(e[t]));
        });
    },
    save: function () {
      var t = this.options.store;
      t && t.set && t.set(this);
    },
    closest: function (t, e) {
      return b(t, e || this.options.draggable, this.el, !1);
    },
    option: function (t, e) {
      var n = this.options;
      if (void 0 === e) return n[t];
      var o = K.modifyOption(this, t, e);
      (n[t] = void 0 !== o ? o : e), 'group' === t && It(n);
    },
    destroy: function () {
      z('destroy', this);
      var t = this.el;
      (t[F] = null),
        g(t, 'mousedown', this._onTapStart),
        g(t, 'touchstart', this._onTapStart),
        g(t, 'pointerdown', this._onTapStart),
        this.nativeDraggable && (g(t, 'dragover', this), g(t, 'dragenter', this)),
        Array.prototype.forEach.call(t.querySelectorAll('[draggable]'), function (t) {
          t.removeAttribute('draggable');
        }),
        this._onDrop(),
        this._disableDelayedDragEvents(),
        wt.splice(wt.indexOf(this.el), 1),
        (this.el = t = null);
    },
    _hideClone: function () {
      if (!tt) {
        if ((z('hideClone', this), Yt.eventCanceled)) return;
        D(J, 'display', 'none'), this.options.removeCloneOnHide && J.parentNode && J.parentNode.removeChild(J), (tt = !0);
      }
    },
    _showClone: function (t) {
      if ('clone' === t.lastPutMode) {
        if (tt) {
          if ((z('showClone', this), Yt.eventCanceled)) return;
          Z.contains(U) && !this.options.group.revertClone ? Z.insertBefore(J, U) : Q ? Z.insertBefore(J, Q) : Z.appendChild(J),
            this.options.group.revertClone && this.animate(U, J),
            D(J, 'display', ''),
            (tt = !1);
        }
      } else this._hideClone();
    },
  }),
    Tt &&
      p(document, 'touchmove', function (t) {
        (Yt.active || bt) && t.cancelable && t.preventDefault();
      }),
    (Yt.utils = {
      on: p,
      off: g,
      css: D,
      find: _,
      is: function (t, e) {
        return !!b(t, e, t, !1);
      },
      extend: function (t, e) {
        if (t && e) for (var n in e) e.hasOwnProperty(n) && (t[n] = e[n]);
        return t;
      },
      throttle: k,
      closest: b,
      toggleClass: E,
      clone: X,
      index: A,
      nextTick: jt,
      cancelNextTick: Kt,
      detectDirection: Nt,
      getChild: M,
    }),
    (Yt.get = function (t) {
      return t[F];
    }),
    (Yt.mount = function () {
      for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++) e[n] = arguments[n];
      e[0].constructor === Array && (e = e[0]),
        e.forEach(function (t) {
          if (!t.prototype || !t.prototype.constructor) throw 'Sortable: Mounted plugin must be a constructor function, not '.concat({}.toString.call(t));
          t.utils && (Yt.utils = o({}, Yt.utils, t.utils)), K.mount(t);
        });
    }),
    (Yt.create = function (t, e) {
      return new Yt(t, e);
    }),
    (Yt.version = '1.10.2');
  var Wt,
    zt,
    Gt,
    Ut,
    qt,
    Vt,
    Zt = [],
    Qt = !1;
  function $t() {
    Zt.forEach(function (t) {
      clearInterval(t.pid);
    }),
      (Zt = []);
  }
  function Jt() {
    clearInterval(Vt);
  }
  var te,
    ee = k(function (t, e, n, o) {
      if (e.scroll) {
        var i,
          r = (t.touches ? t.touches[0] : t).clientX,
          a = (t.touches ? t.touches[0] : t).clientY,
          l = e.scrollSensitivity,
          s = e.scrollSpeed,
          c = C(),
          u = !1;
        zt !== n && ((zt = n), $t(), (Wt = e.scroll), (i = e.scrollFn), !0 === Wt && (Wt = I(n, !0)));
        var d = 0,
          h = Wt;
        do {
          var f = h,
            p = T(f),
            g = p.top,
            v = p.bottom,
            m = p.left,
            b = p.right,
            y = p.width,
            w = p.height,
            E = void 0,
            S = void 0,
            _ = f.scrollWidth,
            x = f.scrollHeight,
            M = D(f),
            O = f.scrollLeft,
            A = f.scrollTop;
          f === c
            ? ((E = y < _ && ('auto' === M.overflowX || 'scroll' === M.overflowX || 'visible' === M.overflowX)),
              (S = w < x && ('auto' === M.overflowY || 'scroll' === M.overflowY || 'visible' === M.overflowY)))
            : ((E = y < _ && ('auto' === M.overflowX || 'scroll' === M.overflowX)), (S = w < x && ('auto' === M.overflowY || 'scroll' === M.overflowY)));
          var N = E && (Math.abs(b - r) <= l && O + y < _) - (Math.abs(m - r) <= l && !!O),
            P = S && (Math.abs(v - a) <= l && A + w < x) - (Math.abs(g - a) <= l && !!A);
          if (!Zt[d]) for (var k = 0; k <= d; k++) Zt[k] || (Zt[k] = {});
          (Zt[d].vx == N && Zt[d].vy == P && Zt[d].el === f) ||
            ((Zt[d].el = f),
            (Zt[d].vx = N),
            (Zt[d].vy = P),
            clearInterval(Zt[d].pid),
            (0 == N && 0 == P) ||
              ((u = !0),
              (Zt[d].pid = setInterval(
                function () {
                  o && 0 === this.layer && Yt.active._onTouchMove(qt);
                  var e = Zt[this.layer].vy ? Zt[this.layer].vy * s : 0,
                    n = Zt[this.layer].vx ? Zt[this.layer].vx * s : 0;
                  ('function' == typeof i && 'continue' !== i.call(Yt.dragged.parentNode[F], n, e, t, qt, Zt[this.layer].el)) || R(Zt[this.layer].el, n, e);
                }.bind({layer: d}),
                24,
              )))),
            d++;
        } while (e.bubbleScroll && h !== c && (h = I(h, !1)));
        Qt = u;
      }
    }, 30),
    ne = function (t) {
      var e = t.originalEvent,
        n = t.putSortable,
        o = t.dragEl,
        i = t.activeSortable,
        r = t.dispatchSortableEvent,
        a = t.hideGhostForTarget,
        l = t.unhideGhostForTarget;
      if (e) {
        var s = n || i;
        a();
        var c = e.changedTouches && e.changedTouches.length ? e.changedTouches[0] : e,
          u = document.elementFromPoint(c.clientX, c.clientY);
        l(), s && !s.el.contains(u) && (r('spill'), this.onSpill({dragEl: o, putSortable: n}));
      }
    };
  function oe() {}
  function ie() {}
  (oe.prototype = {
    startIndex: null,
    dragStart: function (t) {
      var e = t.oldDraggableIndex;
      this.startIndex = e;
    },
    onSpill: function (t) {
      var e = t.dragEl,
        n = t.putSortable;
      this.sortable.captureAnimationState(), n && n.captureAnimationState();
      var o = M(this.sortable.el, this.startIndex, this.options);
      o ? this.sortable.el.insertBefore(e, o) : this.sortable.el.appendChild(e), this.sortable.animateAll(), n && n.animateAll();
    },
    drop: ne,
  }),
    n(oe, {pluginName: 'revertOnSpill'}),
    (ie.prototype = {
      onSpill: function (t) {
        var e = t.dragEl,
          n = t.putSortable || this.sortable;
        n.captureAnimationState(), e.parentNode && e.parentNode.removeChild(e), n.animateAll();
      },
      drop: ne,
    }),
    n(ie, {pluginName: 'removeOnSpill'});
  var re,
    ae,
    le,
    se,
    ce,
    ue = [],
    de = [],
    he = !1,
    fe = !1,
    pe = !1;
  function ge(t, e) {
    de.forEach(function (n, o) {
      var i = e.children[n.sortableIndex + (t ? Number(o) : 0)];
      i ? e.insertBefore(n, i) : e.appendChild(n);
    });
  }
  function ve() {
    ue.forEach(function (t) {
      t !== le && t.parentNode && t.parentNode.removeChild(t);
    });
  }
  return (
    Yt.mount(
      new (function () {
        function t() {
          for (var t in ((this.defaults = {scroll: !0, scrollSensitivity: 30, scrollSpeed: 10, bubbleScroll: !0}), this))
            '_' === t.charAt(0) && 'function' == typeof this[t] && (this[t] = this[t].bind(this));
        }
        return (
          (t.prototype = {
            dragStarted: function (t) {
              var e = t.originalEvent;
              this.sortable.nativeDraggable
                ? p(document, 'dragover', this._handleAutoScroll)
                : this.options.supportPointer
                ? p(document, 'pointermove', this._handleFallbackAutoScroll)
                : e.touches
                ? p(document, 'touchmove', this._handleFallbackAutoScroll)
                : p(document, 'mousemove', this._handleFallbackAutoScroll);
            },
            dragOverCompleted: function (t) {
              var e = t.originalEvent;
              this.options.dragOverBubble || e.rootEl || this._handleAutoScroll(e);
            },
            drop: function () {
              this.sortable.nativeDraggable
                ? g(document, 'dragover', this._handleAutoScroll)
                : (g(document, 'pointermove', this._handleFallbackAutoScroll),
                  g(document, 'touchmove', this._handleFallbackAutoScroll),
                  g(document, 'mousemove', this._handleFallbackAutoScroll)),
                Jt(),
                $t(),
                clearTimeout(y),
                (y = void 0);
            },
            nulling: function () {
              (qt = zt = Wt = Qt = Vt = Gt = Ut = null), (Zt.length = 0);
            },
            _handleFallbackAutoScroll: function (t) {
              this._handleAutoScroll(t, !0);
            },
            _handleAutoScroll: function (t, e) {
              var n = this,
                o = (t.touches ? t.touches[0] : t).clientX,
                i = (t.touches ? t.touches[0] : t).clientY,
                r = document.elementFromPoint(o, i);
              if (((qt = t), e || s || l || u)) {
                ee(t, this.options, r, e);
                var a = I(r, !0);
                !Qt ||
                  (Vt && o === Gt && i === Ut) ||
                  (Vt && Jt(),
                  (Vt = setInterval(function () {
                    var r = I(document.elementFromPoint(o, i), !0);
                    r !== a && ((a = r), $t()), ee(t, n.options, r, e);
                  }, 10)),
                  (Gt = o),
                  (Ut = i));
              } else {
                if (!this.options.bubbleScroll || I(r, !0) === C()) return void $t();
                ee(t, this.options, I(r, !1), !1);
              }
            },
          }),
          n(t, {pluginName: 'scroll', initializeByDefault: !0})
        );
      })(),
    ),
    Yt.mount(ie, oe),
    Yt.mount(
      new (function () {
        function t() {
          this.defaults = {swapClass: 'sortable-swap-highlight'};
        }
        return (
          (t.prototype = {
            dragStart: function (t) {
              var e = t.dragEl;
              te = e;
            },
            dragOverValid: function (t) {
              var e = t.completed,
                n = t.target,
                o = t.onMove,
                i = t.activeSortable,
                r = t.changed,
                a = t.cancel;
              if (i.options.swap) {
                var l = this.sortable.el,
                  s = this.options;
                if (n && n !== l) {
                  var c = te;
                  !1 !== o(n) ? (E(n, s.swapClass, !0), (te = n)) : (te = null), c && c !== te && E(c, s.swapClass, !1);
                }
                r(), e(!0), a();
              }
            },
            drop: function (t) {
              var e,
                n,
                o,
                i,
                r,
                a,
                l = t.activeSortable,
                s = t.putSortable,
                c = t.dragEl,
                u = s || this.sortable,
                d = this.options;
              te && E(te, d.swapClass, !1),
                te &&
                  (d.swap || (s && s.options.swap)) &&
                  c !== te &&
                  (u.captureAnimationState(),
                  u !== l && l.captureAnimationState(),
                  (n = te),
                  (r = (e = c).parentNode),
                  (a = n.parentNode),
                  r &&
                    a &&
                    !r.isEqualNode(n) &&
                    !a.isEqualNode(e) &&
                    ((o = A(e)), (i = A(n)), r.isEqualNode(a) && o < i && i++, r.insertBefore(n, r.children[o]), a.insertBefore(e, a.children[i])),
                  u.animateAll(),
                  u !== l && l.animateAll());
            },
            nulling: function () {
              te = null;
            },
          }),
          n(t, {
            pluginName: 'swap',
            eventProperties: function () {
              return {swapItem: te};
            },
          })
        );
      })(),
    ),
    Yt.mount(
      new (function () {
        function t(t) {
          for (var e in this) '_' === e.charAt(0) && 'function' == typeof this[e] && (this[e] = this[e].bind(this));
          t.options.supportPointer
            ? p(document, 'pointerup', this._deselectMultiDrag)
            : (p(document, 'mouseup', this._deselectMultiDrag), p(document, 'touchend', this._deselectMultiDrag)),
            p(document, 'keydown', this._checkKeyDown),
            p(document, 'keyup', this._checkKeyUp),
            (this.defaults = {
              selectedClass: 'sortable-selected',
              multiDragKey: null,
              setData: function (e, n) {
                var o = '';
                ue.length && ae === t
                  ? ue.forEach(function (t, e) {
                      o += (e ? ', ' : '') + t.textContent;
                    })
                  : (o = n.textContent),
                  e.setData('Text', o);
              },
            });
        }
        return (
          (t.prototype = {
            multiDragKeyDown: !1,
            isMultiDrag: !1,
            delayStartGlobal: function (t) {
              var e = t.dragEl;
              le = e;
            },
            delayEnded: function () {
              this.isMultiDrag = ~ue.indexOf(le);
            },
            setupClone: function (t) {
              var e = t.sortable,
                n = t.cancel;
              if (this.isMultiDrag) {
                for (var o = 0; o < ue.length; o++)
                  de.push(X(ue[o])),
                    (de[o].sortableIndex = ue[o].sortableIndex),
                    (de[o].draggable = !1),
                    (de[o].style['will-change'] = ''),
                    E(de[o], this.options.selectedClass, !1),
                    ue[o] === le && E(de[o], this.options.chosenClass, !1);
                e._hideClone(), n();
              }
            },
            clone: function (t) {
              var e = t.sortable,
                n = t.rootEl,
                o = t.dispatchSortableEvent,
                i = t.cancel;
              this.isMultiDrag && (this.options.removeCloneOnHide || (ue.length && ae === e && (ge(!0, n), o('clone'), i())));
            },
            showClone: function (t) {
              var e = t.cloneNowShown,
                n = t.rootEl,
                o = t.cancel;
              this.isMultiDrag &&
                (ge(!1, n),
                de.forEach(function (t) {
                  D(t, 'display', '');
                }),
                e(),
                (ce = !1),
                o());
            },
            hideClone: function (t) {
              var e = this,
                n = (t.sortable, t.cloneNowHidden),
                o = t.cancel;
              this.isMultiDrag &&
                (de.forEach(function (t) {
                  D(t, 'display', 'none'), e.options.removeCloneOnHide && t.parentNode && t.parentNode.removeChild(t);
                }),
                n(),
                (ce = !0),
                o());
            },
            dragStartGlobal: function (t) {
              t.sortable,
                !this.isMultiDrag && ae && ae.multiDrag._deselectMultiDrag(),
                ue.forEach(function (t) {
                  t.sortableIndex = A(t);
                }),
                (ue = ue.sort(function (t, e) {
                  return t.sortableIndex - e.sortableIndex;
                })),
                (pe = !0);
            },
            dragStarted: function (t) {
              var e = this,
                n = t.sortable;
              if (this.isMultiDrag) {
                if (this.options.sort && (n.captureAnimationState(), this.options.animation)) {
                  ue.forEach(function (t) {
                    t !== le && D(t, 'position', 'absolute');
                  });
                  var o = T(le, !1, !0, !0);
                  ue.forEach(function (t) {
                    t !== le && Y(t, o);
                  }),
                    (fe = !0),
                    (he = !0);
                }
                n.animateAll(function () {
                  (fe = !1),
                    (he = !1),
                    e.options.animation &&
                      ue.forEach(function (t) {
                        B(t);
                      }),
                    e.options.sort && ve();
                });
              }
            },
            dragOver: function (t) {
              var e = t.target,
                n = t.completed,
                o = t.cancel;
              fe && ~ue.indexOf(e) && (n(!1), o());
            },
            revert: function (t) {
              var e = t.fromSortable,
                n = t.rootEl,
                o = t.sortable,
                i = t.dragRect;
              ue.length > 1 &&
                (ue.forEach(function (t) {
                  o.addAnimationState({target: t, rect: fe ? T(t) : i}), B(t), (t.fromRect = i), e.removeAnimationState(t);
                }),
                (fe = !1),
                (function (t, e) {
                  ue.forEach(function (n, o) {
                    var i = e.children[n.sortableIndex + (t ? Number(o) : 0)];
                    i ? e.insertBefore(n, i) : e.appendChild(n);
                  });
                })(!this.options.removeCloneOnHide, n));
            },
            dragOverCompleted: function (t) {
              var e = t.sortable,
                n = t.isOwner,
                o = t.insertion,
                i = t.activeSortable,
                r = t.parentEl,
                a = t.putSortable,
                l = this.options;
              if (o) {
                if ((n && i._hideClone(), (he = !1), l.animation && ue.length > 1 && (fe || (!n && !i.options.sort && !a)))) {
                  var s = T(le, !1, !0, !0);
                  ue.forEach(function (t) {
                    t !== le && (Y(t, s), r.appendChild(t));
                  }),
                    (fe = !0);
                }
                if (!n)
                  if ((fe || ve(), ue.length > 1)) {
                    var c = ce;
                    i._showClone(e),
                      i.options.animation &&
                        !ce &&
                        c &&
                        de.forEach(function (t) {
                          i.addAnimationState({target: t, rect: se}), (t.fromRect = se), (t.thisAnimationDuration = null);
                        });
                  } else i._showClone(e);
              }
            },
            dragOverAnimationCapture: function (t) {
              var e = t.dragRect,
                o = t.isOwner,
                i = t.activeSortable;
              if (
                (ue.forEach(function (t) {
                  t.thisAnimationDuration = null;
                }),
                i.options.animation && !o && i.multiDrag.isMultiDrag)
              ) {
                se = n({}, e);
                var r = S(le, !0);
                (se.top -= r.f), (se.left -= r.e);
              }
            },
            dragOverAnimationComplete: function () {
              fe && ((fe = !1), ve());
            },
            drop: function (t) {
              var e = t.originalEvent,
                n = t.rootEl,
                o = t.parentEl,
                i = t.sortable,
                r = t.dispatchSortableEvent,
                a = t.oldIndex,
                l = t.putSortable,
                s = l || this.sortable;
              if (e) {
                var c = this.options,
                  u = o.children;
                if (!pe)
                  if ((c.multiDragKey && !this.multiDragKeyDown && this._deselectMultiDrag(), E(le, c.selectedClass, !~ue.indexOf(le)), ~ue.indexOf(le)))
                    ue.splice(ue.indexOf(le), 1), (re = null), W({sortable: i, rootEl: n, name: 'deselect', targetEl: le, originalEvt: e});
                  else {
                    if ((ue.push(le), W({sortable: i, rootEl: n, name: 'select', targetEl: le, originalEvt: e}), e.shiftKey && re && i.el.contains(re))) {
                      var d,
                        h,
                        f = A(re),
                        p = A(le);
                      if (~f && ~p && f !== p)
                        for (p > f ? ((h = f), (d = p)) : ((h = p), (d = f + 1)); h < d; h++)
                          ~ue.indexOf(u[h]) ||
                            (E(u[h], c.selectedClass, !0), ue.push(u[h]), W({sortable: i, rootEl: n, name: 'select', targetEl: u[h], originalEvt: e}));
                    } else re = le;
                    ae = s;
                  }
                if (pe && this.isMultiDrag) {
                  if ((o[F].options.sort || o !== n) && ue.length > 1) {
                    var g = T(le),
                      v = A(le, ':not(.' + this.options.selectedClass + ')');
                    if (
                      (!he && c.animation && (le.thisAnimationDuration = null),
                      s.captureAnimationState(),
                      !he &&
                        (c.animation &&
                          ((le.fromRect = g),
                          ue.forEach(function (t) {
                            if (((t.thisAnimationDuration = null), t !== le)) {
                              var e = fe ? T(t) : g;
                              (t.fromRect = e), s.addAnimationState({target: t, rect: e});
                            }
                          })),
                        ve(),
                        ue.forEach(function (t) {
                          u[v] ? o.insertBefore(t, u[v]) : o.appendChild(t), v++;
                        }),
                        a === A(le)))
                    ) {
                      var m = !1;
                      ue.forEach(function (t) {
                        t.sortableIndex === A(t) || (m = !0);
                      }),
                        m && r('update');
                    }
                    ue.forEach(function (t) {
                      B(t);
                    }),
                      s.animateAll();
                  }
                  ae = s;
                }
                (n === o || (l && 'clone' !== l.lastPutMode)) &&
                  de.forEach(function (t) {
                    t.parentNode && t.parentNode.removeChild(t);
                  });
              }
            },
            nullingGlobal: function () {
              (this.isMultiDrag = pe = !1), (de.length = 0);
            },
            destroyGlobal: function () {
              this._deselectMultiDrag(),
                g(document, 'pointerup', this._deselectMultiDrag),
                g(document, 'mouseup', this._deselectMultiDrag),
                g(document, 'touchend', this._deselectMultiDrag),
                g(document, 'keydown', this._checkKeyDown),
                g(document, 'keyup', this._checkKeyUp);
            },
            _deselectMultiDrag: function (t) {
              if (!((void 0 !== pe && pe) || ae !== this.sortable || (t && b(t.target, this.options.draggable, this.sortable.el, !1)) || (t && 0 !== t.button)))
                for (; ue.length; ) {
                  var e = ue[0];
                  E(e, this.options.selectedClass, !1),
                    ue.shift(),
                    W({sortable: this.sortable, rootEl: this.sortable.el, name: 'deselect', targetEl: e, originalEvt: t});
                }
            },
            _checkKeyDown: function (t) {
              t.key === this.options.multiDragKey && (this.multiDragKeyDown = !0);
            },
            _checkKeyUp: function (t) {
              t.key === this.options.multiDragKey && (this.multiDragKeyDown = !1);
            },
          }),
          n(t, {
            pluginName: 'multiDrag',
            utils: {
              select: function (t) {
                var e = t.parentNode[F];
                e &&
                  e.options.multiDrag &&
                  !~ue.indexOf(t) &&
                  (ae && ae !== e && (ae.multiDrag._deselectMultiDrag(), (ae = e)), E(t, e.options.selectedClass, !0), ue.push(t));
              },
              deselect: function (t) {
                var e = t.parentNode[F],
                  n = ue.indexOf(t);
                e && e.options.multiDrag && ~n && (E(t, e.options.selectedClass, !1), ue.splice(n, 1));
              },
            },
            eventProperties: function () {
              var t = this,
                e = [],
                n = [];
              return (
                ue.forEach(function (o) {
                  var i;
                  e.push({multiDragElement: o, index: o.sortableIndex}),
                    (i = fe && o !== le ? -1 : fe ? A(o, ':not(.' + t.options.selectedClass + ')') : A(o)),
                    n.push({multiDragElement: o, index: i});
                }),
                {items: r(ue), clones: [].concat(de), oldIndicies: e, newIndicies: n}
              );
            },
            optionListeners: {
              multiDragKey: function (t) {
                return 'ctrl' === (t = t.toLowerCase()) ? (t = 'Control') : t.length > 1 && (t = t.charAt(0).toUpperCase() + t.substr(1)), t;
              },
            },
          })
        );
      })(),
    ),
    Yt
  );
});
