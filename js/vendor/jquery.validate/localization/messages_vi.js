(function (factory) {
  if (typeof define === 'function' && define.amd) {
    define(['jquery', '../jquery.validate'], factory);
  } else if (typeof module === 'object' && module.exports) {
    module.exports = factory(require('jquery'));
  } else {
    factory(jQuery);
  }
})(function ($) {
  /*
   * Translated default messages for the jQuery validation plugin.
   * Locale: VI (Vietnamese; Tiếng Việt)
   */
  $.extend($.validator.messages, {
    required: 'Hãy nhập.',
    remote: '<PERSON><PERSON><PERSON> sửa cho đúng.',
    email: 'Hãy nhập email.',
    url: '<PERSON><PERSON><PERSON> nhập URL.',
    date: 'Hãy nhập ngày.',
    dateISO: '<PERSON><PERSON><PERSON> nhập ngày (ISO).',
    number: '<PERSON><PERSON><PERSON> nhập số.',
    digits: '<PERSON><PERSON><PERSON> nhập chữ số.',
    creditcard: '<PERSON><PERSON><PERSON> nhập số thẻ tín dụng.',
    equalTo: '<PERSON><PERSON><PERSON> nhập thêm lần nữa.',
    extension: 'Phần mở rộng không đúng.',
    maxlength: $.validator.format('<PERSON><PERSON><PERSON> nhập từ {0} kí tự trở xuống.'),
    minlength: $.validator.format('Hãy nhập từ {0} kí tự trở lên.'),
    rangelength: $.validator.format('Hãy nhập từ {0} đến {1} kí tự.'),
    range: $.validator.format('Hãy nhập từ {0} đến {1}.'),
    max: $.validator.format('Hãy nhập từ {0} trở xuống.'),
    min: $.validator.format('Hãy nhập từ {0} trở lên.'),
  });
  return $;
});
