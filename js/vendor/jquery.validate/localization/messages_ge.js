(function (factory) {
  if (typeof define === 'function' && define.amd) {
    define(['jquery', '../jquery.validate'], factory);
  } else if (typeof module === 'object' && module.exports) {
    module.exports = factory(require('jquery'));
  } else {
    factory(jQuery);
  }
})(function ($) {
  /**
   * <AUTHOR> <<EMAIL>>
   * Translated default messages for the jQuery validation plugin.
   * Locale: GE (Georgian; ქართული)
   */
  $.extend($.validator.messages, {
    required: 'ეს ველი სავალდებულოა',
    remote: 'გთხოვთ შეასწოროთ.',
    email: 'გთხოვთ შეიყვანოთ სწორი ფორმატით.',
    url: 'გთხოვთ შეიყვანოთ სწორი ფორმატით.',
    date: 'გთხოვთ შეიყვანოთ სწორი თარიღი.',
    dateISO: 'გთხოვთ შეიყვანოთ სწორი ფორმატით (ISO).',
    number: 'გთხოვთ შეიყვანოთ რიცხვი.',
    digits: 'დაშვებულია მხოლოდ ციფრები.',
    creditcard: 'გთხოვთ შეიყვანოთ სწორი ფორმატის ბარათის კოდი.',
    equalTo: 'გთხოვთ შეიყვანოთ იგივე მნიშვნელობა.',
    maxlength: $.validator.format('გთხოვთ შეიყვანოთ არა უმეტეს {0} სიმბოლოსი.'),
    minlength: $.validator.format('შეიყვანეთ მინიმუმ {0} სიმბოლო.'),
    rangelength: $.validator.format('გთხოვთ შეიყვანოთ {0} -დან {1} -მდე რაოდენობის სიმბოლოები.'),
    range: $.validator.format('შეიყვანეთ {0} -სა {1} -ს შორის.'),
    max: $.validator.format('გთხოვთ შეიყვანოთ მნიშვნელობა ნაკლები ან ტოლი {0} -ს.'),
    min: $.validator.format('გთხოვთ შეიყვანოთ მნიშვნელობა მეტი ან ტოლი {0} -ს.'),
  });
  return $;
});
