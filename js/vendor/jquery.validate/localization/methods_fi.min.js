/*! jQuery Validation Plugin - v1.19.2 - 5/23/2020
 * https://jqueryvalidation.org/
 * Copyright (c) 2020 <PERSON><PERSON><PERSON>; Licensed MIT */
!(function (a) {
  'function' == typeof define && define.amd
    ? define(['jquery', '../jquery.validate.min'], a)
    : 'object' == typeof module && module.exports
    ? (module.exports = a(require('jquery')))
    : a(jQuery);
})(function (a) {
  return (
    a.extend(a.validator.methods, {
      date: function (a, b) {
        return this.optional(b) || /^\d{1,2}\.\d{1,2}\.\d{4}$/.test(a);
      },
      number: function (a, b) {
        return this.optional(b) || /^-?(?:\d+)(?:,\d+)?$/.test(a);
      },
    }),
    a
  );
});
