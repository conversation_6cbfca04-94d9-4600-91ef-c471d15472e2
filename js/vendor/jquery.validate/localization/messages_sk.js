(function (factory) {
  if (typeof define === 'function' && define.amd) {
    define(['jquery', '../jquery.validate'], factory);
  } else if (typeof module === 'object' && module.exports) {
    module.exports = factory(require('jquery'));
  } else {
    factory(jQuery);
  }
})(function ($) {
  /*
   * Translated default messages for the jQuery validation plugin.
   * Locale: SK (Slovak; slovenčina, slovenský jazyk)
   */
  $.extend($.validator.messages, {
    required: 'Povinné zadať.',
    maxlength: $.validator.format('Maximálne {0} znakov.'),
    minlength: $.validator.format('Minimálne {0} znakov.'),
    rangelength: $.validator.format('Minimálne {0} a maximálne {1} znakov.'),
    email: 'E-mailová adresa musí byť platná.',
    url: 'URL musí byť platná.',
    date: '<PERSON>s<PERSON> byť dátum.',
    number: '<PERSON><PERSON><PERSON> byť číslo.',
    digits: '<PERSON>ô<PERSON>e obsahovať iba číslice.',
    equalTo: 'Dve hodnoty sa musia rovnať.',
    range: $.validator.format('<PERSON>sí byť medzi {0} a {1}.'),
    max: $.validator.format('Nemôže byť viac ako {0}.'),
    min: $.validator.format('Nemôže byť menej ako {0}.'),
    creditcard: 'Číslo platobnej karty musí byť platné.',
    step: $.validator.format('Musí byť násobkom čísla {0}.'),
  });
  return $;
});
